import json
import time
import asyncio
import re
import nest_asyncio
from urllib.parse import urlparse
from playwright.async_api import async_playwright, <PERSON>
from typing import Optional, Dict
from .llm_interpreter import LLMInterpreter
from .dom_analyzer import DOMAnalyzer
from .report_generator import H<PERSON>LReportGenerator
from .smart_locator_finder import Smart<PERSON>oc<PERSON><PERSON><PERSON>
from .config import Config
import platform
import os

# Apply nest_asyncio to allow nested event loops
nest_asyncio.apply()

class TestExecutor:
    def __init__(self, steps, recorder, model_name="mixtral:8x7b", browser_type=None, base_url=None, report_generator=None):
        # Load configuration
        self.config = Config()

        # Use configuration values with fallbacks
        self.steps = steps
        self.recorder = recorder
        self.model_name = model_name or self.config.get('models.default')
        self.browser_type = browser_type or self.config.get('browser.type', 'chromium')  # Default to chromium if not specified
        self.base_url = base_url  # Store the base URL

        # Initialize components
        self.interpreter = LLMInterpreter(self.model_name)
        self.dom_analyzer = DOMAnalyzer()
        self.smart_locator_finder = SmartLocatorFinder()
        # AI modules removed - pattern matching mode only
        self.rag = None

        # Other initialization
        self.current_url = None
        self.domain = None
        self.collected_locators = {}
        self.step_locator_map = {}
        self.step_results = {}  # Track step results: {step_index: {'status': 'passed'|'failed'|'skipped', 'error': str}}
        
        # Use shared report generator if provided, otherwise create new one
        self.report_generator = report_generator or HTMLReportGenerator()
        if not report_generator:
            self.report_generator.start_test_run()

    async def run(self):
        """Run the test case."""
        try:
            # Prepare the test
            print("Preparing test...")
            await self._prepare_test()

            # Execute the test
            print("Executing test...")
            await self._run_async()  # Changed from _execute_test to _run_async which exists

            # Show test results summary
            self._show_test_results()

        except Exception as e:
            error_msg = f"Error during test execution: {str(e)}"
            print(error_msg)

            # Re-raise the exception
            raise

    def _inspect_locators(self):
        """Display the current locators and step mapping for inspection."""
        print("\n=== LOCATOR INSPECTION ===")
        print(f"Total collected locators: {len(self.collected_locators)}")
        print(f"Steps with mapped locators: {len(self.step_locator_map)} out of {len(self.steps)}")

        print("\n--- STEPS WITHOUT LOCATORS ---")
        for i, step in enumerate(self.steps):
            if i not in self.step_locator_map:
                print(f"Step {i+1}: {step}")

        print("\n--- STEP MAPPING ---")
        for step_index, mapping in sorted(self.step_locator_map.items()):
            print(f"Step {step_index+1}: {self.steps[step_index]}")
            print(f"  Action: {mapping.get('action', 'unknown')}")
            print(f"  Target: {mapping.get('target', 'unknown')}")
            if 'locator' in mapping and mapping['locator']:
                locator = mapping['locator']
                print(f"  Selector: {locator.get('selector', 'unknown')}")
                print(f"  Type: {locator.get('type', 'unknown')}")
                print(f"  Confidence: {locator.get('confidence', 0)}")
                if locator.get('ai_generated'):
                    print(f"  AI Generated: Yes")
                    if 'reasoning' in locator:
                        print(f"  Reasoning: {locator['reasoning']}")
            print()

        user_input = input("Press Enter to continue...")

    def _show_test_summary(self):
        """Show a summary of the test before execution."""
        print("\n=== TEST EXECUTION SUMMARY ===")
        print(f"Total steps: {len(self.steps)}")

        # Count steps with locators
        steps_with_locators = sum(1 for i in range(len(self.steps)) if i in self.step_locator_map)
        print(f"Steps with locators: {steps_with_locators}/{len(self.steps)}")

        # Show each step and its status
        print("\nStep details:")
        for i, step in enumerate(self.steps):
            if i in self.step_locator_map:
                print(f"  ✓ Step {i+1}: {step}")
            else:
                print(f"  ✗ Step {i+1}: {step} (No locator found)")

        print("\n=== END SUMMARY ===\n")

    def _show_test_results(self):
        """Show test execution results summary."""
        print("\n" + "="*60)
        print("🎯 TEST EXECUTION RESULTS SUMMARY")
        print("="*60)
        
        total_steps = len(self.steps)
        passed_steps = sum(1 for result in self.step_results.values() if result.get('status') == 'passed')
        failed_steps = sum(1 for result in self.step_results.values() if result.get('status') == 'failed')
        skipped_steps = sum(1 for result in self.step_results.values() if result.get('status') == 'skipped')
        
        print(f"📊 Total Steps: {total_steps}")
        print(f"✅ Passed: {passed_steps}")
        print(f"❌ Failed: {failed_steps}")
        print(f"⏭️  Skipped: {skipped_steps}")
        
        if total_steps > 0:
            success_rate = (passed_steps / total_steps) * 100
            print(f"📈 Success Rate: {success_rate:.1f}%")
        
        print("\n📝 Step Details:")
        print("-" * 60)
        
        for i, step in enumerate(self.steps):
            step_num = i + 1
            result = self.step_results.get(i, {'status': 'unknown'})
            status = result.get('status', 'unknown')
            
            if status == 'passed':
                print(f"✅ Step {step_num}: {step}")
            elif status == 'failed':
                error = result.get('error', 'Unknown error')
                print(f"❌ Step {step_num}: {step}")
                print(f"   💥 Error: {error}")
            elif status == 'skipped':
                print(f"⏭️  Step {step_num}: {step} (Skipped)")
            else:
                print(f"❓ Step {step_num}: {step} (Unknown status)")
        
        print("="*60)
        print("🏁 Test execution completed.\n")



    async def _prepare_test(self):
        """Prepare the test by analyzing steps and collecting locators.

        Returns:
            bool: True if all locators were found, False otherwise
        """
        # Step 1: Use the provided base URL or extract from test steps
        if self.base_url:
            base_url = self.base_url
            print(f"Using provided base URL: {base_url}")
        else:
            # Extract base URL from test steps as fallback
            base_url = self._extract_base_url()
            if not base_url:
                print("Error: Could not find base URL in test steps")
                return False

        self.current_url = base_url
        parsed_url = urlparse(base_url)
        self.domain = parsed_url.netloc
        print(f"Base URL identified: {base_url} (Domain: {self.domain})")

        # Step 2: Open base URL and collect locators
        print("Step 2: Opening base URL to collect locators...")
        locators_found = await self._collect_locators_from_url(base_url)
        if not locators_found:
            print("Error: Failed to collect locators from base URL")
            return False

        # Step 3: Match locators with test steps
        print("Step 3: Matching locators with test steps...")
        all_locators_found = await self._match_locators_with_steps()

        # Step 4: If not all locators found, try to find additional locators using pattern matching
        max_attempts = 3
        attempt = 0

        while not all_locators_found and attempt < max_attempts:
            attempt += 1
            print(f"Step 4: Not all locators found. Attempt {attempt}/{max_attempts} to find additional locators...")

            # Try to find additional locators using pattern matching
            print("Using pattern matching to find missing locators...")
            additional_locators_found = await self._find_additional_locators()

            if additional_locators_found:
                # Try matching again
                all_locators_found = await self._match_locators_with_steps()

            if all_locators_found:
                break

        if all_locators_found:
            print("All locators successfully matched with test steps!")
        else:
            print("Warning: Not all locators could be matched with test steps")

        return all_locators_found

    async def _find_additional_locators(self):
        """Use pattern matching to find locators for steps that don't have locators yet."""
        # Find steps that don't have locators
        missing_steps = [(i, step) for i, step in enumerate(self.steps)
                         if i not in self.step_locator_map]

        if not missing_steps:
            return False

        print(f"Using pattern matching to find locators for {len(missing_steps)} steps")

        # Open the page to analyze it
        async with async_playwright() as p:
            browser_module = getattr(p, self.browser_type)  # Use configured browser type
            browser = await browser_module.launch(headless=self.config.get('browser.headless', True))
            context = await browser.new_context()
            page = await context.new_page()

            try:
                # Navigate to the URL
                await page.goto(self.current_url, wait_until="networkidle")

                # Get the page content
                html_content = await page.content()

                # For each missing step, use pattern matching to find a locator
                locators_found = False

                for step_index, step in missing_steps:
                    print(f"Using pattern matching to find locator for step: {step}")

                    # Use simple pattern matching for Gherkin steps
                    step_lower = step.lower()
                    
                    # Navigation step - no locator needed
                    if "i am on" in step_lower or "i visit" in step_lower:
                        self.step_locator_map[step_index] = {
                            'action': 'navigate',
                            'url': self.base_url,
                            'locator': None
                        }
                        locators_found = True
                        continue
                    
                    # Click step - look for buttons/links
                    if "i click" in step_lower:
                        target_match = re.search(r'"([^"]+)"', step)
                        if target_match:
                            target = target_match.group(1)
                            # Look for matching button/link in collected locators
                            for locator_id, locator in self.collected_locators.items():
                                if (locator.get('type') in ['button', 'link'] and 
                                    target.lower() in locator.get('text', '').lower()):
                                    self.step_locator_map[step_index] = {
                                        'action': 'click',
                                        'target': target,
                                        'locator': locator
                                    }
                                    locators_found = True
                                    break
                    
                    # Type step - look for input fields
                    elif "i type" in step_lower:
                        value_match = re.search(r'"([^"]+)"', step)
                        if value_match:
                            value = value_match.group(1)
                            # Look for search input in collected locators
                            for locator_id, locator in self.collected_locators.items():
                                if (locator.get('type') == 'input' and 
                                    'search' in locator.get('name', '').lower()):
                                    self.step_locator_map[step_index] = {
                                        'action': 'type',
                                        'target': 'search field',
                                        'value': value,
                                        'locator': locator
                                    }
                                    locators_found = True
                                    break
                    
                    # Assert step - look for text content
                    elif "i should see" in step_lower:
                        target_match = re.search(r'"([^"]+)"', step)
                        if target_match:
                            target = target_match.group(1)
                            # Look for any element containing the text
                            for locator_id, locator in self.collected_locators.items():
                                if target.lower() in locator.get('text', '').lower():
                                    self.step_locator_map[step_index] = {
                                        'action': 'assert',
                                        'target': target,
                                        'locator': locator
                                    }
                                    locators_found = True
                                    break

                return locators_found

            finally:
                await context.close()
                await browser.close()

        return locators_found

    def _interpret_gherkin_step(self, step: str) -> Optional[Dict[str, str]]:
        """Interpret a Gherkin step using simple pattern matching."""
        step_lower = step.lower()
        
        # Navigation patterns
        if "i am on" in step_lower or "i visit" in step_lower:
            return {
                'action_type': 'navigate',
                'target_description': 'main page',
                'value': ''
            }
        
        # Click patterns
        if "i click" in step_lower:
            # Extract the target from quotes
            target_match = re.search(r'"([^"]+)"', step)
            if target_match:
                target = target_match.group(1)
                return {
                    'action_type': 'click',
                    'target_description': target,
                    'value': ''
                }
        
        # Type patterns
        if "i type" in step_lower:
            # Extract value and field
            value_match = re.search(r'"([^"]+)"', step)
            if value_match:
                value = value_match.group(1)
                # Determine field type
                if "search" in step_lower:
                    target = "search field"
                else:
                    target = "input field"
                return {
                    'action_type': 'type',
                    'target_description': target,
                    'value': value
                }
        
        # Assertion patterns
        if "i should see" in step_lower:
            target_match = re.search(r'"([^"]+)"', step)
            if target_match:
                target = target_match.group(1)
                return {
                    'action_type': 'assert',
                    'target_description': target,
                    'value': ''
                }
        
        # Scroll patterns
        if "i scroll" in step_lower:
            target_match = re.search(r'"([^"]+)"', step)
            if target_match:
                target = target_match.group(1)
                return {
                    'action_type': 'scroll',
                    'target_description': target,
                    'value': ''
                }
        
        return None

    def _extract_base_url(self):
        """Extract the base URL from the test steps."""
        # Look for a URL in the first step
        if self.steps and len(self.steps) > 0:
            first_step = self.steps[0].lower()
            if "buka halaman" in first_step or "go to" in first_step:
                # Extract URL using regex
                url_match = re.search(r'https?://[^\s"\']+', first_step)
                if url_match:
                    return url_match.group(0)

        return None

    async def _collect_locators_from_url(self, url):
        """Collect locators from a URL by opening it in a headless browser."""
        async with async_playwright() as p:
            browser = await p.chromium.launch(headless=self.config.get('browser.headless', True))
            context = await browser.new_context()
            page = await context.new_page()

            try:
                # Navigate to the URL
                await page.goto(url, wait_until="networkidle")

                # Get the page content
                html_content = await page.content()

                # Extract locators from the HTML content
                locators = self.dom_analyzer.extract_all_locators(html_content)

                # Store the locators
                self.collected_locators.update(locators)

                print(f"Collected {len(locators)} locators from {url}")

                # Add special search field locators - expanded list
                search_selectors = [
                    'input[type="search"]',
                    'input[name="q"]',
                    'input[name="search"]',
                    'input[name="s"]',
                    'input[placeholder*="search" i]',
                    'input[placeholder*="cari" i]',
                    'input[aria-label*="search" i]',
                    'input[aria-label*="cari" i]',
                    '.search-input',
                    '.search-field',
                    '#search',
                    '#searchbox',
                    'input.form-control',
                    'input[type="text"]',  # Added generic text input
                    '.form-control',       # Added Bootstrap form control
                    'input[role="searchbox"]',
                    'input[id*="search" i]',
                    'input[id*="cari" i]',
                    'input[class*="search" i]',
                    'input[class*="cari" i]'
                ]

                # Try to find search input with more robust approach
                for selector in search_selectors:
                    try:
                        search_input = await page.query_selector(selector)
                        if search_input:
                            # Check if it's visible
                            is_visible = await search_input.is_visible()
                            if is_visible:
                                print(f"Found search input with selector: {selector}")
                                self.collected_locators['search_input'] = {
                                    'type': 'input',
                                    'selector': selector,
                                    'confidence': 0.9
                                }
                                break
                    except Exception as e:
                        print(f"Error checking selector {selector}: {e}")

                # If no search input found, try to find by clicking common search icons
                if 'search_input' not in self.collected_locators:
                    search_icon_selectors = [
                        'button.search-toggle',
                        '.search-icon',
                        'i.fa-search',
                        'svg[class*="search"]',
                        'button[aria-label*="search" i]',
                        'button[aria-label*="cari" i]'
                    ]

                    for selector in search_icon_selectors:
                        try:
                            search_icon = await page.query_selector(selector)
                            if search_icon and await search_icon.is_visible():
                                print(f"Found search icon with selector: {selector}")
                                await search_icon.click()
                                await page.wait_for_timeout(1000)  # Wait for search field to appear

                                # Now try to find the search input again
                                for search_selector in search_selectors:
                                    search_input = await page.query_selector(search_selector)
                                    if search_input and await search_input.is_visible():
                                        print(f"Found search input after clicking icon: {search_selector}")
                                        self.collected_locators['search_input'] = {
                                            'type': 'input',
                                            'selector': search_selector,
                                            'confidence': 0.9,
                                            'requires_icon_click': True,
                                            'icon_selector': selector
                                        }
                                        break
                        except Exception as e:
                            print(f"Error with search icon selector {selector}: {e}")

                # Add special search button locators
                search_button_selectors = [
                    'button[type="submit"]',
                    'input[type="submit"]',
                    '.search-button',
                    '.search-submit',
                    'button.btn-search',
                    'button:has(.search-icon)',
                    'button:has(svg)',
                    'button.inline-flex'
                ]

                self.collected_locators['search_button'] = {
                    'type': 'button',
                    'selector': search_button_selectors[0],
                    'element_type': 'button',
                    'confidence': 0.8,
                    'search_button_selectors': search_button_selectors
                }

                return True
            except Exception as e:
                print(f"Error collecting locators from {url}: {e}")
                return False
            finally:
                await context.close()
                await browser.close()

    async def _match_locators_with_steps(self):
        """Match collected locators with test steps."""
        all_locators_found = True
        navigation_detected = False

        for i, step in enumerate(self.steps):
            # Detect navigation patterns that will change the page
            is_navigation_step = self._is_navigation_step(step)
            
            # If we've already detected a navigation step, skip locator analysis for subsequent steps
            if navigation_detected:
                print(f"Step {i + 1} skipped for pre-analysis (navigation detected in previous step): {step}")
                self.step_locator_map[i] = {
                    'action': 'skip_pre_analysis',
                    'target': '',
                    'value': '',
                    'locator': None,
                    'requires_reanalysis': True
                }
                continue
            
            # Skip the first step if it's just navigation
            if i == 0 and ("buka halaman" in step.lower() or "go to" in step.lower()):
                self.step_locator_map[i] = {
                    'action': 'navigate',
                    'url': self.current_url,
                    'locator': None,
                    'requires_reanalysis': False
                }
                continue

            # Skip the first step if it's just navigation
            if i == 0 and ("i am on" in step.lower() or "i visit" in step.lower()):
                self.step_locator_map[i] = {
                    'action': 'navigate',
                    'url': self.current_url,
                    'locator': None,
                    'requires_reanalysis': False
                }
                print(f"Direct pattern match: navigate to main page")
                continue

            # Special handling for search fields
            if "i type" in step.lower() and "search" in step.lower():
                # Check if search_input locator exists
                if 'search_input' in self.collected_locators:
                    value_match = re.search(r'"([^"]+)"', step)
                    value = value_match.group(1) if value_match else ""
                    self.step_locator_map[i] = {
                        'action': 'type',
                        'target': 'search field',
                        'value': value,
                        'locator': self.collected_locators['search_input'],
                        'requires_reanalysis': False
                    }
                    print(f"Direct pattern match: type '{value}' into field 'search field'")
                else:
                    print(f"Warning: Search input locator not found for step {i + 1}: {step}")
                    all_locators_found = False
                continue

            # Special handling for search buttons
            if "i click" in step.lower() and "cari" in step.lower():
                # Check if search_button locator exists
                if 'search_button' in self.collected_locators:
                    self.step_locator_map[i] = {
                        'action': 'click',
                        'target': 'Cari',
                        'value': '',
                        'locator': self.collected_locators['search_button'],
                        'requires_reanalysis': False
                    }
                    print(f"Direct pattern match: click on 'Cari'")
                else:
                    print(f"Warning: Search button locator not found for step {i + 1}: {step}")
                    all_locators_found = False
                continue

            # Special handling for assertions
            if "i should see" in step.lower():
                target_match = re.search(r'"([^"]+)"', step)
                if target_match:
                    target = target_match.group(1)
                    # Look for any element containing the text
                    for locator_id, locator in self.collected_locators.items():
                        if target.lower() in locator.get('text', '').lower():
                            self.step_locator_map[i] = {
                                'action': 'assert',
                                'target': target,
                                'locator': locator,
                                'requires_reanalysis': False
                            }
                            print(f"Direct pattern match: assert text '{target}'")
                            break
                    else:
                        print(f"Warning: No matching locator found for assertion step {i + 1}: {step}")
                        all_locators_found = False
                continue

            # Use the original working approach with interpreter
            action_plan = self._interpret_gherkin_step(step)
            if not action_plan:
                print(f"Warning: Could not interpret step {i+1}: {step}")
                all_locators_found = False
                continue

            action_type = action_plan.get('action_type', '')
            target = action_plan.get('target_description', '')
            value = action_plan.get('value', '')

            print(f"Action plan for step {i + 1}: {action_type} | Target: {target} | Value: {value}")

            # Special handling for search fields
            if action_type == 'type' and ('search' in target.lower() or 'cari' in target.lower() or 'pencarian' in target.lower()):
                # Check if search_input locator exists
                if 'search_input' in self.collected_locators:
                    self.step_locator_map[i] = {
                        'action': action_type,
                        'target': target,
                        'value': value,
                        'locator': self.collected_locators['search_input'],
                        'requires_reanalysis': False
                    }
                    print(f"Direct pattern match: {action_type} '{value}' into field '{target}'")
                else:
                    print(f"Warning: Search input locator not found for step {i + 1}: {step}")
                    all_locators_found = False
                continue

            # Special handling for search buttons
            if action_type == 'click' and ('search' in target.lower() or 'cari' in target.lower()):
                # Check if search_button locator exists
                if 'search_button' in self.collected_locators:
                    self.step_locator_map[i] = {
                        'action': action_type,
                        'target': target,
                        'value': value,
                        'locator': self.collected_locators['search_button'],
                        'requires_reanalysis': False
                    }
                    print(f"Direct pattern match: {action_type} on '{target}'")
                else:
                    print(f"Warning: Search button locator not found for step {i + 1}: {step}")
                    all_locators_found = False
                continue

            # Special handling for scroll actions
            if action_type == 'scroll':
                # For scroll actions, we don't need a specific locator initially
                # The scroll will be handled during execution
                self.step_locator_map[i] = {
                    'action': action_type,
                    'target': target,
                    'value': value,
                    'locator': None,  # No specific locator needed for scroll
                    'requires_reanalysis': False
                }

                print(f"Direct pattern match: {action_type} to {target or 'down'}")
                continue

            # Find a matching locator
            locator = self._find_matching_locator(action_type, target)

            if locator:
                self.step_locator_map[i] = {
                    'action': action_type,
                    'target': target,
                    'value': value,
                    'locator': locator,
                    'requires_reanalysis': is_navigation_step
                }
                
                # If this is a navigation step, mark that navigation has been detected
                if is_navigation_step:
                    navigation_detected = True
                    print(f"Navigation step detected at step {i + 1}: {step}")
                    print("Subsequent steps will be analyzed after navigation")

                if action_type == 'click':
                    print(f"Direct pattern match: {action_type} on '{target}'")
                elif action_type == 'assert':
                    print(f"Direct pattern match: {action_type} text '{target}'")
                else:
                    print(f"Direct pattern match: {action_type} '{target}'")
            else:
                print(f"Warning: No matching locator found for step {i + 1}: {step}")
                # Mark navigation steps for re-analysis even if no locator found initially
                if is_navigation_step:
                    self.step_locator_map[i] = {
                        'action': action_type,
                        'target': target,
                        'value': value,
                        'locator': None,
                        'requires_reanalysis': True
                    }
                    print(f"Step {i + 1} marked for re-analysis after navigation: {step}")
                    navigation_detected = True
                    print(f"Navigation step detected at step {i + 1}: {step}")
                    print("Subsequent steps will be analyzed after navigation")
                all_locators_found = False



            # Find a matching locator
            locator = self._find_matching_locator(action_type, target)

            if locator:
                self.step_locator_map[i] = {
                    'action': action_type,
                    'target': target,
                    'value': value,
                    'locator': locator,
                    'requires_reanalysis': is_navigation_step
                }
                
                # If this is a navigation step, mark that navigation has been detected
                if is_navigation_step:
                    navigation_detected = True
                    print(f"Navigation step detected at step {i + 1}: {step}")
                    print("Subsequent steps will be analyzed after navigation")

                if action_type == 'click':
                    print(f"Direct pattern match: {action_type} on '{target}'")
                elif action_type == 'assert':
                    print(f"Direct pattern match: {action_type} text '{target}'")
                else:
                    print(f"Direct pattern match: {action_type} '{target}'")
            else:
                print(f"Warning: No matching locator found for step {i + 1}: {step}")
                # Mark navigation steps for re-analysis even if no locator found initially
                if is_navigation_step:
                    self.step_locator_map[i] = {
                        'action': action_type,
                        'target': target,
                        'value': value,
                        'locator': None,
                        'requires_reanalysis': True
                    }
                    print(f"Step {i + 1} marked for re-analysis after navigation: {step}")
                    navigation_detected = True
                    print(f"Navigation step detected at step {i + 1}: {step}")
                    print("Subsequent steps will be analyzed after navigation")
                all_locators_found = False

        return all_locators_found

    def _is_navigation_step(self, step):
        """
        Detect if a step will cause navigation to a different page.
        
        Args:
            step (str): The test step text
            
        Returns:
            bool: True if the step will cause navigation, False otherwise
        """
        step_lower = step.lower()
        
        # Navigation patterns that indicate page changes
        navigation_patterns = [
            # Indonesian patterns
            "akan berpindah ke halaman",
            "akan berpindah ke halaman berikutnya",
            "akan berpindah ke halaman sebelumnya", 
            "akan pindah ke halaman",
            "akan pindah ke halaman berikutnya",
            "akan pindah ke halaman sebelumnya",
            "berpindah ke halaman",
            "pindah ke halaman",
            "masuk ke halaman",
            "klik untuk masuk",
            "klik untuk pindah",
            "klik untuk berpindah",
            
            # English patterns
            "will navigate to",
            "will go to",
            "will move to",
            "will switch to",
            "will redirect to",
            "navigate to",
            "go to page",
            "move to page",
            "switch to page",
            "redirect to",
            
            # Common navigation actions
            "klik link",
            "klik tombol",
            "klik menu",
            "klik navigasi",
            "click link",
            "click button",
            "click menu",
            "click navigation",
            
            # Specific navigation indicators
            "masuk ke ruang",
            "enter room",
            "masuk ke menu",
            "enter menu",
            "pilih menu",
            "select menu"
        ]
        
        # Check if step contains any navigation patterns
        for pattern in navigation_patterns:
            if pattern in step_lower:
                return True
                
        # Check for specific element types that typically cause navigation
        navigation_elements = [
            "link", "a href", "button", "menu", "tab", "navigation"
        ]
        
        for element in navigation_elements:
            if element in step_lower:
                # Additional context check to avoid false positives
                if any(word in step_lower for word in ["klik", "click", "pilih", "select", "pindah", "move"]):
                    return True
                    
        return False

    def _find_matching_locator(self, action_type, target):
        """Find a matching locator for a step."""
        # Normalize target for better matching
        normalized_target = target.lower().strip()

        # For click actions, prioritize exact text matches for buttons
        if action_type == 'click':
            # First, try to find exact text matches in buttons
            for locator_id, locator in self.collected_locators.items():
                locator_text = locator.get('text', '').lower().strip()
                locator_type = locator.get('type', '').lower()
                # Check for exact text match in buttons
                if (locator_type == 'button' and 
                    locator_text == normalized_target):
                    print(f"Found exact button match: {locator_text} for target: {normalized_target}")
                    return locator
            # Then try partial matches in buttons
            for locator_id, locator in self.collected_locators.items():
                locator_text = locator.get('text', '').lower().strip()
                locator_type = locator.get('type', '').lower()
                if (locator_type == 'button' and 
                    normalized_target in locator_text):
                    print(f"Found partial button match: {locator_text} for target: {normalized_target}")
                    return locator
            # Finally try other clickable elements
            for locator_id, locator in self.collected_locators.items():
                locator_text = locator.get('text', '').lower().strip()
                locator_type = locator.get('type', '').lower()
                locator_id_lower = locator_id.lower()
                if (locator_type in ['link', 'a'] and
                    (normalized_target in locator_text or
                     normalized_target in locator_id_lower)):
                    return locator
        # For type actions, look for inputs, textareas, or elements with matching labels
        elif action_type == 'type':
            for locator_id, locator in self.collected_locators.items():
                locator_text = locator.get('text', '').lower().strip()
                locator_type = locator.get('type', '').lower()
                locator_id_lower = locator_id.lower()
                if (locator_type in ['input', 'textarea'] and
                    (normalized_target in locator_text or
                     normalized_target in locator_id_lower)):
                    return locator
        # For assert actions, any element with matching text will do
        elif action_type == 'assert':
            for locator_id, locator in self.collected_locators.items():
                locator_text = locator.get('text', '').lower().strip()
                if normalized_target in locator_text:
                    return locator
        return None

    async def _find_additional_locators(self):
        """Find additional locators by exploring the page further."""
        try:
            async with async_playwright() as p:
                browser = await p.chromium.launch(headless=self.config.get('browser.headless', True))
                context = await browser.new_context()
                page = await context.new_page()

                # Navigate to the base URL
                await page.goto(self.current_url, wait_until="networkidle")

                # Click on navigation elements to explore more pages
                nav_elements = await page.query_selector_all('nav a, .nav a, .menu a, .navigation a')

                initial_locator_count = len(self.collected_locators)

                for nav_element in nav_elements[:5]:  # Limit to first 5 to avoid too much exploration
                    try:
                        # Get the URL before clicking
                        current_url = page.url

                        # Click the navigation element
                        await nav_element.click()
                        await page.wait_for_load_state("networkidle")

                        # If we navigated to a new page, collect locators
                        if page.url != current_url:
                            html_content = await page.content()
                            new_locators = self.dom_analyzer.extract_all_locators(html_content)

                            # Add new locators to our collection
                            for locator_id, locator in new_locators.items():
                                if locator_id not in self.collected_locators:
                                    self.collected_locators[locator_id] = locator

                            # Go back to the previous page
                            await page.go_back()
                            await page.wait_for_load_state("networkidle")
                    except Exception as e:
                        print(f"Error exploring navigation element: {e}")

                await browser.close()

                # Return True if we found additional locators
                return len(self.collected_locators) > initial_locator_count

        except Exception as e:
            print(f"Error finding additional locators: {e}")
            return False

    async def _run_async(self):
        """Execute the test case steps asynchronously."""
        async with async_playwright() as p:
            # Determine browser launch arguments based on OS
            launch_args = [
                "--start-maximized",
                "--high-dpi-support=1",
                "--force-device-scale-factor=1",
                "--disable-gpu-vsync",
            ]

            # Add OS-specific arguments
            if platform.system() == "Windows":
                launch_args.extend([
                    "--window-position=0,0",
                ])
            elif platform.system() == "Darwin":  # macOS
                # macOS specific arguments
                launch_args.extend([
                    "--window-position=0,0",
                    "--full-screen",  # Try to use full screen on macOS
                ])
            elif platform.system() == "Linux":
                launch_args.extend([
                    "--window-position=0,0",
                    "--kiosk",  # Full screen mode on Linux
                ])

            # Configure browser for high-quality recording with maximized window
            browser = await getattr(p, self.browser_type).launch(
                headless=self.config.get('browser.headless', True),
                args=launch_args
            )

            # Get the screen dimensions more accurately
            screen_width = 1920  # Default fallback
            screen_height = 1080  # Default fallback

            try:
                # Create a temporary page to get screen dimensions
                context_temp = await browser.new_context()
                screen_info = await context_temp.new_page()

                # More comprehensive screen detection
                dimensions = await screen_info.evaluate("""() => {
                    return {
                        // Try multiple methods to get the most accurate screen size
                        width: Math.max(
                            window.screen.width,
                            window.screen.availWidth,
                            window.outerWidth || 0
                        ),
                        height: Math.max(
                            window.screen.height,
                            window.screen.availHeight,
                            window.outerHeight || 0
                        )
                    }
                }""")

                await screen_info.close()
                await context_temp.close()

                screen_width = dimensions['width']
                screen_height = dimensions['height']
            except Exception as e:
                print(f"Could not detect screen dimensions, using defaults: {e}")

            # Create a context with the detected screen size (no video recording)
            context = await browser.new_context(
                viewport={"width": screen_width, "height": screen_height},
                no_viewport=False
            )

            page = await context.new_page()

            # Ensure the window is maximized
            await self._ensure_window_maximized(page)

            try:
                # First, navigate to the base URL
                if self.base_url:
                    print(f"Navigating to base URL: {self.base_url}")
                    await page.goto(self.base_url, wait_until="networkidle")

                    # Try to maximize again after page load
                    await self._ensure_window_maximized(page)

                    await asyncio.sleep(2)  # Wait for page to load

                # Execute each step
                for i, step in enumerate(self.steps):
                    step_num = i + 1
                    step_description = f"Step {step_num}: {step}"

                    # Use Playwright's step API to make steps visible in execution
                    async with page.expect_console_message() as _:
                        await page.evaluate(f"console.log('Executing {step_description}')")

                    print(step_description)

                    # Execute the step within a Playwright step context
                    success = await self._execute_step(page, step, i)

                    # If step execution failed, give options to retry, skip, or abort
                    if not success:
                        print(f"Step {step_num} execution failed: {step}")

                        # Pause to let user see the current state
                        print("Pausing for 5 seconds to allow inspection...")
                        await asyncio.sleep(5)

                        # Auto-retry failed steps
                        print(f"Auto-retrying step {step_num}...")
                        # Try to analyze the current page for locators
                        html_content = await page.content()
                        self.current_url = page.url
                        print(f"Analyzing current page: {page.url}")

                        # Extract all locators from current page
                        new_locators = self.dom_analyzer.extract_all_locators(html_content)

                        # Add new locators to our collection
                        for locator_id, locator in new_locators.items():
                            if locator_id not in self.collected_locators:
                                self.collected_locators[locator_id] = locator

                        # Try to find a locator for this step
                        locator_found = await self._find_locator_for_step_on_current_page(page, i, step, html_content)

                        if locator_found:
                            print(f"Found new locator for step {step_num} on current page")
                            # Retry the step
                            success = await self._execute_step(page, step, i)
                            if not success:
                                print(f"Step {step_num} still failed after retry, skipping...")
                                self.step_results[i] = {'status': 'skipped', 'error': 'Failed after retry'}
                                continue
                        else:
                            print(f"Could not find locator for step {step_num} on current page, skipping...")
                            self.step_results[i] = {'status': 'skipped', 'error': 'No locator found'}
                            continue

            except Exception as e:
                error_msg = f"Error during test execution: {str(e)}"
                print(error_msg)

                # Error occurred during test execution
                print(f"Test execution error: {str(e)}")

            finally:
                # Clean up browser resources
                await context.close()
                await browser.close()

    async def _execute_step(self, page, step, step_index):
        """Execute a single test step with intelligent error handling and retry mechanisms."""
        try:
            # Clean the step text
            clean_step = step.strip()

            # Get configuration
            config = Config()
            element_wait_timeout = config.get('timeouts.element_wait', 5000)

            # Execute the step
            print(f"Executing step {step_index + 1}: {clean_step}")

            # Brief pause before executing action (reduced for performance)
            await asyncio.sleep(0.5)

            # Check if we have a pre-analyzed action for this step
            step_info = self.step_locator_map.get(step_index)
            locator = None

            # Prepare action plan
            if step_info:
                action_plan = {
                    'action_type': step_info.get('action', ''),
                    'target_description': step_info.get('target', ''),
                    'value': step_info.get('value', ''),
                    'url': step_info.get('url', '')
                }
                locator = step_info.get('locator')
                requires_reanalysis = step_info.get('requires_reanalysis', False)
                
                if requires_reanalysis:
                    print(f"Step {step_index + 1} is marked for re-analysis after navigation")
            else:
                # Try to interpret the step if no pre-analysis was done
                action_plan = self._interpret_gherkin_step(step)
                if not action_plan:
                    print(f"Could not interpret step: {step}")
                    self.step_results[step_index] = {'status': 'failed', 'error': f'Could not interpret step: {step}'}
                    return False
                locator = None
                requires_reanalysis = False

            print(f"Using action plan: {action_plan}")

            # Execute the action with intelligent retry mechanism
            success = await self._execute_action_with_retry(page, action_plan, step_index, locator)

            if success:
                self.step_results[step_index] = {'status': 'passed', 'error': None}
                return True
            else:
                # If action failed, try to find locator on current page
                print(f"Step {step_index + 1} execution failed, trying to find locator on current page...")
                
                # Get current page HTML for analysis
                html_content = await page.content()
                
                # Try to find locator on current page
                locator_found = await self._find_locator_for_step_on_current_page(page, step_index, step, html_content)
                
                if locator_found:
                    # Retry with new locator
                    print(f"Found locator on current page, retrying step {step_index + 1}...")
                    success = await self._execute_action_with_retry(page, action_plan, step_index, locator)
                    
                    if success:
                        self.step_results[step_index] = {'status': 'passed', 'error': None}
                        return True
                
                # If still failed, mark as skipped with error details
                error_msg = f"Step execution failed: {action_plan.get('action_type', 'unknown')} '{action_plan.get('target_description', 'unknown')}'"
                self.step_results[step_index] = {'status': 'skipped', 'error': error_msg}
                print(f"Step {step_index + 1} execution failed: {error_msg}")
                return False

        except Exception as e:
            error_msg = f"Error executing step {step_index + 1}: {str(e)}"
            print(error_msg)
            self.step_results[step_index] = {'status': 'failed', 'error': error_msg}
            return False

    async def _execute_action_with_retry(self, page, action_plan, step_index, locator=None):
        """Execute action with intelligent retry mechanism."""
        max_retries = 3
        
        for attempt in range(max_retries):
            try:
                # Execute the action
                success = await self._execute_action(page, action_plan, step_index)
                
                if success:
                    return True
                
                # If failed and not the last attempt, wait and retry
                if attempt < max_retries - 1:
                    print(f"⚠️ Action failed, retrying... Attempt {attempt + 2}/{max_retries}")
                    await asyncio.sleep(1)
                    
                    # Try to refresh the page if it's a navigation issue
                    if action_plan.get('action_type') in ['click', 'assert']:
                        try:
                            await page.reload(wait_until='networkidle', timeout=10000)
                            await asyncio.sleep(2)
                        except:
                            pass
                    
            except Exception as e:
                print(f"⚠️ Action error on attempt {attempt + 1}: {e}")
                if attempt < max_retries - 1:
                    await asyncio.sleep(1)
                    continue
        
                return False
                selectors = [
                        f"[role='button']:has-text('{target}')",
                        f"div:has-text('{target}')",
                        f"img[alt*='{target}' i]",
                        f":has(img[alt*='{target}' i])",
                        f"[onclick]:has-text('{target}')",
                        f"[style*='cursor: pointer']:has-text('{target}')",
                        f"*:has-text('{target}')"  # Fallback to any element with text
                    ]
                    
                    for selector in selectors_to_try:
                        try:
                            print(f"Trying selector for clickable text: {selector}")
                            await page.hover(selector)
                            await page.click(selector)
                            print(f"Clicked text element using selector: {selector}")
                            return True
                        except Exception as e:
                            print(f"Failed with selector {selector}: {e}")
                            continue
                    
                    # If all selectors fail, try JavaScript-based search
                    try:
                        print("Trying JavaScript-based search for clickable text element...")
                        js_result = await page.evaluate(f"""() => {{
                            // Find all elements containing the target text
                            const elements = document.querySelectorAll('*');
                            for (let element of elements) {{
                                const text = element.textContent || element.innerText || '';
                                if (text.toLowerCase().includes('{target.lower()}')) {{
                                    // Check if element or its parent is clickable
                                    let clickableElement = element;
                                    if (element.tagName === 'IMG') {{
                                        // For images, look for clickable parent
                                        clickableElement = element.closest('a, button, [onclick], [role="button"]') || element.parentElement;
                                    }}
                                    
                                    if (clickableElement && (
                                        clickableElement.tagName === 'A' || 
                                        clickableElement.tagName === 'BUTTON' ||
                                        clickableElement.onclick ||
                                        clickableElement.getAttribute('role') === 'button' ||
                                        clickableElement.style.cursor === 'pointer'
                                    )) {{
                                        return {{
                                            found: true,
                                            tagName: clickableElement.tagName,
                                            classes: Array.from(clickableElement.classList).join(' '),
                                            text: text.trim()
                                        }};
                                    }}
                                }}
                            }}
                            return {{ found: false }};
                        }}""")
                        
                        if js_result.get('found'):
                            print(f"Found clickable element via JavaScript: {js_result}")
                            # Try to click using the found element's properties
                            if js_result.get('classes'):
                                classes = js_result['classes'].split(' ')
                                for cls in classes:
                                    if cls and len(cls) > 2:
                                        try:
                                            await page.click(f"{js_result['tagName'].lower()}.{cls}")
                                            print(f"Clicked element using class: {cls}")
                                            return True
                                        except:
                                            continue
                    except Exception as e:
                        print(f"JavaScript-based search failed: {e}")
                    
                    print(f"Error during click action: Could not find clickable element for '{target}'")
                    return False

            # Execute the action with the pre-analyzed locator
            if action_plan.get('action_type') == 'skip_pre_analysis':
                print(f"Step {step_index + 1} requires re-analysis on current page")
                # Find locator for this step on current page
                html_content = await page.content()
                locator_found = await self._find_locator_for_step_on_current_page(page, step_index, step, html_content)
                if locator_found:
                    success = await self._execute_step(page, step, step_index)
                else:
                    print(f"Could not find locator for step {step_index + 1} on current page")
                    success = False
            elif locator:
                success = await self._execute_action_with_locator(page, action_plan, locator)
            else:
                success = await self._execute_action(page, action_plan, step_index)

            # Handle re-analysis after navigation steps
            if success and requires_reanalysis:
                print(f"Step {step_index + 1} completed successfully. Re-analyzing page for next steps...")
                await self._reanalyze_after_navigation(page, step_index)



            # Track step result
            if success:
                self.step_results[step_index] = {'status': 'passed'}
            else:
                self.step_results[step_index] = {'status': 'failed', 'error': 'Step execution failed'}

            return success

        except Exception as e:
            print(f"Error during step execution: {str(e)}")
            
            # Track step result for exception
            self.step_results[step_index] = {'status': 'failed', 'error': str(e)}
            
            return False

    async def _reanalyze_after_navigation(self, page, completed_step_index):
        """
        Re-analyze the page after a navigation step to update locators for subsequent steps.
        Args:
            page: Playwright page object
            completed_step_index: Index of the completed navigation step
        """
        try:
            print(f"Re-analyzing page after navigation step {completed_step_index + 1}")
            # Wait a moment for the page to fully load
            await asyncio.sleep(2)
            # Get current page content
            html_content = await page.content()
            self.current_url = page.url
            print(f"Current page after navigation: {self.current_url}")
            # Extract all locators from the new page
            new_locators = self.dom_analyzer.extract_all_locators(html_content)
            # Update collected locators with new ones
            for locator_id, locator in new_locators.items():
                if locator_id not in self.collected_locators:
                    self.collected_locators[locator_id] = locator
                    print(f"Added new locator: {locator_id}")
            # Update locators for subsequent steps that might be affected
            for step_index in range(completed_step_index + 1, len(self.steps)):
                step = self.steps[step_index]
                step_info = self.step_locator_map.get(step_index)
                # Skip steps that already have valid locators
                if step_info and step_info.get('locator'):
                    continue
                # Try to find a locator for this step on the new page
                print(f"Looking for locator for step {step_index + 1}: {step}")
                locator_found = await self._find_locator_for_step_on_current_page(page, step_index, step, html_content)
                if locator_found:
                    print(f"Updated locator for step {step_index + 1} after navigation")
                else:
                    print(f"Could not find locator for step {step_index + 1} on new page")
            print("Re-analysis completed")
        except Exception as e:
            print(f"Error during re-analysis after navigation: {e}")

    async def _execute_action_with_locator(self, page, action_plan, locator):
        """Execute an action using a pre-analyzed locator."""
        try:
            action_type = action_plan.get('action_type', '')
            value = action_plan.get('value', '')
            selectors = locator.get('selector', '')
            # Support multiple selectors
            if isinstance(selectors, str):
                selectors = [selectors]
            # If locator has all_selectors, use those instead
            if locator.get('all_selectors'):
                selectors = locator.get('all_selectors')
                print(f"Using all available selectors: {selectors}")
                print(f"Will try selectors in this order: {selectors}")
            # Check if this locator requires clicking an icon first
            if locator.get('requires_icon_click') and locator.get('icon_selector'):
                try:
                    icon_selector = locator.get('icon_selector')
                    print(f"This search field requires clicking an icon first: {icon_selector}")
                    await page.click(icon_selector)
                    await page.wait_for_timeout(1000)
                except Exception as e:
                    print(f"Error clicking search icon: {e}")
            if action_type == 'type':
                # Handle type actions
                for selector in selectors:
                    try:
                        # Find the input element
                        element = await page.wait_for_selector(selector, timeout=5000)
                        if element:
                            # Clear the field first
                            await element.fill('')
                            # Type the value
                            await element.type(value)
                            print(f"Typed '{value}' into field using selector: {selector}")
                            return True
                        else:
                            print(f"Could not find input with selector: {selector}")
                            continue
                    except Exception as e:
                        print(f"Error typing into field with selector {selector}: {e}")
                        continue
                
                print(f"All selectors tried, type action failed for value: {value}")
                return False
            elif action_type == "click":
                target = action_plan.get('target_description', '')
                for selector in selectors:
                    print(f"Trying selector: {selector}")
                    try:
                        elements = await page.query_selector_all(selector)
                        if not elements:
                            print(f"No elements found with selector: {selector}")
                            continue
                        for element in elements:
                            try:
                                # Highlight - use Playwright locator for text-based selectors
                                if 'has-text' in selector:
                                    # For text-based selectors, use Playwright locator
                                    locator_obj = page.locator(selector)
                                    await locator_obj.highlight()
                                else:
                                    # For standard CSS selectors, use querySelector
                                    await page.evaluate(f"""(selector) => {{
                                        const element = document.querySelector(selector);
                                        if (element) {{
                                            element.style.transition = 'all 0.3s';
                                            element.style.border = '3px solid red';
                                            element.style.boxShadow = '0 0 10px rgba(255, 0, 0, 0.5)';
                                        }}
                                    }}""", selector)
                                await asyncio.sleep(1)
                                # Try click strategies
                                click_success = False
                                try:
                                    await element.click()
                                    print(f"Clicked element with selector: {selector}")
                                    click_success = True
                                except Exception as e1:
                                    print(f"Direct click failed: {e1}")
                                    try:
                                        if 'has-text' in selector:
                                            # For text-based selectors, use Playwright locator
                                            locator_obj = page.locator(selector)
                                            await locator_obj.click()
                                            print(f"Playwright locator click successful for selector: {selector}")
                                        else:
                                            # For standard CSS selectors, use querySelector
                                            await page.evaluate("""(selector) => {
                                                const element = document.querySelector(selector);
                                                if (element) { element.click(); }
                                            }""", selector)
                                            print(f"JavaScript click successful for selector: {selector}")
                                        click_success = True
                                    except Exception as e2:
                                        print(f"JavaScript click failed: {e2}")
                                if click_success:
                                    await page.wait_for_load_state("networkidle", timeout=5000)
                                    # For SPAs, also wait for any content changes
                                    await page.wait_for_timeout(2000)
                                    return True
                            except Exception as e:
                                print(f"Error clicking element with selector {selector}: {e}")
                                continue
                    except Exception as e:
                        print(f"Error finding elements with selector {selector}: {e}")
                        continue
                print(f"All selectors and elements tried, click failed for target: {target}")
                return False
            elif action_type == "assert":
                target = action_plan.get('target_description', '')
                print(f"Executing assertion for target: {target}")
                # Try multiple approaches to find the text
                found = False
                # Method 1: Try all selectors
                for selector in selectors:
                    print(f"Trying assertion with selector: {selector}")
                    try:
                        if 'has-text' in selector:
                            locator_obj = page.locator(selector)
                            await locator_obj.wait_for(timeout=3000)
                            found = True
                            print(f"Assertion passed: Found text '{target}' on page using selector: {selector}")
                            break
                        else:
                            await page.wait_for_selector(selector, timeout=3000)
                            found = True
                            print(f"Assertion passed: Found element with selector: {selector}")
                            break
                    except Exception:
                        pass
                # Method 2: Try case-insensitive text match using :has-text()
                if not found:
                    try:
                        await page.wait_for_selector(f":has-text(\"{target}\")", timeout=3000)
                        found = True
                        print(f"Assertion passed: Found text '{target}' on page (case-insensitive)")
                    except Exception:
                        pass
                # Method 3: Try partial text match
                if not found:
                    try:
                        await page.evaluate(f"""(target) => {{
                            const elements = Array.from(document.querySelectorAll('*'));
                            const found = elements.some(el => 
                                el.textContent && el.textContent.includes(target)
                            );
                            if (!found) throw new Error('Text not found');
                        }}""", target)
                        found = True
                        print(f"Assertion passed: Found text '{target}' in page content")
                    except Exception:
                        pass
                if found:
                    await page.evaluate(f"""(target) => {{
                        function highlightText(text) {{
                            const walker = document.createTreeWalker(
                                document.body,
                                NodeFilter.SHOW_TEXT,
                                {{ acceptNode: node => node.textContent.includes(text) ? NodeFilter.FILTER_ACCEPT : NodeFilter.FILTER_REJECT }}
                            );
                            const matches = [];
                            while(walker.nextNode()) {{
                                matches.push(walker.currentNode);
                            }}
                            matches.forEach(textNode => {{
                                const span = document.createElement('span');
                                span.className = 'test-verification-highlight';
                                span.style.backgroundColor = 'rgba(0, 255, 0, 0.3)';
                                span.style.border = '2px solid green';
                                span.style.borderRadius = '3px';
                                span.style.padding = '2px';
                                span.style.transition = 'all 0.5s';
                                const text = textNode.textContent;
                                const targetIndex = text.indexOf(target);
                                if (targetIndex >= 0) {{
                                    const before = text.substring(0, targetIndex);
                                    const highlight = text.substring(targetIndex, targetIndex + target.length);
                                    const after = text.substring(targetIndex + target.length);
                                    const parent = textNode.parentNode;
                                    const beforeNode = document.createTextNode(before);
                                    const highlightNode = document.createElement('span');
                                    highlightNode.className = 'test-verification-highlight';
                                    highlightNode.style.backgroundColor = 'rgba(0, 255, 0, 0.3)';
                                    highlightNode.style.border = '2px solid green';
                                    highlightNode.style.borderRadius = '3px';
                                    highlightNode.style.padding = '2px';
                                    highlightNode.textContent = highlight;
                                    const afterNode = document.createTextNode(after);
                                    parent.replaceChild(afterNode, textNode);
                                    parent.insertBefore(highlightNode, afterNode);
                                    parent.insertBefore(beforeNode, highlightNode);
                                }}
                            }});
                            setTimeout(() => {{
                                document.querySelectorAll('.test-verification-highlight').forEach(el => {{
                                    const parent = el.parentNode;
                                    const text = el.textContent;
                                    const textNode = document.createTextNode(text);
                                    parent.replaceChild(textNode, el);
                                }});
                            }}, 3000);
                        }}
                        highlightText(target);
                    }}""", target)
                    await asyncio.sleep(1)
                    return True
                else:
                    print(f"Assertion failed: Text '{target}' not found on page")
                    return False
        except Exception as e:
            print(f"Error during click/assert action: {e}")
            return False

    async def _execute_action(self, page, action_plan, step_index):
        """Execute an action based on the action plan with intelligent error handling and retry mechanisms."""
        try:
            action_type = action_plan.get('action_type', '')
            target = action_plan.get('target_description', '')
            value = action_plan.get('value', '')
            url = action_plan.get('url', '')

            print(f"Executing action: {action_type} | Target: {target} | Value: {value} | URL: {url}")

            # Brief pause before action
            await asyncio.sleep(0.5)  # Reduced for better performance

            if action_type == 'navigate':
                return await self._execute_navigation(page, url)

            elif action_type == 'type':
                return await self._execute_type_action(page, target, value, step_index)

            elif action_type == 'click':
                return await self._execute_click_action(page, target, step_index)

            elif action_type == 'assert':
                return await self._execute_assert_action(page, target, step_index)

            elif action_type == 'scroll':
                return await self._execute_scroll_action(page, target, step_index)

            else:
                print(f"Unknown action type: {action_type}")
                return False

        except Exception as e:
            print(f"Error executing action: {e}")
            return False

    async def _execute_navigation(self, page, url):
        """Execute navigation action with retry mechanism."""
        max_retries = 3
        for attempt in range(max_retries):
            try:
                if url:
                    await page.goto(url, wait_until="networkidle", timeout=30000)
                    print(f"✅ Navigated to: {url}")
                    return True
                else:
                    print("❌ Error: No URL provided for navigation")
                    return False
            except Exception as e:
                print(f"⚠️ Navigation attempt {attempt + 1} failed: {e}")
                if attempt < max_retries - 1:
                    await asyncio.sleep(2)
                    continue
                else:
                    print(f"❌ Navigation failed after {max_retries} attempts")
                    return False

    async def _execute_type_action(self, page, target, value, step_index):
        """Execute type action with intelligent locator finding."""
        max_retries = 3
        
        for attempt in range(max_retries):
            try:
                # Strategy 1: Use pre-collected locators
                if 'search_input' in self.collected_locators:
                    search_locator = self.collected_locators['search_input']
                    selector = search_locator.get('selector', 'input[type="text"]')
                    
                    element = await page.wait_for_selector(selector, timeout=5000)
                    if element and await element.is_visible():
                        await element.fill('')
                        await element.type(value)
                        print(f"✅ Typed '{value}' using pre-collected selector: {selector}")
                        return True

                # Strategy 2: Use smart locator finder
                locator_result = await self.smart_locator_finder.smart_find_locator(page, 'type', target, value)
                if locator_result:
                    selector = locator_result.get('selector')
                    element = await page.wait_for_selector(selector, timeout=5000)
                    if element and await element.is_visible():
                        await element.fill('')
                        await element.type(value)
                        print(f"✅ Typed '{value}' using smart locator: {selector}")
                        return True

                # Strategy 3: Common input selectors
                common_selectors = [
                    'input[type="text"]',
                    'input[type="search"]',
                    'input[placeholder*="search" i]',
                    'input[placeholder*="cari" i]',
                    'input[aria-label*="search" i]',
                    'input[aria-label*="cari" i]',
                    '.search-input',
                    '.search-field',
                    '#search',
                    'input.form-control'
                ]

                for selector in common_selectors:
                    try:
                        element = await page.wait_for_selector(selector, timeout=3000)
                        if element and await element.is_visible():
                            await element.fill('')
                            await element.type(value)
                            print(f"✅ Typed '{value}' using common selector: {selector}")
                            return True
                    except:
                        continue

                print(f"⚠️ Type action attempt {attempt + 1} failed")
                if attempt < max_retries - 1:
                    await asyncio.sleep(1)
                    continue

            except Exception as e:
                print(f"⚠️ Type action error on attempt {attempt + 1}: {e}")
                if attempt < max_retries - 1:
                    await asyncio.sleep(1)
                    continue

        print(f"❌ Type action failed after {max_retries} attempts")
        return False

    async def _execute_click_action(self, page, target, step_index):
        """Execute click action with intelligent locator finding."""
        max_retries = 3
        
        for attempt in range(max_retries):
            try:
                # Strategy 1: Use smart locator finder
                locator_result = await self.smart_locator_finder.smart_find_locator(page, 'click', target)
                if locator_result:
                    selector = locator_result.get('selector')
                    await self._highlight_element(page, selector)
                    await page.click(selector, timeout=5000)
                    print(f"✅ Clicked using smart locator: {selector}")
                    await asyncio.sleep(1)
                    return True

                # Strategy 2: Common click selectors
                common_selectors = [
                    f"button:has-text('{target}')",
                    f"a:has-text('{target}')",
                    f"button[type='submit']",
                    f"input[type='submit'][value*='{target}' i]",
                    f".btn:has-text('{target}')",
                    f"[aria-label*='{target}' i]",
                    f"[title*='{target}' i]",
                    f"[role='button']:has-text('{target}')",
                    f"[onclick]:has-text('{target}')"
                ]

                for selector in common_selectors:
                    try:
                        await self._highlight_element(page, selector)
                        await page.click(selector, timeout=5000)
                        print(f"✅ Clicked using common selector: {selector}")
                        await asyncio.sleep(1)
                        return True
                    except Exception as e:
                        continue

                print(f"⚠️ Click action attempt {attempt + 1} failed")
                if attempt < max_retries - 1:
                    await asyncio.sleep(1)
                    continue

            except Exception as e:
                print(f"⚠️ Click action error on attempt {attempt + 1}: {e}")
                if attempt < max_retries - 1:
                    await asyncio.sleep(1)
                    continue

        print(f"❌ Click action failed after {max_retries} attempts")
        return False

    async def _execute_assert_action(self, page, target, step_index):
        """Execute assert action with intelligent text finding."""
        max_retries = 3
        
        for attempt in range(max_retries):
            try:
                # Strategy 1: Use smart locator finder
                locator_result = await self.smart_locator_finder.smart_find_locator(page, 'assert', target)
                if locator_result:
                    selector = locator_result.get('selector')
                    element = await page.wait_for_selector(selector, timeout=5000)
                    if element and await element.is_visible():
                        await self._highlight_text(page, target)
                        print(f"✅ Assertion passed using smart locator: {selector}")
                        return True

                # Strategy 2: Direct text search
                try:
                    element = await page.wait_for_selector(f':has-text("{target}")', timeout=5000)
                    if element and await element.is_visible():
                        await self._highlight_text(page, target)
                        print(f"✅ Assertion passed: Found text '{target}'")
                        return True
                except:
                    pass

                # Strategy 3: Case-insensitive search
                try:
                    element = await page.wait_for_selector(f':has-text("{target.lower()}")', timeout=3000)
                    if element and await element.is_visible():
                        await self._highlight_text(page, target)
                        print(f"✅ Assertion passed (case-insensitive): Found text '{target}'")
                        return True
                except:
                    pass

                # Strategy 4: Partial text match
                words = target.split()
                for word in words:
                    if len(word) > 3:
                        try:
                            element = await page.wait_for_selector(f':has-text("{word}")', timeout=2000)
                            if element and await element.is_visible():
                                await self._highlight_text(page, target)
                                print(f"✅ Assertion passed (partial match): Found word '{word}' from '{target}'")
                                return True
                        except:
                            continue

                print(f"⚠️ Assert action attempt {attempt + 1} failed")
                if attempt < max_retries - 1:
                    await asyncio.sleep(1)
                    continue

            except Exception as e:
                print(f"⚠️ Assert action error on attempt {attempt + 1}: {e}")
                if attempt < max_retries - 1:
                    await asyncio.sleep(1)
                    continue

        print(f"❌ Assert action failed after {max_retries} attempts")
        return False

    async def _execute_scroll_action(self, page, target, step_index):
        """Execute scroll action with intelligent element finding."""
        max_retries = 3
        
        for attempt in range(max_retries):
            try:
                # Strategy 1: Scroll to specific element
                locator_result = await self.smart_locator_finder.smart_find_locator(page, 'assert', target)
                if locator_result:
                    selector = locator_result.get('selector')
                    element = await page.wait_for_selector(selector, timeout=5000)
                    if element:
                        await element.scroll_into_view_if_needed()
                        print(f"✅ Scrolled to element: {selector}")
                        return True

                # Strategy 2: Scroll to text
                try:
                    element = await page.wait_for_selector(f':has-text("{target}")', timeout=5000)
                    if element:
                        await element.scroll_into_view_if_needed()
                        print(f"✅ Scrolled to text: '{target}'")
                        return True
                except:
                    pass

                # Strategy 3: Generic scroll down
                await page.evaluate("window.scrollBy(0, 500)")
                print(f"✅ Performed generic scroll")
                return True

            except Exception as e:
                print(f"⚠️ Scroll action error on attempt {attempt + 1}: {e}")
                if attempt < max_retries - 1:
                    await asyncio.sleep(1)
                    continue

        print(f"❌ Scroll action failed after {max_retries} attempts")
        return False

    async def _highlight_element(self, page, selector):
        """Highlight an element before interaction."""
        try:
            await page.evaluate(f"""(selector) => {{
                const element = document.querySelector(selector);
                if (element) {{
                    element.style.border = '3px solid red';
                    element.style.boxShadow = '0 0 10px rgba(255, 0, 0, 0.7)';
                    element.style.transition = 'all 0.3s';
                }}
            }}""", selector)
            await asyncio.sleep(0.5)
        except:
            pass

    async def _highlight_text(self, page, target_text):
        """Highlight text on the page for verification."""
        try:
            await page.evaluate("""(target) => {
                // Function to highlight text on the page
                function highlightText(text) {
                    const walker = document.createTreeWalker(
                        document.body,
                        NodeFilter.SHOW_TEXT,
                        { acceptNode: node => node.textContent.includes(text) ? NodeFilter.FILTER_ACCEPT : NodeFilter.FILTER_REJECT }
                    );

                    const matches = [];
                    while(walker.nextNode()) {
                        matches.push(walker.currentNode);
                    }

                    matches.forEach(textNode => {
                        const span = document.createElement('span');
                        span.className = 'test-verification-highlight';
                        span.style.backgroundColor = 'rgba(0, 255, 0, 0.3)';
                        span.style.border = '2px solid green';
                        span.style.borderRadius = '3px';
                        span.style.padding = '2px';
                        span.style.transition = 'all 0.5s';

                        const text = textNode.textContent;
                        const targetIndex = text.indexOf(target);
                        if (targetIndex >= 0) {
                            const before = text.substring(0, targetIndex);
                            const highlight = text.substring(targetIndex, targetIndex + target.length);
                            const after = text.substring(targetIndex + target.length);

                            const parent = textNode.parentNode;

                            // Create text nodes and highlighted span
                            const beforeNode = document.createTextNode(before);
                            const highlightNode = document.createElement('span');
                            highlightNode.className = 'test-verification-highlight';
                                    highlightNode.style.backgroundColor = 'rgba(0, 255, 0, 0.3)';
                                    highlightNode.style.border = '2px solid green';
                                    highlightNode.style.borderRadius = '3px';
                                    highlightNode.style.padding = '2px';
                                    highlightNode.textContent = highlight;
                                    const afterNode = document.createTextNode(after);

                                    // Replace the original text node
                                    parent.replaceChild(afterNode, textNode);
                                    parent.insertBefore(highlightNode, afterNode);
                                    parent.insertBefore(beforeNode, highlightNode);
                                }
                            });

                            // Remove highlights after 3 seconds
                            setTimeout(() => {
                                document.querySelectorAll('.test-verification-highlight').forEach(el => {
                                    const parent = el.parentNode;
                                    const text = el.textContent;
                                    const textNode = document.createTextNode(text);
                                    parent.replaceChild(textNode, el);
                                });
                            }, 3000);
                        }

                        highlightText(target);
                    }""", target_text)

            await asyncio.sleep(0.5)
        except Exception as e:
            print(f"Error highlighting text: {e}")
            pass

    async def _find_locator_for_step_on_current_page(self, page, step_index, step, html_content):
        """
        Find a locator for a step on the current page.

        Args:
            page: Playwright page object
            step_index: Index of the step
            step: Step text
            html_content: HTML content of the page

        Returns:
            bool: True if locator was found, False otherwise
        """
        try:
            print(f"Finding locator for step {step_index + 1} on current page: {page.url}")

            # Extract all locators from current page
            new_locators = self.dom_analyzer.extract_all_locators(html_content)

            # Add new locators to our collection
            for locator_id, locator in new_locators.items():
                if locator_id not in self.collected_locators:
                    self.collected_locators[locator_id] = locator

            # Interpret the step
            action_plan = self._interpret_gherkin_step(step)
            if not action_plan:
                print(f"Could not interpret step: {step}")
                return False

            action_type = action_plan.get('action_type', '')
            target = action_plan.get('target_description', '')
            value = action_plan.get('value', '')

            # Use smart locator finder for better element detection
            print(f"🔍 Using smart locator finder for {action_type} action")
            smart_locator = await self.smart_locator_finder.smart_find_locator(page, action_type, target, value)
            
            if smart_locator:
                print(f"✅ Smart locator finder found element: {smart_locator['selector']}")
                
                locator_id = f"smart_{action_type}_{step_index}"
                self.collected_locators[locator_id] = {
                    "type": smart_locator['type'],
                    "selector": smart_locator['selector'],
                    "confidence": smart_locator['confidence'],
                    "ai_generated": False,
                    "strategy": smart_locator.get('strategy', 'unknown')
                }

                self.step_locator_map[step_index] = {
                    'action': action_type,
                    'target': target,
                    'value': value,
                    'locator': self.collected_locators[locator_id]
                }
                return True
            else:
                print(f"❌ Smart locator finder could not find element for {action_type} '{target}'")
                return False

        except Exception as e:
            print(f"Error finding locator on current page: {e}")
            return False

    async def _deep_ai_locator_analysis(self):
        """Use AI for deep analysis to find missing locators.

        This method performs a more thorough analysis using AI to identify
        hard-to-find elements on the page.

        Returns:
            bool: True if new locators were found, False otherwise
        """
        print("Starting deep AI analysis to find missing locators...")

        # Find steps that don't have locators
        missing_steps = [(i, step) for i, step in enumerate(self.steps)
                         if i not in self.step_locator_map]

        if not missing_steps:
            print("No missing steps found, but validating existing locators...")
            return True

        print(f"Found {len(missing_steps)} steps without locators. Using AI for deep analysis...")

        # Open the page to analyze it
        async with async_playwright() as p:
            browser = await p.chromium.launch(headless=self.config.get('browser.headless', True))
            context = await browser.new_context()
            page = await context.new_page()

            try:
                # Navigate to the URL
                await page.goto(self.current_url, wait_until="networkidle")

                # Get the page content
                html_content = await page.content()

                # Take a screenshot for better context
                screenshot_path = "temp_screenshot.png"
                await page.screenshot(path=screenshot_path)
                print(f"Captured page screenshot for analysis at {screenshot_path}")

                # For each missing step, ask the AI to find a locator with more context
                new_locators_found = False

                for step_index, step in missing_steps:
                    print(f"Deep AI analysis for step {step_index + 1}: {step}")

                    # Create a more detailed prompt for the AI
                    prompt = f"""
                    I need to find a precise locator for this step in a web automation test: "{step}"

                    The website is {self.current_url}

                    I need the most reliable CSS selector or XPath to locate the element needed for this step.

                    Consider these strategies:
                    1. For buttons: Look for button elements, input[type="button"], elements with button roles, or clickable divs/spans
                    2. For inputs: Check input fields, textareas, contenteditable divs
                    3. For text assertions: Find elements containing the exact text
                    4. For navigation: Look for links (a tags) with matching text or href attributes

                    Analyze the page structure carefully and suggest multiple possible selectors, ranked by reliability.

                    Return your answer in JSON format with these fields:
                    {{
                        "primary_selector": "the best CSS selector or XPath",
                        "alternative_selectors": ["backup selector 1", "backup selector 2"],
                        "element_type": "button/input/link/etc",
                        "confidence": 0.0-1.0,
                        "reasoning": "brief explanation of why this is the best selector"
                    }}

                    Only return the JSON, no other text.
                    """

                    # Get AI response
                    ai_response = self.interpreter.get_raw_completion(prompt)

                    try:
                        # Parse the JSON response
                        locator_info = json.loads(ai_response)

                        # Try the primary selector first
                        primary_selector = locator_info.get("primary_selector")
                        alternative_selectors = locator_info.get("alternative_selectors", [])

                        # Try all selectors until one works
                        working_selector = None
                        selector_confidence = 0

                        try:
                            # Try primary selector
                            element = await page.wait_for_selector(primary_selector, timeout=2000)
                            if element:
                                working_selector = primary_selector
                                selector_confidence = locator_info.get("confidence", 0.8)
                                print(f"Primary selector works: {primary_selector}")
                        except Exception:
                            # Try alternative selectors
                            for i, alt_selector in enumerate(alternative_selectors):
                                try:
                                    element = await page.wait_for_selector(alt_selector, timeout=2000)
                                    if element:
                                        working_selector = alt_selector
                                        # Reduce confidence slightly for alternatives
                                        selector_confidence = max(0.5, locator_info.get("confidence", 0.8) - (i+1)*0.1)
                                        print(f"Alternative selector {i+1} works: {alt_selector}")
                                        break
                                except Exception:
                                    continue

                        if working_selector:
                            # Add to collected locators
                            locator_id = f"deep_ai_locator_{step_index}"
                            self.collected_locators[locator_id] = {
                                "type": locator_info.get("element_type", "unknown"),
                                "selector": working_selector,
                                "confidence": selector_confidence,
                                "ai_generated": True,
                                "reasoning": locator_info.get("reasoning", "")
                            }

                            # Add to step locator map
                            action_plan = self.interpreter.interpret_step(step, "")
                            if action_plan:
                                action_type = action_plan.get('action_type', '')
                                target = action_plan.get('target_description', '')
                                value = action_plan.get('value', '')

                                self.step_locator_map[step_index] = {
                                    'action': action_type,
                                    'target': target,
                                    'value': value,
                                    'locator': self.collected_locators[locator_id]
                                }

                                new_locators_found = True
                                print(f"Successfully found locator for step {step_index + 1}")
                    except Exception as e:
                        print(f"Error processing AI response for step {step_index + 1}: {e}")

                await browser.close()

                # Clean up screenshot
                import os
                if os.path.exists(screenshot_path):
                    os.remove(screenshot_path)

                return new_locators_found

            except Exception as e:
                print(f"Error in deep AI locator analysis: {e}")
                await browser.close()
                return False

    async def _ensure_window_maximized(self, page):
        """Ensure the browser window is maximized using multiple methods."""
        try:
            # Method 1: Use JavaScript to maximize
            await page.evaluate("""() => {
                window.moveTo(0, 0);
                window.resizeTo(
                    Math.max(window.screen.width, window.screen.availWidth),
                    Math.max(window.screen.height, window.screen.availHeight)
                );
            }""")

            # Method 2: Set document body to fill viewport
            await page.evaluate("""() => {
                document.documentElement.style.overflow = 'hidden';
                document.body.style.overflow = 'hidden';
                document.documentElement.style.width = '100vw';
                document.documentElement.style.height = '100vh';
                document.body.style.width = '100vw';
                document.body.style.height = '100vh';
            }""")

            # Wait a moment for the resize to take effect
            await asyncio.sleep(1)

            # Verify the size
            actual_size = await page.evaluate("""() => {
                return {
                    width: window.innerWidth,
                    height: window.innerHeight,
                    screen: {
                        width: window.screen.width,
                        height: window.screen.height
                    }
                }
            }""")
            

            return True
        except Exception as e:
            print(f"Could not maximize window: {e}")
            return False
