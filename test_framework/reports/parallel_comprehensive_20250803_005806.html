
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON><PERSON> Pendidikan Automation Report</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }
        .header h2 {
            margin: 10px 0 0 0;
            font-size: 1.2em;
            font-weight: 300;
            opacity: 0.9;
        }
        .summary-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            padding: 30px;
            background: #f8f9fa;
        }
        .card {
            background: white;
            border-radius: 10px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            text-align: center;
        }
        .card h3 {
            margin: 0 0 20px 0;
            color: #333;
            font-size: 1.3em;
        }
        .donut-chart {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            margin: 0 auto 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2em;
            font-weight: bold;
            color: white;
        }
        .donut-passed { background: conic-gradient(#28a745 0deg 360deg); }
        .donut-failed { background: conic-gradient(#dc3545 0deg 360deg); }
        .donut-mixed { background: conic-gradient(#28a745 0deg 0.0deg, #dc3545 0.0deg 360deg); }
        .stats-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            text-align: left;
        }
        .stat-item {
            display: flex;
            justify-content: space-between;
            padding: 5px 0;
        }
        .stat-label {
            font-weight: 500;
            color: #666;
        }
        .stat-value {
            font-weight: bold;
            color: #333;
        }
        .test-results {
            padding: 30px;
        }
        .test-results h3 {
            margin: 0 0 20px 0;
            color: #333;
            font-size: 1.5em;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }
        th, td {
            padding: 15px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }
        th {
            background: #f8f9fa;
            font-weight: 600;
            color: #333;
        }
        tr:hover {
            background: #f8f9fa;
        }
        .status-passed { color: #28a745; font-weight: bold; }
        .status-failed { color: #dc3545; font-weight: bold; }
        .status-skipped { color: #ffc107; font-weight: bold; }
        .status-error { color: #dc3545; font-weight: bold; }
        .status-timeout { color: #fd7e14; font-weight: bold; }
        .footer {
            background: #f8f9fa;
            padding: 20px;
            text-align: center;
            color: #666;
            border-top: 1px solid #eee;
        }
        .social-icons {
            margin-top: 10px;
        }
        .social-icons a {
            margin: 0 10px;
            color: #666;
            text-decoration: none;
            font-size: 1.2em;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Rumah Pendidikan Automation Report</h1>
            <h2>Comprehensive Test Automation Report</h2>
        </div>
        
        <div class="summary-cards">
            <div class="card">
                <h3>Features</h3>
                <div class="donut-chart donut-passed">
                    14
                </div>
                <div class="stats-grid">
                    <div class="stat-item">
                        <span class="stat-label">STATUS</span>
                        <span class="stat-value">✅ Passed</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">PROGRESS</span>
                        <span class="stat-value">0.0%</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">STATUS</span>
                        <span class="stat-value">❌ Failed</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">PROGRESS</span>
                        <span class="stat-value">0.0%</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">STATUS</span>
                        <span class="stat-value">⏭️ Skipped</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">PROGRESS</span>
                        <span class="stat-value">0.0%</span>
                    </div>
                </div>
            </div>
            
            <div class="card">
                <h3>Scenarios</h3>
                <div class="donut-chart donut-passed">
                    14
                </div>
                <div class="stats-grid">
                    <div class="stat-item">
                        <span class="stat-label">STATUS</span>
                        <span class="stat-value">✅ Passed</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">PROGRESS</span>
                        <span class="stat-value">0.0%</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">STATUS</span>
                        <span class="stat-value">❌ Failed</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">PROGRESS</span>
                        <span class="stat-value">0.0%</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">STATUS</span>
                        <span class="stat-value">⏭️ Skipped</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">PROGRESS</span>
                        <span class="stat-value">0.0%</span>
                    </div>
                </div>
            </div>
            
            <div class="card">
                <h3>Run Info</h3>
                <div style="text-align: left; margin-top: 20px;">
                    <div class="stat-item">
                        <span class="stat-label">🔗 Project</span>
                        <span class="stat-value">Rumah Pendidikan Automation Report</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">🕐 Generation Time</span>
                        <span class="stat-value">2025-08-03T00:58:06.247467Z</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">👤 OS User</span>
                        <span class="stat-value">jemz</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">⏱️ Duration</span>
                        <span class="stat-value">6 Seconds</span>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="test-results">
            <h3>Features Overview</h3>
            <table>
                <thead>
                    <tr>
                        <th>Feature Name</th>
                        <th>Status</th>
                        <th>Device</th>
                        <th>OS</th>
                        <th>Browser</th>
                        <th>Total</th>
                        <th>Passed</th>
                        <th>Failed</th>
                        <th>Undefined</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td style="color: #007bff; font-weight: 500;">Parallel Test Execution</td>
                        <td>✅</td>
                        <td>🖥️ Runner Machine</td>
                        <td>🐧 linux</td>
                        <td>🌐 103</td>
                        <td>14</td>
                        <td>0</td>
                        <td>0</td>
                        <td>14</td>
                    </tr>
                </tbody>
            </table>
            
            <h3 style="margin-top: 30px;">Detailed Test Results</h3>
            <table>
                <thead>
                    <tr>
                        <th>Test Case</th>
                        <th>Status</th>
                        <th>Duration</th>
                        <th>Steps Passed</th>
                        <th>Steps Failed</th>
                        <th>Steps Skipped</th>
                        <th>Total Steps</th>
                        <th>Error Message</th>
                    </tr>
                </thead>
                <tbody>
                    
            <tr>
                <td>TC1</td>
                <td>💥 ERROR</td>
                <td>0.5s</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>Failed to execute test case TC1: usage: main.py [-h] [--excel EXCEL] [--features FEATURES] [--output OUTPUT]
               [--model MODEL] [--convert-to-indonesian]
               [--browser {chromium,firefox,webkit}] [--test-cases TEST_CASES]
               [--parallel-mode] [--single-report]
main.py: error: unrecognized arguments: true true
</td>
            </tr>
            
            <tr>
                <td>TC10</td>
                <td>💥 ERROR</td>
                <td>0.4s</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>Failed to execute test case TC10: usage: main.py [-h] [--excel EXCEL] [--features FEATURES] [--output OUTPUT]
               [--model MODEL] [--convert-to-indonesian]
               [--browser {chromium,firefox,webkit}] [--test-cases TEST_CASES]
               [--parallel-mode] [--single-report]
main.py: error: unrecognized arguments: true true
</td>
            </tr>
            
            <tr>
                <td>TC11</td>
                <td>💥 ERROR</td>
                <td>0.4s</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>Failed to execute test case TC11: usage: main.py [-h] [--excel EXCEL] [--features FEATURES] [--output OUTPUT]
               [--model MODEL] [--convert-to-indonesian]
               [--browser {chromium,firefox,webkit}] [--test-cases TEST_CASES]
               [--parallel-mode] [--single-report]
main.py: error: unrecognized arguments: true true
</td>
            </tr>
            
            <tr>
                <td>TC12</td>
                <td>💥 ERROR</td>
                <td>0.4s</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>Failed to execute test case TC12: usage: main.py [-h] [--excel EXCEL] [--features FEATURES] [--output OUTPUT]
               [--model MODEL] [--convert-to-indonesian]
               [--browser {chromium,firefox,webkit}] [--test-cases TEST_CASES]
               [--parallel-mode] [--single-report]
main.py: error: unrecognized arguments: true true
</td>
            </tr>
            
            <tr>
                <td>TC13</td>
                <td>💥 ERROR</td>
                <td>0.5s</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>Failed to execute test case TC13: usage: main.py [-h] [--excel EXCEL] [--features FEATURES] [--output OUTPUT]
               [--model MODEL] [--convert-to-indonesian]
               [--browser {chromium,firefox,webkit}] [--test-cases TEST_CASES]
               [--parallel-mode] [--single-report]
main.py: error: unrecognized arguments: true true
</td>
            </tr>
            
            <tr>
                <td>TC14</td>
                <td>💥 ERROR</td>
                <td>0.4s</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>Failed to execute test case TC14: usage: main.py [-h] [--excel EXCEL] [--features FEATURES] [--output OUTPUT]
               [--model MODEL] [--convert-to-indonesian]
               [--browser {chromium,firefox,webkit}] [--test-cases TEST_CASES]
               [--parallel-mode] [--single-report]
main.py: error: unrecognized arguments: true true
</td>
            </tr>
            
            <tr>
                <td>TC2</td>
                <td>💥 ERROR</td>
                <td>0.4s</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>Failed to execute test case TC2: usage: main.py [-h] [--excel EXCEL] [--features FEATURES] [--output OUTPUT]
               [--model MODEL] [--convert-to-indonesian]
               [--browser {chromium,firefox,webkit}] [--test-cases TEST_CASES]
               [--parallel-mode] [--single-report]
main.py: error: unrecognized arguments: true true
</td>
            </tr>
            
            <tr>
                <td>TC3</td>
                <td>💥 ERROR</td>
                <td>0.4s</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>Failed to execute test case TC3: usage: main.py [-h] [--excel EXCEL] [--features FEATURES] [--output OUTPUT]
               [--model MODEL] [--convert-to-indonesian]
               [--browser {chromium,firefox,webkit}] [--test-cases TEST_CASES]
               [--parallel-mode] [--single-report]
main.py: error: unrecognized arguments: true true
</td>
            </tr>
            
            <tr>
                <td>TC4</td>
                <td>💥 ERROR</td>
                <td>0.4s</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>Failed to execute test case TC4: usage: main.py [-h] [--excel EXCEL] [--features FEATURES] [--output OUTPUT]
               [--model MODEL] [--convert-to-indonesian]
               [--browser {chromium,firefox,webkit}] [--test-cases TEST_CASES]
               [--parallel-mode] [--single-report]
main.py: error: unrecognized arguments: true true
</td>
            </tr>
            
            <tr>
                <td>TC5</td>
                <td>💥 ERROR</td>
                <td>0.4s</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>Failed to execute test case TC5: usage: main.py [-h] [--excel EXCEL] [--features FEATURES] [--output OUTPUT]
               [--model MODEL] [--convert-to-indonesian]
               [--browser {chromium,firefox,webkit}] [--test-cases TEST_CASES]
               [--parallel-mode] [--single-report]
main.py: error: unrecognized arguments: true true
</td>
            </tr>
            
            <tr>
                <td>TC6</td>
                <td>💥 ERROR</td>
                <td>0.4s</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>Failed to execute test case TC6: usage: main.py [-h] [--excel EXCEL] [--features FEATURES] [--output OUTPUT]
               [--model MODEL] [--convert-to-indonesian]
               [--browser {chromium,firefox,webkit}] [--test-cases TEST_CASES]
               [--parallel-mode] [--single-report]
main.py: error: unrecognized arguments: true true
</td>
            </tr>
            
            <tr>
                <td>TC7</td>
                <td>💥 ERROR</td>
                <td>0.4s</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>Failed to execute test case TC7: usage: main.py [-h] [--excel EXCEL] [--features FEATURES] [--output OUTPUT]
               [--model MODEL] [--convert-to-indonesian]
               [--browser {chromium,firefox,webkit}] [--test-cases TEST_CASES]
               [--parallel-mode] [--single-report]
main.py: error: unrecognized arguments: true true
</td>
            </tr>
            
            <tr>
                <td>TC8</td>
                <td>💥 ERROR</td>
                <td>0.4s</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>Failed to execute test case TC8: usage: main.py [-h] [--excel EXCEL] [--features FEATURES] [--output OUTPUT]
               [--model MODEL] [--convert-to-indonesian]
               [--browser {chromium,firefox,webkit}] [--test-cases TEST_CASES]
               [--parallel-mode] [--single-report]
main.py: error: unrecognized arguments: true true
</td>
            </tr>
            
            <tr>
                <td>TC9</td>
                <td>💥 ERROR</td>
                <td>0.4s</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>Failed to execute test case TC9: usage: main.py [-h] [--excel EXCEL] [--features FEATURES] [--output OUTPUT]
               [--model MODEL] [--convert-to-indonesian]
               [--browser {chromium,firefox,webkit}] [--test-cases TEST_CASES]
               [--parallel-mode] [--single-report]
main.py: error: unrecognized arguments: true true
</td>
            </tr>
            
                </tbody>
            </table>
        </div>
        
        <div class="footer">
            <p>Maintained by Test Automation Team. Find us on:</p>
            <div class="social-icons">
                <a href="https://github.com" target="_blank">🐙</a>
                <a href="https://twitter.com" target="_blank">🐦</a>
                <a href="https://linkedin.com" target="_blank">💼</a>
            </div>
        </div>
    </div>
</body>
</html>
        