
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON><PERSON> Pendidikan Automation Report</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }
        .header h2 {
            margin: 10px 0 0 0;
            font-size: 1.2em;
            font-weight: 300;
            opacity: 0.9;
        }
        .summary-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            padding: 30px;
            background: #f8f9fa;
        }
        .card {
            background: white;
            border-radius: 10px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            text-align: center;
        }
        .card h3 {
            margin: 0 0 20px 0;
            color: #333;
            font-size: 1.3em;
        }
        .donut-chart {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            margin: 0 auto 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5em;
            font-weight: bold;
            color: white;
            position: relative;
            cursor: pointer;
            background: conic-gradient(#28a745 0deg 0.0deg, #dc3545 0.0deg 360.0deg, #ffc107 360.0deg 360deg);
        }
        .donut-chart::before {
            content: '';
            position: absolute;
            width: 90px;
            height: 90px;
            border-radius: 50%;
            background: white;
            z-index: 1;
        }
        .donut-chart::after {
            content: '1';
            position: absolute;
            z-index: 2;
            color: #333;
            font-size: 2em;
            font-weight: bold;
        }
        /* Specific CSS for Scenarios card (second card) */
        .card:nth-child(2) .donut-chart::after {
            content: '1';
        }
        .donut-chart.passed {
            background: conic-gradient(#28a745 0deg 360deg);
        }
        .donut-chart.failed {
            background: conic-gradient(#dc3545 0deg 360deg);
        }
        .donut-chart.mixed {
            background: conic-gradient(#28a745 0deg 0.0deg, #dc3545 0.0deg 360.0deg, #ffc107 360.0deg 360deg);
        }
        .passed-tooltip, .failed-tooltip {
            position: fixed;
            background: rgba(0,0,0,0.9);
            color: white;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 12px;
            font-weight: 500;
            white-space: nowrap;
            pointer-events: none;
            opacity: 0;
            transition: opacity 0.2s ease;
            z-index: 1000;
            box-shadow: 0 3px 8px rgba(0,0,0,0.3);
            backdrop-filter: blur(4px);
        }
        .passed-tooltip {
            border-left: 3px solid #28a745;
        }
        .failed-tooltip {
            border-left: 3px solid #dc3545;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            text-align: left;
        }
        .stat-item {
            display: flex;
            justify-content: space-between;
            padding: 5px 0;
        }
        .stat-label {
            font-weight: 500;
            color: #666;
        }
        .stat-value {
            font-weight: bold;
            color: #333;
        }
        .test-results {
            padding: 30px;
        }
        .test-results h3 {
            margin: 0 0 20px 0;
            color: #333;
            font-size: 1.5em;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }
        th, td {
            padding: 15px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }
        th {
            background: #f8f9fa;
            font-weight: 600;
            color: #333;
        }
        tr:hover {
            background: #f8f9fa;
        }
        .footer {
            background: #f8f9fa;
            padding: 20px;
            text-align: center;
            color: #666;
            border-top: 1px solid #e9ecef;
        }
        .social-icons {
            margin-top: 10px;
        }
        .social-icons a {
            margin: 0 10px;
            font-size: 1.5em;
            text-decoration: none;
        }
        .social-icons a:hover {
            opacity: 0.7;
        }
        
        /* Error Details Styles */
        .error-details {
            margin-top: 10px;
            background: #fff5f5;
            border: 1px solid #fed7d7;
            border-radius: 6px;
            overflow: hidden;
        }
        .error-header {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 12px 15px;
            background: #fed7d7;
            border-left: 4px solid #e53e3e;
        }
        .error-icon {
            font-size: 1.1em;
        }
        .error-text {
            flex: 1;
            font-weight: 600;
            color: #c53030;
        }
        .quick-show-btn {
            background: #38a169;
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 4px;
            font-size: 0.8em;
            font-weight: 600;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        .quick-show-btn:hover {
            background: #2f855a;
        }
        .error-content {
            padding: 15px;
            background: white;
        }
        .error-comparison {
            border: 1px solid #e2e8f0;
            border-radius: 4px;
            overflow: hidden;
        }
        .expected-actual {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }
        .expected, .actual {
            padding: 10px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .expected {
            background: #f0fff4;
            border-bottom: 1px solid #e2e8f0;
        }
        .actual {
            background: #fff5f5;
        }
        .expected strong, .actual strong {
            min-width: 80px;
            font-size: 0.9em;
        }
        .expected .value {
            color: #38a169;
            font-family: monospace;
            background: #f0fff4;
            padding: 2px 6px;
            border-radius: 3px;
            border: 1px solid #c6f6d5;
        }
        .actual .value {
            color: #e53e3e;
            font-family: monospace;
            background: #fed7d7;
            padding: 2px 6px;
            border-radius: 3px;
            border: 1px solid #feb2b2;
        }
    </style>
    <script>
        function showPassedTooltip(event, passedText) {
            const tooltip = document.getElementById('passed-tooltip-summary');
            tooltip.innerHTML = passedText;
            tooltip.style.left = (event.clientX - tooltip.offsetWidth/2) + 'px';
            tooltip.style.top = (event.clientY - tooltip.offsetHeight - 10) + 'px';
            tooltip.style.opacity = '1';
        }
        
        function showFailedTooltip(event, failedText) {
            const tooltip = document.getElementById('failed-tooltip-summary');
            tooltip.innerHTML = failedText;
            tooltip.style.left = (event.clientX - tooltip.offsetWidth/2) + 'px';
            tooltip.style.top = (event.clientY - tooltip.offsetHeight - 10) + 'px';
            tooltip.style.opacity = '1';
        }
        
        function showPassedTooltip(event, text) {
            const tooltip = document.getElementById('passed-tooltip-summary');
            tooltip.textContent = text;
            tooltip.style.left = (event.clientX + 10) + 'px';
            tooltip.style.top = (event.clientY - 30) + 'px';
            tooltip.style.opacity = '1';
        }
        
        function showFailedTooltip(event, text) {
            const tooltip = document.getElementById('failed-tooltip-summary');
            tooltip.textContent = text;
            tooltip.style.left = (event.clientX + 10) + 'px';
            tooltip.style.top = (event.clientY - 30) + 'px';
            tooltip.style.opacity = '1';
        }
        
        function hideTooltip() {
            document.getElementById('passed-tooltip-summary').style.opacity = '0';
            document.getElementById('failed-tooltip-summary').style.opacity = '0';
        }
        
        function handleDonutHover(event, passedTests, failedTests) {
            const rect = event.target.getBoundingClientRect();
            const centerX = rect.left + rect.width / 2;
            const centerY = rect.top + rect.height / 2;
            const mouseX = event.clientX;
            const mouseY = event.clientY;
            
            // Calculate angle from center to mouse position
            const deltaX = mouseX - centerX;
            const deltaY = centerY - mouseY; // Invert Y axis
            const angle = Math.atan2(deltaY, deltaX) * 180 / Math.PI;
            const normalizedAngle = (angle + 360) % 360;
            
            // Calculate passed section angle
            const passedAngle = (passedTests / (passedTests + failedTests)) * 360;
            
            // Determine which section is being hovered
            if (normalizedAngle <= passedAngle) {
                // Hovering over passed section (green)
                showPassedTooltip(event, `Passed: ${passedTests}`);
                document.getElementById('failed-tooltip-summary').style.opacity = '0';
            } else {
                // Hovering over failed section (red)
                showFailedTooltip(event, `Failed: ${failedTests}`);
                document.getElementById('passed-tooltip-summary').style.opacity = '0';
            }
        }
        
        function toggleErrorDetails(errorId) {
            const errorContent = document.getElementById(errorId);
            if (errorContent.style.display === 'none') {
                errorContent.style.display = 'block';
            } else {
                errorContent.style.display = 'none';
            }
        }
        
        // Expand first scenario by default
        window.onload = function() {
            const firstScenario = document.querySelector('.scenario-item');
            if (firstScenario) {
                const scenarioId = firstScenario.querySelector('.scenario-header').getAttribute('onclick').match(/'([^']+)'/)[1];
                toggleScenario(scenarioId);
            }
        };
    </script>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Rumah Pendidikan Automation Report</h1>
            <h2>Comprehensive Test Automation Report</h2>
        </div>
        
        <div class="summary-cards">
            <div class="card">
                <h3>Features</h3>
                <div class="donut-chart mixed" 
                     onmousemove="handleDonutHover(event, 0, 1)" 
                     onmouseout="hideTooltip()">
                    1
                    <div class="passed-tooltip" id="passed-tooltip-summary"></div>
                    <div class="failed-tooltip" id="failed-tooltip-summary"></div>
                </div>
                <div class="stats-grid">
                    <div class="stat-item">
                        <span class="stat-label">STATUS</span>
                        <span class="stat-value">✅ Passed</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">PROGRESS</span>
                        <span class="stat-value">0.0%</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">STATUS</span>
                        <span class="stat-value">❌ Failed</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">PROGRESS</span>
                        <span class="stat-value">100.0%</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">STATUS</span>
                        <span class="stat-value">⏭️ Skipped</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">PROGRESS</span>
                        <span class="stat-value">0.0%</span>
                    </div>
                </div>
            </div>
            
            <div class="card">
                <h3>Scenarios</h3>
                <div class="donut-chart mixed" 
                     onmousemove="handleDonutHover(event, 0, 1)" 
                     onmouseout="hideTooltip()">
                    1
                    <div class="passed-tooltip" id="passed-tooltip-scenarios"></div>
                    <div class="failed-tooltip" id="failed-tooltip-scenarios"></div>
                </div>
                <div class="stats-grid">
                    <div class="stat-item">
                        <span class="stat-label">STATUS</span>
                        <span class="stat-value">✅ Passed</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">PROGRESS</span>
                        <span class="stat-value">0.0%</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">STATUS</span>
                        <span class="stat-value">❌ Failed</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">PROGRESS</span>
                        <span class="stat-value">100.0%</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">STATUS</span>
                        <span class="stat-value">⏭️ Skipped</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">PROGRESS</span>
                        <span class="stat-value">0.0%</span>
                    </div>
                </div>
            </div>
            
            <div class="card">
                <h3>Run Info</h3>
                <div style="text-align: left; margin-top: 20px;">
                    <div class="stat-item">
                        <span class="stat-label">🔗 Project</span>
                        <span class="stat-value">Rumah Pendidikan Automation Report</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">🕐 Generation Time</span>
                        <span class="stat-value">2025-08-11T08:26:06.743109Z</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">👤 OS User</span>
                        <span class="stat-value">jemz</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">⏱️ Duration</span>
                        <span class="stat-value">12 Seconds</span>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="test-results">
            <h3>Features Overview</h3>
            <table>
                <thead>
                    <tr>
                        <th>Feature Name</th>
                        <th>Status</th>
                        <th>Device</th>
                        <th>OS</th>
                        <th>Browser</th>
                        <th>Total</th>
                        <th>Passed</th>
                        <th>Failed</th>
                        <th>Undefined</th>
                    </tr>
                </thead>
                <tbody>
                    
        <tr>
            <td style="color: #007bff; font-weight: 500;">
                <a href="parallel_feature_20250811_082606.html" style="color: #007bff; text-decoration: none;">
                    Parallel Test Execution →
                </a>
            </td>
            <td>✅</td>
            <td>🖥️ Runner Machine</td>
            <td>🍎 darwin</td>
            <td>🌐 103</td>
            <td>1</td>
            <td>0</td>
            <td>1</td>
            <td>0</td>
        </tr>
        
                </tbody>
            </table>
        </div>
        
        <div class="footer">
            <p>Maintained by Test Automation Team.</p>
        </div>
    </div>
</body>
</html>
        