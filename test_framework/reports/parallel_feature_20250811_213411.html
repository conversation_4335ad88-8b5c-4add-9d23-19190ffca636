
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON>h Pendidikan Automation Report - Feature Details</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            min-height: 100vh;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
            position: relative;
        }
        .back-button {
            position: absolute;
            left: 30px;
            top: 50%;
            transform: translateY(-50%);
            color: white;
            text-decoration: none;
            font-size: 1.1em;
            display: flex;
            align-items: center;
            gap: 8px;
            transition: opacity 0.3s;
        }
        .back-button:hover {
            opacity: 0.8;
        }
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }
        .header h2 {
            margin: 10px 0 0 0;
            font-size: 1.2em;
            font-weight: 300;
            opacity: 0.9;
        }
        .content {
            padding: 30px;
        }
        
        /* FEATURE SECTION - Dedicated feature area */
        .feature-section {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .feature-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-bottom: 20px;
            max-height: 60px;
            overflow-y: auto;
        }
        .tag {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 8px 15px;
            border-radius: 20px;
            text-decoration: none;
            font-size: 0.9em;
            font-weight: 500;
            transition: transform 0.2s;
        }
        .tag:hover {
            transform: translateY(-2px);
        }
        .feature-info {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        .feature-details h2 {
            margin: 0 0 15px 0;
            color: #333;
            font-size: 1.8em;
        }
        .feature-description {
            color: #666;
            margin-bottom: 10px;
        }
        .metadata {
            color: #666;
            font-size: 0.9em;
        }
        
        /* Scenarios and Metadata Grid */
        .scenarios-overview {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }
        .scenarios-chart {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            text-align: center;
        }
        .scenarios-chart h3 {
            margin: 0 0 20px 0;
            color: #333;
            font-size: 1.4em;
        }
        .scenarios-chart-wrapper {
            position: relative;
            width: 120px;
            height: 120px;
            margin: 0 auto 20px;
        }
        .donut-chart {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5em;
            font-weight: bold;
            color: #333;
            cursor: pointer;
            transition: transform 0.2s;
        }
        .donut-chart:hover {
            transform: scale(1.05);
        }
        .donut-chart::before {
            content: '';
            position: absolute;
            width: 90px;
            height: 90px;
            border-radius: 50%;
            background: white;
            z-index: 1;
        }
        .donut-chart::after {
            content: '8';
            position: absolute;
            z-index: 2;
            color: #333;
            font-size: 2em;
            font-weight: bold;
        }
        .donut-chart.passed {
            background: conic-gradient(#28a745 0deg 360deg);
        }
        .donut-chart.failed {
            background: conic-gradient(#dc3545 0deg 360deg);
        }
        .donut-chart.mixed {
            background: conic-gradient(#28a745 0deg 135.0deg, #dc3545 135.0deg 360.0deg, #ffc107 360.0deg 360deg);
        }
        .scenarios-chart-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-top: 20px;
        }
        .chart-detail-section h4 {
            font-size: 14px;
            color: #6c757d;
            margin-bottom: 8px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        .status-list, .progress-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        .status-list li, .progress-list li {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 5px;
            font-size: 0.9em;
        }
        .status-list .passed, .progress-list .passed {
            color: #28a745;
        }
        .status-list .failed, .progress-list .failed {
            color: #dc3545;
        }
        .status-values, .progress-values {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }
        .status-values .passed, .progress-values .passed {
            color: #28a745;
            font-size: 0.9em;
            font-weight: 500;
        }
        .status-values .failed, .progress-values .failed {
            color: #dc3545;
            font-size: 0.9em;
            font-weight: 500;
        }
        
        /* Metadata Panel */
        .metadata-panel {
            background: white;
            border-radius: 10px;
            padding: 25px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .metadata-panel h3 {
            margin: 0 0 20px 0;
            color: #333;
            font-size: 1.4em;
        }
        .metadata-grid {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }
        .metadata-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #e9ecef;
        }
        .metadata-item:last-child {
            border-bottom: none;
        }
        .metadata-label {
            font-weight: 500;
            color: #666;
        }
        .metadata-value {
            color: #333;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .metadata-value i {
            font-size: 1.2em;
        }
        
        /* Filter Section */
        .filter-section {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .filter-section h3 {
            margin: 0 0 20px 0;
            color: #333;
            font-size: 1.4em;
        }
        .filter-buttons {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
        }
        .filter-btn {
            padding: 10px 20px;
            border: 2px solid #e9ecef;
            background: white;
            color: #666;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s;
            font-weight: 500;
        }
        .filter-btn.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-color: transparent;
        }
        .filter-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        /* Scenarios Section */
        .scenarios-section {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .scenarios-section h3 {
            margin: 0 0 20px 0;
            color: #333;
            font-size: 1.4em;
        }
        
        /* Scenario Items */
        .scenario-section {
            margin-bottom: 20px;
            border: 1px solid #e9ecef;
            border-radius: 10px;
            overflow: hidden;
        }
        .scenario-header {
            background: #f8f9fa;
            padding: 20px;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
            transition: background-color 0.3s;
        }
        .scenario-header:hover {
            background: #e9ecef;
        }
        .scenario-info {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        .scenario-icon {
            font-size: 1.5em;
        }
        .scenario-title {
            font-weight: 600;
            color: #333;
            font-size: 1.1em;
        }
        .scenario-status {
            padding: 5px 12px;
            border-radius: 15px;
            font-size: 0.8em;
            font-weight: 600;
            text-transform: uppercase;
        }
        .scenario-status.passed {
            background: #d4edda;
            color: #155724;
        }
        .scenario-status.failed {
            background: #f8d7da;
            color: #721c24;
        }
        .scenario-status.skipped {
            background: #fff3cd;
            color: #856404;
        }
        .scenario-meta {
            display: flex;
            align-items: center;
            gap: 15px;
            color: #666;
            font-size: 0.9em;
        }
        .toggle-arrow {
            font-size: 1.2em;
            transition: transform 0.3s;
        }
        .scenario-content {
            padding: 0;
            background: white;
        }
        .steps-container {
            padding: 20px;
        }
        
        /* Steps */
        .step {
            margin-bottom: 15px;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #e9ecef;
        }
        .step.passed {
            background: #f8fff9;
            border-left-color: #28a745;
        }
        .step.failed {
            background: #fff8f8;
            border-left-color: #dc3545;
        }
        .step.skipped {
            background: #fffef8;
            border-left-color: #ffc107;
        }
        .step-header {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 10px;
        }
        .step-icon {
            font-size: 1.2em;
        }
        .step-number {
            font-weight: 600;
            color: #666;
            min-width: 60px;
        }
        .step-text {
            color: #333;
            flex: 1;
        }
        
        /* LONG TEXT DISPLAY - Proper text wrapping for long assertions */
        .long-step-text {
            word-wrap: break-word;
            white-space: pre-wrap;
            line-height: 1.5;
            max-width: 100%;
            overflow-wrap: break-word;
        }
        
        /* STEP EXECUTION TEXT - Show "Step X execution" for successful steps */
        .step-execution-text {
            margin-top: 8px;
            padding: 5px 10px;
            background: #e8f5e8;
            color: #155724;
            border-radius: 5px;
            font-size: 0.85em;
            font-weight: 500;
            display: inline-block;
        }
        
        /* INTERACTIVE ERROR DETAILS - Expandable error details for failed steps */
        .error-details {
            margin-top: 10px;
            border: 1px solid #f8d7da;
            border-radius: 8px;
            overflow: hidden;
        }
        .error-summary {
            background: #f8d7da;
            padding: 12px 15px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 10px;
            color: #721c24;
            font-weight: 500;
            transition: background-color 0.3s;
        }
        .error-summary:hover {
            background: #f5c6cb;
        }
        .error-icon {
            font-size: 1.1em;
        }
        .toggle-icon {
            margin-left: auto;
            transition: transform 0.3s;
        }
        .error-content {
            padding: 15px;
            background: white;
        }
        .error-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 15px;
        }
        .error-item {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }
        .error-item strong {
            color: #721c24;
            font-size: 0.9em;
        }
        .expected-value, .actual-value {
            background: #f8f9fa;
            padding: 8px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 0.9em;
            word-break: break-all;
        }
        .error-message {
            margin-top: 10px;
        }
        .error-message strong {
            color: #721c24;
            display: block;
            margin-bottom: 5px;
        }
        .error-message pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            font-size: 0.85em;
            overflow-x: auto;
            white-space: pre-wrap;
            word-break: break-all;
        }
        
        /* Tooltip styles */
        .tooltip {
            position: absolute;
            background: rgba(0, 0, 0, 0.9);
            color: white;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 12px;
            pointer-events: none;
            z-index: 1000;
            white-space: nowrap;
            transform: translate(-50%, -100%);
            margin-top: -10px;
        }
        .tooltip::after {
            content: '';
            position: absolute;
            top: 100%;
            left: 50%;
            margin-left: -5px;
            border-width: 5px;
            border-style: solid;
            border-color: rgba(0, 0, 0, 0.9) transparent transparent transparent;
        }
        .passed-tooltip {
            border-left: 3px solid #28a745;
        }
        .failed-tooltip {
            border-left: 3px solid #dc3545;
        }
        
        /* Scenarios donut chart specific styles */
        .card:nth-child(2) .donut-chart {
            background: conic-gradient(#28a745 0deg 135.0deg, #dc3545 135.0deg 360.0deg, #ffc107 360.0deg 360deg);
        }
        .card:nth-child(2) .donut-chart::after {
            content: '8';
        }
        .card:nth-child(2) .donut-chart.passed {
            background: conic-gradient(#28a745 0deg 360deg);
        }
        .card:nth-child(2) .donut-chart.failed {
            background: conic-gradient(#dc3545 0deg 360deg);
        }
        .card:nth-child(2) .donut-chart.mixed {
            background: conic-gradient(#28a745 0deg 135.0deg, #dc3545 135.0deg 360.0deg, #ffc107 360.0deg 360deg);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <a href="parallel_comprehensive_20250811_213411.html" class="back-button">
                <i class="fas fa-arrow-left"></i> Summary
            </a>
            <h1>Rumah Pendidikan Automation Report</h1>
            <h2>Feature: Test Execution</h2>
        </div>
        
        <div class="content">
            <!-- Feature Details Section -->
            <div class="feature-section">
                <div class="feature-tags">
                    <a href="#TEST_RP-124" class="tag">TEST_RP-124</a><a href="#TEST_RP-299" class="tag">TEST_RP-299</a><a href="#TEST_RP-305" class="tag">TEST_RP-305</a><a href="#TEST_RP-355" class="tag">TEST_RP-355</a><a href="#TEST_RP-356" class="tag">TEST_RP-356</a><a href="#TEST_RP-357" class="tag">TEST_RP-357</a><a href="#TEST_RP-543" class="tag">TEST_RP-543</a><a href="#TEST_RP-98" class="tag">TEST_RP-98</a>
                </div>
                <div class="feature-info">
                    <div class="feature-details">
                        <h2>Feature: TC 188</h2>
                        <p class="feature-description"><strong>Description:</strong> Regression Test</p>
                        <div><strong>File name:</strong> rumdik_regression_test.feature</div>
                    </div>
                    <div class="metadata">
                        <div><strong>Relative path:</strong> test_framework/features/rumdik_regression_test.feature</div>
                    </div>
                </div>
            </div>
            
            <!-- Scenarios and Metadata Grid -->
            <div class="scenarios-overview">
                <div class="scenarios-chart">
                    <h3>Scenarios</h3>
                    <!-- Donut Chart positioned above status/progress -->
                    <div class="scenarios-chart-wrapper">
                        <div class="donut-chart mixed" onmouseover="handleDonutHover(event, 3, 5)" onmouseout="hideTooltip()">
                            8
                        </div>
                    </div>
                    <!-- Status and Progress details below donut chart -->
                    <div class="scenarios-chart-details">
                        <div class="chart-detail-section">
                            <h4>Status</h4>
                            <div class="status-values">
                                <div class="passed">✅ Passed: 3</div>
                                <div class="failed">❌ Failed: 5</div>
                            </div>
                        </div>
                        <div class="chart-detail-section">
                            <h4>Progress</h4>
                            <div class="progress-values">
                                <div class="passed">✅ 37.5%</div>
                                <div class="failed">❌ 62.5%</div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="metadata-panel">
                    <h3>Metadata</h3>
                    <div class="metadata-grid">
                        <div class="metadata-item">
                            <div class="metadata-label">Device</div>
                            <div class="metadata-value">Runner Machine</div>
                        </div>
                        <div class="metadata-item">
                            <div class="metadata-label">OS</div>
                            <div class="metadata-value">
                                <i class="fab fa-apple"></i>
                                Darwin
                            </div>
                        </div>
                        <div class="metadata-item">
                            <div class="metadata-label">Browser</div>
                            <div class="metadata-value">
                                <i class="fab fa-chrome"></i>
                                Chrome 103
                            </div>
                        </div>
                        <div class="metadata-item">
                            <div class="metadata-label">Execution Time</div>
                            <div class="metadata-value">
                                <i class="fas fa-clock"></i>
                                118.0s
                            </div>
                        </div>
                        <div class="metadata-item">
                            <div class="metadata-label">Total Tests</div>
                            <div class="metadata-value">
                                <i class="fas fa-list"></i>
                                8
                            </div>
                        </div>
                        <div class="metadata-item">
                            <div class="metadata-label">Success Rate</div>
                            <div class="metadata-value">
                                <i class="fas fa-chart-line"></i>
                                37.5%
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Filter Section -->
            <div class="filter-section">
                <h3>Filter Scenarios</h3>
                <div class="filter-buttons">
                    <button class="filter-btn active" onclick="filterScenarios('all')">All (8)</button>
                    <button class="filter-btn" onclick="filterScenarios('passed')">Passed (3)</button>
                    <button class="filter-btn" onclick="filterScenarios('failed')">Failed (5)</button>
                </div>
            </div>
            
            <!-- Scenarios Section -->
            <div class="scenarios-section">
                <h3>Test Scenarios</h3>
                
            <div class="scenario-section">
                <div class="scenario-header" onclick="toggleScenario(this)">
                    <div class="scenario-info">
                        <span class="scenario-icon">❌</span>
                        <span class="scenario-title">TEST_RP-124</span>
                        <span class="scenario-status failed">FAILED</span>
                    </div>
                    <div class="scenario-meta">
                        <span class="execution-time">⏱️ 5.2s</span>
                        <span class="steps-summary">📊 4/6 passed</span>
                        <span class="toggle-arrow">▼</span>
                    </div>
                </div>
                <div class="scenario-content" style="display: none;">
                    <div class="steps-container">
                        
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 1</span>
                        <span class="step-text">Given Mengakses halaman Beranda pada aplikasi Rumah Pendidikan</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Navigated to beranda pada aplikasi rumah pendidikan</div>
                        
                    </div>
                </div>
                
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 2</span>
                        <span class="step-text">When Klik "Ruang Pemerintah"</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Clicked ruang pemerintah</div>
                        
                    </div>
                </div>
                
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 3</span>
                        <span class="step-text">And Menampilkan halaman "Ruang Pemerintah"</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Asserted ruang pemerintah</div>
                        
                    </div>
                </div>
                
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 4</span>
                        <span class="step-text">And Klik "Akun Pendidikan"</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Clicked akun pendidikan</div>
                        
                    </div>
                </div>
                
                <div class="step failed">
                    <div class="step-header">
                        <span class="step-icon">❌</span>
                        <span class="step-number">Step 5</span>
                        <span class="step-text">Then diarahkan ke Webview Website "https://belajar.id/"</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: FAILED</div>
                        <div class="step-result">Result: Assertion failed for https://belajar.id/</div>
                        <div class="step-error">Error: Expected: https://belajar.id/, Actual: Assertion error: 'NoneType' object has no attribute 'lower'</div>
                    </div>
                </div>
                
                <div class="step failed">
                    <div class="step-header">
                        <span class="step-icon">❌</span>
                        <span class="step-number">Step 6</span>
                        <span class="step-text">And Diarahkan ke halaman Webview Website Belajar.id</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: FAILED</div>
                        <div class="step-result">Result: Assertion failed for belajar.id</div>
                        <div class="step-error">Error: Expected: belajar.id, Actual: Content visibility assertion failed</div>
                    </div>
                </div>
                
                    </div>
                </div>
            </div>
            
            <div class="scenario-section">
                <div class="scenario-header" onclick="toggleScenario(this)">
                    <div class="scenario-info">
                        <span class="scenario-icon">✅</span>
                        <span class="scenario-title">TEST_RP-299</span>
                        <span class="scenario-status passed">PASSED</span>
                    </div>
                    <div class="scenario-meta">
                        <span class="execution-time">⏱️ 2.7s</span>
                        <span class="steps-summary">📊 4/4 passed</span>
                        <span class="toggle-arrow">▼</span>
                    </div>
                </div>
                <div class="scenario-content" style="display: none;">
                    <div class="steps-container">
                        
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 1</span>
                        <span class="step-text">Given the user navigates to "https://rumah-baru.staging.belajar.id/"</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Navigated to https://rumah-baru.staging.belajar.id/</div>
                        
                    </div>
                </div>
                
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 2</span>
                        <span class="step-text">Then the header should display the "Rumah Pendidikan" logo on the left</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Navigated to on the left</div>
                        
                    </div>
                </div>
                
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 3</span>
                        <span class="step-text">And a search box on the top</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Asserted search box</div>
                        
                    </div>
                </div>
                
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 4</span>
                        <span class="step-text">And a "Masuk" button on the right</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Asserted button</div>
                        
                    </div>
                </div>
                
                    </div>
                </div>
            </div>
            
            <div class="scenario-section">
                <div class="scenario-header" onclick="toggleScenario(this)">
                    <div class="scenario-info">
                        <span class="scenario-icon">✅</span>
                        <span class="scenario-title">TEST_RP-305</span>
                        <span class="scenario-status passed">PASSED</span>
                    </div>
                    <div class="scenario-meta">
                        <span class="execution-time">⏱️ 116.2s</span>
                        <span class="steps-summary">📊 15/15 passed</span>
                        <span class="toggle-arrow">▼</span>
                    </div>
                </div>
                <div class="scenario-content" style="display: none;">
                    <div class="steps-container">
                        
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 1</span>
                        <span class="step-text">Given the user is on the homepage</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Navigated to home</div>
                        
                    </div>
                </div>
                
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 2</span>
                        <span class="step-text">When I click "masuk"</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Clicked masuk</div>
                        
                    </div>
                </div>
                
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 3</span>
                        <span class="step-text">And selects the "Masuk" option</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Clicked Masuk</div>
                        
                    </div>
                </div>
                
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 4</span>
                        <span class="step-text">And in the page https://rumah-baru.staging.belajar.id/login i click "Masuk"</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Navigate and click completed for masuk</div>
                        
                    </div>
                </div>
                
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 5</span>
                        <span class="step-text">And in the column email i input user guru email address</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Typed 'None' in email_field</div>
                        
                    </div>
                </div>
                
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 6</span>
                        <span class="step-text">And i click Next</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Clicked next</div>
                        
                    </div>
                </div>
                
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 7</span>
                        <span class="step-text">And in the column password i input password guru</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Typed 'None' in password_field</div>
                        
                    </div>
                </div>
                
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 8</span>
                        <span class="step-text">And i click Next</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Clicked next</div>
                        
                    </div>
                </div>
                
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 9</span>
                        <span class="step-text">And i click "Continue"</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Clicked continue</div>
                        
                    </div>
                </div>
                
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 10</span>
                        <span class="step-text">Then the user should be logged in</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Asserted user</div>
                        
                    </div>
                </div>
                
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 11</span>
                        <span class="step-text">Then And the navbar on the top right should display the user's initial name</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Asserted user's initial name</div>
                        
                    </div>
                </div>
                
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 12</span>
                        <span class="step-text">When I click the user's initial name</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Clicked user_initial_name</div>
                        
                    </div>
                </div>
                
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 13</span>
                        <span class="step-text">And selects "Logout"</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Clicked logout</div>
                        
                    </div>
                </div>
                
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 14</span>
                        <span class="step-text">Then the user should be logged out</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Asserted user</div>
                        
                    </div>
                </div>
                
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 15</span>
                        <span class="step-text">Then And the navbar on the top right should display the "Masuk" button again</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Asserted masuk</div>
                        
                    </div>
                </div>
                
                    </div>
                </div>
            </div>
            
            <div class="scenario-section">
                <div class="scenario-header" onclick="toggleScenario(this)">
                    <div class="scenario-info">
                        <span class="scenario-icon">✅</span>
                        <span class="scenario-title">TEST_RP-355</span>
                        <span class="scenario-status failed">FAILED</span>
                    </div>
                    <div class="scenario-meta">
                        <span class="execution-time">⏱️ 27.8s</span>
                        <span class="steps-summary">📊 3/4 passed</span>
                        <span class="toggle-arrow">▼</span>
                    </div>
                </div>
                <div class="scenario-content" style="display: none;">
                    <div class="steps-container">
                        
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 1</span>
                        <span class="step-text">Given the user is on the Rumah Pendidikan homepage</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Navigated to rumah pendidikan home</div>
                        
                    </div>
                </div>
                
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 2</span>
                        <span class="step-text">When I click "Ruang Mitra"</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Clicked ruang mitra</div>
                        
                    </div>
                </div>
                
                <div class="step failed">
                    <div class="step-header">
                        <span class="step-icon">❌</span>
                        <span class="step-number">Step 3</span>
                        <span class="step-text">When I click "Relawan Pendidikan" on the "Ruang Mitra"</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: FAILED</div>
                        <div class="step-result">Result: Failed to click relawan pendidikan</div>
                        <div class="step-error">Error: Click failed for relawan pendidikan</div>
                    </div>
                </div>
                
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 4</span>
                        <span class="step-text">Then user direct to Relawan Pendidikan Pages "https://rumah.pendidikan.go.id/relawan-pendidikan.html"</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Verified URL: relawan pendidikan pages </div>
                        
                    </div>
                </div>
                
                    </div>
                </div>
            </div>
            
            <div class="scenario-section">
                <div class="scenario-header" onclick="toggleScenario(this)">
                    <div class="scenario-info">
                        <span class="scenario-icon">❌</span>
                        <span class="scenario-title">TEST_RP-356</span>
                        <span class="scenario-status failed">FAILED</span>
                    </div>
                    <div class="scenario-meta">
                        <span class="execution-time">⏱️ 5.4s</span>
                        <span class="steps-summary">📊 1/4 passed</span>
                        <span class="toggle-arrow">▼</span>
                    </div>
                </div>
                <div class="scenario-content" style="display: none;">
                    <div class="steps-container">
                        
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 1</span>
                        <span class="step-text">Given the user is on the Rumah Pendidikan homepage</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Navigated to rumah pendidikan home</div>
                        
                    </div>
                </div>
                
                <div class="step failed">
                    <div class="step-header">
                        <span class="step-icon">❌</span>
                        <span class="step-number">Step 2</span>
                        <span class="step-text">When I enters a keyword in the search box</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: FAILED</div>
                        <div class="step-result">Result: Failed to type 'None' in None</div>
                        <div class="step-error">Error: Type failed for None</div>
                    </div>
                </div>
                
                <div class="step skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-number">Step 3</span>
                        <span class="step-text">And the keyword matches a title or description of one or more services</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: SKIPPED</div>
                        <div class="step-result">Result: Unknown or unsupported action for step: the keyword matches a title or description of one or more services</div>
                        <div class="step-error">Error: Unknown or unsupported action</div>
                    </div>
                </div>
                
                <div class="step failed">
                    <div class="step-header">
                        <span class="step-icon">❌</span>
                        <span class="step-number">Step 4</span>
                        <span class="step-text">Then a list of matching services should be displayed to the user</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: FAILED</div>
                        <div class="step-result">Result: Assertion failed for assertion</div>
                        <div class="step-error">Error: Expected: assertion, Actual: Content visibility assertion failed</div>
                    </div>
                </div>
                
                    </div>
                </div>
            </div>
            
            <div class="scenario-section">
                <div class="scenario-header" onclick="toggleScenario(this)">
                    <div class="scenario-info">
                        <span class="scenario-icon">❌</span>
                        <span class="scenario-title">TEST_RP-357</span>
                        <span class="scenario-status failed">FAILED</span>
                    </div>
                    <div class="scenario-meta">
                        <span class="execution-time">⏱️ 0.8s</span>
                        <span class="steps-summary">📊 3/4 passed</span>
                        <span class="toggle-arrow">▼</span>
                    </div>
                </div>
                <div class="scenario-content" style="display: none;">
                    <div class="steps-container">
                        
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 1</span>
                        <span class="step-text">Given the user is on the Rumah Pendidikan homepage</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Navigated to rumah pendidikan home</div>
                        
                    </div>
                </div>
                
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 2</span>
                        <span class="step-text">When I enters a keyword in the search box that does not match any service title or description</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Asserted no_results</div>
                        
                    </div>
                </div>
                
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 3</span>
                        <span class="step-text">And the keyword does not match any service title or description</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Asserted no_results</div>
                        
                    </div>
                </div>
                
                <div class="step failed">
                    <div class="step-header">
                        <span class="step-icon">❌</span>
                        <span class="step-number">Step 4</span>
                        <span class="step-text">Then the system should display a message stating "Tidak Ada Hasil yang Sesuai"</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: FAILED</div>
                        <div class="step-result">Result: Assertion failed for message stating </div>
                        <div class="step-error">Error: Expected: message stating , Actual: Content visibility assertion failed</div>
                    </div>
                </div>
                
                    </div>
                </div>
            </div>
            
            <div class="scenario-section">
                <div class="scenario-header" onclick="toggleScenario(this)">
                    <div class="scenario-info">
                        <span class="scenario-icon">✅</span>
                        <span class="scenario-title">TEST_RP-543</span>
                        <span class="scenario-status passed">PASSED</span>
                    </div>
                    <div class="scenario-meta">
                        <span class="execution-time">⏱️ 1.3s</span>
                        <span class="steps-summary">📊 3/3 passed</span>
                        <span class="toggle-arrow">▼</span>
                    </div>
                </div>
                <div class="scenario-content" style="display: none;">
                    <div class="steps-container">
                        
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 1</span>
                        <span class="step-text">Given the user is on the homepage or news section of Rumah Pendidikan</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Asserted homepage</div>
                        
                    </div>
                </div>
                
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 2</span>
                        <span class="step-text">When the page loads successfully</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Waited 1.0 seconds</div>
                        
                    </div>
                </div>
                
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 3</span>
                        <span class="step-text">Then I should see a list of available "Informasi Untuk Anda" displayed with thumbnails and titles</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Asserted informasi untuk anda list displayed</div>
                        
                    </div>
                </div>
                
                    </div>
                </div>
            </div>
            
            <div class="scenario-section">
                <div class="scenario-header" onclick="toggleScenario(this)">
                    <div class="scenario-info">
                        <span class="scenario-icon">✅</span>
                        <span class="scenario-title">TEST_RP-98</span>
                        <span class="scenario-status failed">FAILED</span>
                    </div>
                    <div class="scenario-meta">
                        <span class="execution-time">⏱️ 26.7s</span>
                        <span class="steps-summary">📊 5/6 passed</span>
                        <span class="toggle-arrow">▼</span>
                    </div>
                </div>
                <div class="scenario-content" style="display: none;">
                    <div class="steps-container">
                        
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 1</span>
                        <span class="step-text">Given Mengakses halaman Beranda pada aplikasi Rumah Pendidikan</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Navigated to beranda pada aplikasi rumah pendidikan</div>
                        
                    </div>
                </div>
                
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 2</span>
                        <span class="step-text">When Klik "Ruang Sekolah"</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Clicked ruang sekolah</div>
                        
                    </div>
                </div>
                
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 3</span>
                        <span class="step-text">And Menampilkan halaman "Ruang Sekolah"</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Asserted ruang sekolah</div>
                        
                    </div>
                </div>
                
                <div class="step failed">
                    <div class="step-header">
                        <span class="step-icon">❌</span>
                        <span class="step-number">Step 4</span>
                        <span class="step-text">And Klik "Profil Sekolah"</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: FAILED</div>
                        <div class="step-result">Result: Failed to click profil sekolah</div>
                        <div class="step-error">Error: Click failed for profil sekolah</div>
                    </div>
                </div>
                
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 5</span>
                        <span class="step-text">Then Tidak diarahkan ke Webview "https://sekolah.data.kemendikdasmen.go.id/"</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Verified URL: webview </div>
                        
                    </div>
                </div>
                
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 6</span>
                        <span class="step-text">And Gagal Diarahkan ke halaman Webview Website Sekolah Kita</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Verified URL: halaman webview website sekolah kita</div>
                        
                    </div>
                </div>
                
                    </div>
                </div>
            </div>
            
            </div>
        </div>
    </div>
    
    <!-- Tooltip container -->
    <div id="tooltip" class="tooltip" style="display: none;"></div>
    
    <script>
        // DONUT CHART TOOLTIPS - Show passed/failed counts on hover
        function handleDonutHover(event, passed, failed) {
            const tooltip = document.getElementById('tooltip');
            const rect = event.target.getBoundingClientRect();
            const x = rect.left + rect.width / 2;
            const y = rect.top;
            
            tooltip.innerHTML = `Passed: ${passed}<br>Failed: ${failed}`;
            tooltip.style.left = x + 'px';
            tooltip.style.top = y + 'px';
            tooltip.style.display = 'block';
        }
        
        function hideTooltip() {
            document.getElementById('tooltip').style.display = 'none';
        }
        
        // SCENARIOS CLOSED BY DEFAULT - Collapsible scenario sections
        function toggleScenario(header) {
            const content = header.nextElementSibling;
            const arrow = header.querySelector('.toggle-arrow');
            
            if (content.style.display === 'none' || content.style.display === '') {
                content.style.display = 'block';
                arrow.style.transform = 'rotate(180deg)';
            } else {
                content.style.display = 'none';
                arrow.style.transform = 'rotate(0deg)';
            }
        }
        
        // INTERACTIVE ERROR DETAILS - Expandable error details for failed steps
        function toggleErrorDetails(errorDetails) {
            const content = errorDetails.querySelector('.error-content');
            const toggleIcon = errorDetails.querySelector('.toggle-icon');
            
            if (content.style.display === 'none' || content.style.display === '') {
                content.style.display = 'block';
                toggleIcon.textContent = '▲';
            } else {
                content.style.display = 'none';
                toggleIcon.textContent = '▼';
            }
        }
        
        // Filter functionality
        function filterScenarios(status) {
            const scenarios = document.querySelectorAll('.scenario-section');
            const buttons = document.querySelectorAll('.filter-btn');
            
            // Update button states
            buttons.forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');
            
            scenarios.forEach(scenario => {
                const scenarioStatus = scenario.querySelector('.scenario-status').textContent.toLowerCase();
                
                if (status === 'all' || scenarioStatus === status) {
                    scenario.style.display = 'block';
                } else {
                    scenario.style.display = 'none';
                }
            });
        }
    </script>
</body>
</html>
        