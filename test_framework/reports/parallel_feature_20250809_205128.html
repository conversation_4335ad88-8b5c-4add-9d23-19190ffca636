
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON>h Pendidikan Automation Report - Feature Details</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            min-height: 100vh;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
            position: relative;
        }
        .back-button {
            position: absolute;
            left: 30px;
            top: 50%;
            transform: translateY(-50%);
            color: white;
            text-decoration: none;
            font-size: 1.1em;
            display: flex;
            align-items: center;
            gap: 8px;
            transition: opacity 0.3s;
        }
        .back-button:hover {
            opacity: 0.8;
        }
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }
        .header h2 {
            margin: 10px 0 0 0;
            font-size: 1.2em;
            font-weight: 300;
            opacity: 0.9;
        }
        .content {
            padding: 30px;
        }
        
        /* FEATURE SECTION - Dedicated feature area */
        .feature-section {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .feature-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-bottom: 20px;
            max-height: 60px;
            overflow-y: auto;
        }
        .tag {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 8px 15px;
            border-radius: 20px;
            text-decoration: none;
            font-size: 0.9em;
            font-weight: 500;
            transition: transform 0.2s;
        }
        .tag:hover {
            transform: translateY(-2px);
        }
        .feature-info {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        .feature-details h2 {
            margin: 0 0 15px 0;
            color: #333;
            font-size: 1.8em;
        }
        .feature-description {
            color: #666;
            margin-bottom: 10px;
        }
        .metadata {
            color: #666;
            font-size: 0.9em;
        }
        
        /* Scenarios and Metadata Grid */
        .scenarios-overview {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }
        .scenarios-chart {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            text-align: center;
        }
        .scenarios-chart h3 {
            margin: 0 0 20px 0;
            color: #333;
            font-size: 1.4em;
        }
        .scenarios-chart-wrapper {
            position: relative;
            width: 120px;
            height: 120px;
            margin: 0 auto 20px;
        }
        .donut-chart {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5em;
            font-weight: bold;
            color: #333;
            cursor: pointer;
            transition: transform 0.2s;
        }
        .donut-chart:hover {
            transform: scale(1.05);
        }
        .donut-chart::before {
            content: '';
            position: absolute;
            width: 90px;
            height: 90px;
            border-radius: 50%;
            background: white;
            z-index: 1;
        }
        .donut-chart::after {
            content: '59';
            position: absolute;
            z-index: 2;
            color: #333;
            font-size: 2em;
            font-weight: bold;
        }
        .donut-chart.passed {
            background: conic-gradient(#28a745 0deg 360deg);
        }
        .donut-chart.failed {
            background: conic-gradient(#dc3545 0deg 360deg);
        }
        .donut-chart.mixed {
            background: conic-gradient(#28a745 0deg 0.0deg, #dc3545 0.0deg 360.0deg, #ffc107 360.0deg 360deg);
        }
        .scenarios-chart-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-top: 20px;
        }
        .chart-detail-section h4 {
            font-size: 14px;
            color: #6c757d;
            margin-bottom: 8px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        .status-list, .progress-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        .status-list li, .progress-list li {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 5px;
            font-size: 0.9em;
        }
        .status-list .passed, .progress-list .passed {
            color: #28a745;
        }
        .status-list .failed, .progress-list .failed {
            color: #dc3545;
        }
        .status-values, .progress-values {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }
        .status-values .passed, .progress-values .passed {
            color: #28a745;
            font-size: 0.9em;
            font-weight: 500;
        }
        .status-values .failed, .progress-values .failed {
            color: #dc3545;
            font-size: 0.9em;
            font-weight: 500;
        }
        
        /* Metadata Panel */
        .metadata-panel {
            background: white;
            border-radius: 10px;
            padding: 25px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .metadata-panel h3 {
            margin: 0 0 20px 0;
            color: #333;
            font-size: 1.4em;
        }
        .metadata-grid {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }
        .metadata-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #e9ecef;
        }
        .metadata-item:last-child {
            border-bottom: none;
        }
        .metadata-label {
            font-weight: 500;
            color: #666;
        }
        .metadata-value {
            color: #333;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .metadata-value i {
            font-size: 1.2em;
        }
        
        /* Filter Section */
        .filter-section {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .filter-section h3 {
            margin: 0 0 20px 0;
            color: #333;
            font-size: 1.4em;
        }
        .filter-buttons {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
        }
        .filter-btn {
            padding: 10px 20px;
            border: 2px solid #e9ecef;
            background: white;
            color: #666;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s;
            font-weight: 500;
        }
        .filter-btn.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-color: transparent;
        }
        .filter-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        /* Scenarios Section */
        .scenarios-section {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .scenarios-section h3 {
            margin: 0 0 20px 0;
            color: #333;
            font-size: 1.4em;
        }
        
        /* Scenario Items */
        .scenario-section {
            margin-bottom: 20px;
            border: 1px solid #e9ecef;
            border-radius: 10px;
            overflow: hidden;
        }
        .scenario-header {
            background: #f8f9fa;
            padding: 20px;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
            transition: background-color 0.3s;
        }
        .scenario-header:hover {
            background: #e9ecef;
        }
        .scenario-info {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        .scenario-icon {
            font-size: 1.5em;
        }
        .scenario-title {
            font-weight: 600;
            color: #333;
            font-size: 1.1em;
        }
        .scenario-status {
            padding: 5px 12px;
            border-radius: 15px;
            font-size: 0.8em;
            font-weight: 600;
            text-transform: uppercase;
        }
        .scenario-status.passed {
            background: #d4edda;
            color: #155724;
        }
        .scenario-status.failed {
            background: #f8d7da;
            color: #721c24;
        }
        .scenario-status.skipped {
            background: #fff3cd;
            color: #856404;
        }
        .scenario-meta {
            display: flex;
            align-items: center;
            gap: 15px;
            color: #666;
            font-size: 0.9em;
        }
        .toggle-arrow {
            font-size: 1.2em;
            transition: transform 0.3s;
        }
        .scenario-content {
            padding: 0;
            background: white;
        }
        .steps-container {
            padding: 20px;
        }
        
        /* Steps */
        .step {
            margin-bottom: 15px;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #e9ecef;
        }
        .step.passed {
            background: #f8fff9;
            border-left-color: #28a745;
        }
        .step.failed {
            background: #fff8f8;
            border-left-color: #dc3545;
        }
        .step.skipped {
            background: #fffef8;
            border-left-color: #ffc107;
        }
        .step-header {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 10px;
        }
        .step-icon {
            font-size: 1.2em;
        }
        .step-number {
            font-weight: 600;
            color: #666;
            min-width: 60px;
        }
        .step-text {
            color: #333;
            flex: 1;
        }
        
        /* LONG TEXT DISPLAY - Proper text wrapping for long assertions */
        .long-step-text {
            word-wrap: break-word;
            white-space: pre-wrap;
            line-height: 1.5;
            max-width: 100%;
            overflow-wrap: break-word;
        }
        
        /* STEP EXECUTION TEXT - Show "Step X execution" for successful steps */
        .step-execution-text {
            margin-top: 8px;
            padding: 5px 10px;
            background: #e8f5e8;
            color: #155724;
            border-radius: 5px;
            font-size: 0.85em;
            font-weight: 500;
            display: inline-block;
        }
        
        /* INTERACTIVE ERROR DETAILS - Expandable error details for failed steps */
        .error-details {
            margin-top: 10px;
            border: 1px solid #f8d7da;
            border-radius: 8px;
            overflow: hidden;
        }
        .error-summary {
            background: #f8d7da;
            padding: 12px 15px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 10px;
            color: #721c24;
            font-weight: 500;
            transition: background-color 0.3s;
        }
        .error-summary:hover {
            background: #f5c6cb;
        }
        .error-icon {
            font-size: 1.1em;
        }
        .toggle-icon {
            margin-left: auto;
            transition: transform 0.3s;
        }
        .error-content {
            padding: 15px;
            background: white;
        }
        .error-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 15px;
        }
        .error-item {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }
        .error-item strong {
            color: #721c24;
            font-size: 0.9em;
        }
        .expected-value, .actual-value {
            background: #f8f9fa;
            padding: 8px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 0.9em;
            word-break: break-all;
        }
        .error-message {
            margin-top: 10px;
        }
        .error-message strong {
            color: #721c24;
            display: block;
            margin-bottom: 5px;
        }
        .error-message pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            font-size: 0.85em;
            overflow-x: auto;
            white-space: pre-wrap;
            word-break: break-all;
        }
        
        /* Tooltip styles */
        .tooltip {
            position: absolute;
            background: rgba(0, 0, 0, 0.9);
            color: white;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 12px;
            pointer-events: none;
            z-index: 1000;
            white-space: nowrap;
            transform: translate(-50%, -100%);
            margin-top: -10px;
        }
        .tooltip::after {
            content: '';
            position: absolute;
            top: 100%;
            left: 50%;
            margin-left: -5px;
            border-width: 5px;
            border-style: solid;
            border-color: rgba(0, 0, 0, 0.9) transparent transparent transparent;
        }
        .passed-tooltip {
            border-left: 3px solid #28a745;
        }
        .failed-tooltip {
            border-left: 3px solid #dc3545;
        }
        
        /* Scenarios donut chart specific styles */
        .card:nth-child(2) .donut-chart {
            background: conic-gradient(#28a745 0deg 0.0deg, #dc3545 0.0deg 360.0deg, #ffc107 360.0deg 360deg);
        }
        .card:nth-child(2) .donut-chart::after {
            content: '59';
        }
        .card:nth-child(2) .donut-chart.passed {
            background: conic-gradient(#28a745 0deg 360deg);
        }
        .card:nth-child(2) .donut-chart.failed {
            background: conic-gradient(#dc3545 0deg 360deg);
        }
        .card:nth-child(2) .donut-chart.mixed {
            background: conic-gradient(#28a745 0deg 0.0deg, #dc3545 0.0deg 360.0deg, #ffc107 360.0deg 360deg);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <a href="parallel_comprehensive_20250809_205128.html" class="back-button">
                <i class="fas fa-arrow-left"></i> Summary
            </a>
            <h1>Rumah Pendidikan Automation Report</h1>
            <h2>Feature: Test Execution</h2>
        </div>
        
        <div class="content">
            <!-- Feature Details Section -->
            <div class="feature-section">
                <div class="feature-tags">
                    <a href="#TEST_RP-100" class="tag">TEST_RP-100</a><a href="#TEST_RP-101" class="tag">TEST_RP-101</a><a href="#TEST_RP-102" class="tag">TEST_RP-102</a><a href="#TEST_RP-112" class="tag">TEST_RP-112</a><a href="#TEST_RP-113" class="tag">TEST_RP-113</a><a href="#TEST_RP-124" class="tag">TEST_RP-124</a><a href="#TEST_RP-138" class="tag">TEST_RP-138</a><a href="#TEST_RP-142" class="tag">TEST_RP-142</a><a href="#TEST_RP-148" class="tag">TEST_RP-148</a><a href="#TEST_RP-295" class="tag">TEST_RP-295</a><a href="#TEST_RP-296" class="tag">TEST_RP-296</a><a href="#TEST_RP-299" class="tag">TEST_RP-299</a><a href="#TEST_RP-300" class="tag">TEST_RP-300</a><a href="#TEST_RP-301" class="tag">TEST_RP-301</a><a href="#TEST_RP-302" class="tag">TEST_RP-302</a><a href="#TEST_RP-305" class="tag">TEST_RP-305</a><a href="#TEST_RP-307" class="tag">TEST_RP-307</a><a href="#TEST_RP-309" class="tag">TEST_RP-309</a><a href="#TEST_RP-327" class="tag">TEST_RP-327</a><a href="#TEST_RP-328" class="tag">TEST_RP-328</a><a href="#TEST_RP-329" class="tag">TEST_RP-329</a><a href="#TEST_RP-330" class="tag">TEST_RP-330</a><a href="#TEST_RP-331" class="tag">TEST_RP-331</a><a href="#TEST_RP-332" class="tag">TEST_RP-332</a><a href="#TEST_RP-333" class="tag">TEST_RP-333</a><a href="#TEST_RP-334" class="tag">TEST_RP-334</a><a href="#TEST_RP-335" class="tag">TEST_RP-335</a><a href="#TEST_RP-336" class="tag">TEST_RP-336</a><a href="#TEST_RP-337" class="tag">TEST_RP-337</a><a href="#TEST_RP-338" class="tag">TEST_RP-338</a><a href="#TEST_RP-339" class="tag">TEST_RP-339</a><a href="#TEST_RP-340" class="tag">TEST_RP-340</a><a href="#TEST_RP-342" class="tag">TEST_RP-342</a><a href="#TEST_RP-344" class="tag">TEST_RP-344</a><a href="#TEST_RP-345" class="tag">TEST_RP-345</a><a href="#TEST_RP-346" class="tag">TEST_RP-346</a><a href="#TEST_RP-347" class="tag">TEST_RP-347</a><a href="#TEST_RP-348" class="tag">TEST_RP-348</a><a href="#TEST_RP-349" class="tag">TEST_RP-349</a><a href="#TEST_RP-350" class="tag">TEST_RP-350</a><a href="#TEST_RP-351" class="tag">TEST_RP-351</a><a href="#TEST_RP-355" class="tag">TEST_RP-355</a><a href="#TEST_RP-356" class="tag">TEST_RP-356</a><a href="#TEST_RP-357" class="tag">TEST_RP-357</a><a href="#TEST_RP-370" class="tag">TEST_RP-370</a><a href="#TEST_RP-372" class="tag">TEST_RP-372</a><a href="#TEST_RP-373" class="tag">TEST_RP-373</a><a href="#TEST_RP-374" class="tag">TEST_RP-374</a><a href="#TEST_RP-375" class="tag">TEST_RP-375</a><a href="#TEST_RP-46" class="tag">TEST_RP-46</a><a href="#TEST_RP-543" class="tag">TEST_RP-543</a><a href="#TEST_RP-546" class="tag">TEST_RP-546</a><a href="#TEST_RP-547" class="tag">TEST_RP-547</a><a href="#TEST_RP-549" class="tag">TEST_RP-549</a><a href="#TEST_RP-552" class="tag">TEST_RP-552</a><a href="#TEST_RP-553" class="tag">TEST_RP-553</a><a href="#TEST_RP-83" class="tag">TEST_RP-83</a><a href="#TEST_RP-932" class="tag">TEST_RP-932</a><a href="#TEST_RP-98" class="tag">TEST_RP-98</a>
                </div>
                <div class="feature-info">
                    <div class="feature-details">
                        <h2>Feature: Rumdik Regression Test</h2>
                        <p class="feature-description"><strong>Description:</strong> Regression Test</p>
                        <div><strong>File name:</strong> rumdik_regression_test.feature</div>
                    </div>
                    <div class="metadata">
                        <div><strong>Relative path:</strong> test_framework/features/rumdik_regression_test.feature</div>
                    </div>
                </div>
            </div>
            
            <!-- Scenarios and Metadata Grid -->
            <div class="scenarios-overview">
                <div class="scenarios-chart">
                    <h3>Scenarios</h3>
                    <!-- Donut Chart positioned above status/progress -->
                    <div class="scenarios-chart-wrapper">
                        <div class="donut-chart mixed" onmouseover="handleDonutHover(event, 0, 59)" onmouseout="hideTooltip()">
                            59
                        </div>
                    </div>
                    <!-- Status and Progress details below donut chart -->
                    <div class="scenarios-chart-details">
                        <div class="chart-detail-section">
                            <h4>Status</h4>
                            <div class="status-values">
                                <div class="passed">✅ Passed: 0</div>
                                <div class="failed">❌ Failed: 59</div>
                            </div>
                        </div>
                        <div class="chart-detail-section">
                            <h4>Progress</h4>
                            <div class="progress-values">
                                <div class="passed">✅ 0.0%</div>
                                <div class="failed">❌ 100.0%</div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="metadata-panel">
                    <h3>Metadata</h3>
                    <div class="metadata-grid">
                        <div class="metadata-item">
                            <div class="metadata-label">Device</div>
                            <div class="metadata-value">Runner Machine</div>
                        </div>
                        <div class="metadata-item">
                            <div class="metadata-label">OS</div>
                            <div class="metadata-value">
                                <i class="fab fa-apple"></i>
                                Darwin
                            </div>
                        </div>
                        <div class="metadata-item">
                            <div class="metadata-label">Browser</div>
                            <div class="metadata-value">
                                <i class="fab fa-chrome"></i>
                                Chrome 103
                            </div>
                        </div>
                        <div class="metadata-item">
                            <div class="metadata-label">Execution Time</div>
                            <div class="metadata-value">
                                <i class="fas fa-clock"></i>
                                1035.8s
                            </div>
                        </div>
                        <div class="metadata-item">
                            <div class="metadata-label">Total Tests</div>
                            <div class="metadata-value">
                                <i class="fas fa-list"></i>
                                59
                            </div>
                        </div>
                        <div class="metadata-item">
                            <div class="metadata-label">Success Rate</div>
                            <div class="metadata-value">
                                <i class="fas fa-chart-line"></i>
                                0.0%
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Filter Section -->
            <div class="filter-section">
                <h3>Filter Scenarios</h3>
                <div class="filter-buttons">
                    <button class="filter-btn active" onclick="filterScenarios('all')">All (59)</button>
                    <button class="filter-btn" onclick="filterScenarios('passed')">Passed (0)</button>
                    <button class="filter-btn" onclick="filterScenarios('failed')">Failed (59)</button>
                </div>
            </div>
            
            <!-- Scenarios Section -->
            <div class="scenarios-section">
                <h3>Test Scenarios</h3>
                
            <div class="scenario-section">
                <div class="scenario-header" onclick="toggleScenario(this)">
                    <div class="scenario-info">
                        <span class="scenario-icon">❌</span>
                        <span class="scenario-title">TEST_RP-100</span>
                        <span class="scenario-status failed">FAILED</span>
                    </div>
                    <div class="scenario-meta">
                        <span class="execution-time">⏱️ 54.2s</span>
                        <span class="steps-summary">📊 3/6 passed</span>
                        <span class="toggle-arrow">▼</span>
                    </div>
                </div>
                <div class="scenario-content" style="display: none;">
                    <div class="steps-container">
                        
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 1</span>
                        <span class="step-text">Given Mengakses halaman Beranda pada aplikasi Rumah Pendidikan</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Navigated to beranda pada aplikasi rumah pendidikan</div>
                        
                    </div>
                </div>
                
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 2</span>
                        <span class="step-text">When Klik "Ruang Sekolah"</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Clicked ruang sekolah</div>
                        
                    </div>
                </div>
                
                <div class="step failed">
                    <div class="step-header">
                        <span class="step-icon">❌</span>
                        <span class="step-number">Step 3</span>
                        <span class="step-text">And Menampilkan halaman "Ruang Sekolah"</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: FAILED</div>
                        <div class="step-result">Result: Assertion failed for halaman </div>
                        <div class="step-error">Error: Expected: halaman , Actual: Langsung ke konten utama
Rumah Pendidikan
Cari
Pusat Informasi
Masuk
Jelajahi Ruang di Rumah Pendidikan

Ruang GTK

Ruang Murid

Ruang Sekolah

Ruang Bahasa

Ruang Pemerintah

Ruang Mitra

Ruang Publi</div>
                    </div>
                </div>
                
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 4</span>
                        <span class="step-text">And Klik "Rapor Satuan Pendidikan" yang tidak tersedia</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Clicked rapor satuan pendidikan</div>
                        
                    </div>
                </div>
                
                <div class="step failed">
                    <div class="step-header">
                        <span class="step-icon">❌</span>
                        <span class="step-number">Step 5</span>
                        <span class="step-text">Then Tidak diarahkan ke Webview Website "https://raporpendidikan.kemdikbud.go.id/app"</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: FAILED</div>
                        <div class="step-result">Result: Assertion failed for webview website </div>
                        <div class="step-error">Error: Expected: webview website , Actual: Langsung ke konten utama
Rumah Pendidikan
Cari
Pusat Informasi
Masuk
Jelajahi Ruang di Rumah Pendidikan

Ruang GTK

Ruang Murid

Ruang Sekolah

Ruang Bahasa

Ruang Pemerintah

Ruang Mitra

Ruang Publi</div>
                    </div>
                </div>
                
                <div class="step failed">
                    <div class="step-header">
                        <span class="step-icon">❌</span>
                        <span class="step-number">Step 6</span>
                        <span class="step-text">And Gagal Diarahkan ke halaman Webview Website Rapor Pendidikan</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: FAILED</div>
                        <div class="step-result">Result: Assertion failed for halaman webview website rapor pendidikan</div>
                        <div class="step-error">Error: Expected: halaman webview website rapor pendidikan, Actual: Langsung ke konten utama
Rumah Pendidikan
Cari
Pusat Informasi
Masuk
Jelajahi Ruang di Rumah Pendidikan

Ruang GTK

Ruang Murid

Ruang Sekolah

Ruang Bahasa

Ruang Pemerintah

Ruang Mitra

Ruang Publik

Ruang Orang Tua

Semangat Rumah Pendidikan

Rumah Pendidikan berkomitmen untuk menjadi pusat ruang kolaborasi bagi siswa, guru, orang tua, hingga pemerintah daerah

Pelajari Selengkapnya
Layanan Unggulan di Rumah Pendidikan

Sumber Belajar

Portal pembelajaran digital interaktif untuk semua jenjang.

Buku Bacaan Digital

Pusat Buku Digital untuk Gerakan Literasi Nasional.

Pengelolaan Kinerja

Perencanaan, pelaksanaan, dan penilaian Anda untuk pengembangan diri dan satdik.

Diklat

Pelatihan terbimbing menggunakan LMS.

Pelatihan Mandiri

Materi untuk pengembangan kompetensi Guru dan Tendik.

Siapa saja yang sudah memanfaatkan kemudahan kolaborasi di Rumah Pendidikan?

Simak perjalanan mereka

"Rumah Pendidikan merupakan gagasan yang sangat menarik dan terdepan untuk perkembangan zaman terkini. Di mana pergeseran paradigma baru, pendidikan tidak hanya sekedar di dalam kelas tapi juga bisa di ruang keluarga dan masyarakat."

Arham, S.Pd., M.Pd

Kepala Sekolah SD INPRES 33 Birobuli, Palu

"Kami menyambut baik dengan adanya rumah pendidikan ini sebagai langkah strategis Kemendikdasmen terutama memperluas akses pendidikan dan pemerataan mutu pendidikan. Karena diharapkan dengan adanya Rumdik ini sebagai salah satu langkah pendidikan yang Responsif, Akuntabel, Melayani, Adaptif, dan Harmonis. Semoga menjadi langkah terbaik untuk Indonesia maju."

Dr. Firman Oktora, S.SI, M.PD.

Kabalaitekkomdik Jawa Barat

"Menurut saya Rumah Pendidikan sangat keren dan sangat membantu. Karena bagi saya murid kelas 6, Rumah Pendidikan bisa membantu dalam belajar dengan simpel, praktis, dan bisa mempelajari berbagai macam hal dalam satu platform, jadi sangat membantu."

Alena

Pelajar Gen KiHajar

Informasi Untuk Anda

Berita
Sambut Tahun Ajaran Baru 2025 dengan Penuh Inspirasi!
14 Juli 2025 | 10.32 WIB
Baca Selengkapnya
Berita
Laman Direktori Portal SPMB Berbasis SIAP SPMB Online
11 Juli 2025 | 15.13 WIB
Baca Selengkapnya
Berita
Tanya Jawab seputar Tes Kemampuan Akademik (ТKА)
24 Juni 2025 | 15.00 WIB
Baca Selengkapnya
Lihat lebih banyak
Pertanyaan yang paling sering ditanyakan
Apa manfaat menggunakan Rumah Pendidikan?
Bagaimana cara mengakses Rumah Pendidikan?
Apakah pengguna harus menggunakan Akun belajar.id untuk mengakses Rumah Pendidikan?
Siapa saja pengguna yang disarankan untuk menggunakan platform Rumah Pendidikan
Apa perbedaan rumah pendidikan dengan platform digital pendidikan milik Kemendikdasmen lainnya?
Ke Pusat Informasi

Kementerian Pendidikan Dasar dan Menengah

Kompleks Kementerian Pendidikan dan Kebudayaan, Senayan, Jakarta Pusat 10270

Navigasi

Pusat Bantuan Rumah Pendidikan

Syarat & Ketentuan

Kebijakan Privasi

Unduh Aplikasi Rumah Pendidikan</div>
                    </div>
                </div>
                
                    </div>
                </div>
            </div>
            
            <div class="scenario-section">
                <div class="scenario-header" onclick="toggleScenario(this)">
                    <div class="scenario-info">
                        <span class="scenario-icon">❌</span>
                        <span class="scenario-title">TEST_RP-101</span>
                        <span class="scenario-status failed">FAILED</span>
                    </div>
                    <div class="scenario-meta">
                        <span class="execution-time">⏱️ 49.3s</span>
                        <span class="steps-summary">📊 3/6 passed</span>
                        <span class="toggle-arrow">▼</span>
                    </div>
                </div>
                <div class="scenario-content" style="display: none;">
                    <div class="steps-container">
                        
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 1</span>
                        <span class="step-text">Given Mengakses halaman Beranda pada aplikasi Rumah Pendidikan</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Navigated to beranda pada aplikasi rumah pendidikan</div>
                        
                    </div>
                </div>
                
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 2</span>
                        <span class="step-text">When Klik "Ruang Sekolah"</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Clicked ruang sekolah</div>
                        
                    </div>
                </div>
                
                <div class="step failed">
                    <div class="step-header">
                        <span class="step-icon">❌</span>
                        <span class="step-number">Step 3</span>
                        <span class="step-text">And Menampilkan halaman Ruang Sekolah</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: FAILED</div>
                        <div class="step-result">Result: Assertion failed for halaman ruang sekolah</div>
                        <div class="step-error">Error: Expected: halaman ruang sekolah, Actual: Ruang Sekolah</div>
                    </div>
                </div>
                
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 4</span>
                        <span class="step-text">And Klik "Rencana Kegiatan dan Belanja Sekolah"</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Clicked rencana kegiatan dan belanja sekolah</div>
                        
                    </div>
                </div>
                
                <div class="step failed">
                    <div class="step-header">
                        <span class="step-icon">❌</span>
                        <span class="step-number">Step 5</span>
                        <span class="step-text">Then Akan diarahkan ke Webview Website "https://arkas.kemendikdasmen.go.id/download/arkas4"</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: FAILED</div>
                        <div class="step-result">Result: Assertion failed for webview website "https://arkas.kemendikdasmen.go.id/download/arkas4"</div>
                        <div class="step-error">Error: Expected: webview website "https://arkas.kemendikdasmen.go.id/download/arkas4", Actual: Langsung ke konten utama
Rumah Pendidikan
Cari
Pusat Informasi
Masuk
Jelajahi Ruang di Rumah Pendidikan

Ruang GTK

Ruang Murid

Ruang Sekolah

Ruang Bahasa

Ruang Pemerintah

Ruang Mitra

Ruang Publi</div>
                    </div>
                </div>
                
                <div class="step failed">
                    <div class="step-header">
                        <span class="step-icon">❌</span>
                        <span class="step-number">Step 6</span>
                        <span class="step-text">And Berhasil Diarahkan ke halaman Webview Website Arkas</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: FAILED</div>
                        <div class="step-result">Result: Assertion failed for halaman webview website arkas</div>
                        <div class="step-error">Error: Expected: halaman webview website arkas, Actual: Langsung ke konten utama
Rumah Pendidikan
Cari
Pusat Informasi
Masuk
Jelajahi Ruang di Rumah Pendidikan

Ruang GTK

Ruang Murid

Ruang Sekolah

Ruang Bahasa

Ruang Pemerintah

Ruang Mitra

Ruang Publi</div>
                    </div>
                </div>
                
                    </div>
                </div>
            </div>
            
            <div class="scenario-section">
                <div class="scenario-header" onclick="toggleScenario(this)">
                    <div class="scenario-info">
                        <span class="scenario-icon">❌</span>
                        <span class="scenario-title">TEST_RP-102</span>
                        <span class="scenario-status failed">FAILED</span>
                    </div>
                    <div class="scenario-meta">
                        <span class="execution-time">⏱️ 54.0s</span>
                        <span class="steps-summary">📊 3/6 passed</span>
                        <span class="toggle-arrow">▼</span>
                    </div>
                </div>
                <div class="scenario-content" style="display: none;">
                    <div class="steps-container">
                        
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 1</span>
                        <span class="step-text">Given Mengakses halaman Beranda pada aplikasi Rumah Pendidikan</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Navigated to beranda pada aplikasi rumah pendidikan</div>
                        
                    </div>
                </div>
                
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 2</span>
                        <span class="step-text">When Klik "Ruang Sekolah"</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Clicked ruang sekolah</div>
                        
                    </div>
                </div>
                
                <div class="step failed">
                    <div class="step-header">
                        <span class="step-icon">❌</span>
                        <span class="step-number">Step 3</span>
                        <span class="step-text">And Menampilkan halaman "Ruang Sekolah"</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: FAILED</div>
                        <div class="step-result">Result: Assertion failed for halaman </div>
                        <div class="step-error">Error: Expected: halaman , Actual: Langsung ke konten utama
Rumah Pendidikan
Cari
Pusat Informasi
Masuk
Jelajahi Ruang di Rumah Pendidikan

Ruang GTK

Ruang Murid

Ruang Sekolah

Ruang Bahasa

Ruang Pemerintah

Ruang Mitra

Ruang Publi</div>
                    </div>
                </div>
                
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 4</span>
                        <span class="step-text">And Klik "Rencana Kegiatan dan Belanja Sekolah" yang tidak tersedia</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Clicked rencana kegiatan dan belanja sekolah</div>
                        
                    </div>
                </div>
                
                <div class="step failed">
                    <div class="step-header">
                        <span class="step-icon">❌</span>
                        <span class="step-number">Step 5</span>
                        <span class="step-text">Then Tidak diarahkan ke Webview Website "https://arkas.kemdikbud.go.id/"</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: FAILED</div>
                        <div class="step-result">Result: Assertion failed for webview website </div>
                        <div class="step-error">Error: Expected: webview website , Actual: Langsung ke konten utama
Rumah Pendidikan
Cari
Pusat Informasi
Masuk
Jelajahi Ruang di Rumah Pendidikan

Ruang GTK

Ruang Murid

Ruang Sekolah

Ruang Bahasa

Ruang Pemerintah

Ruang Mitra

Ruang Publi</div>
                    </div>
                </div>
                
                <div class="step failed">
                    <div class="step-header">
                        <span class="step-icon">❌</span>
                        <span class="step-number">Step 6</span>
                        <span class="step-text">And Gagal Diarahkan ke halaman Webview Website Arkas</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: FAILED</div>
                        <div class="step-result">Result: Assertion failed for halaman webview website arkas</div>
                        <div class="step-error">Error: Expected: halaman webview website arkas, Actual: Langsung ke konten utama
Rumah Pendidikan
Cari
Pusat Informasi
Masuk
Jelajahi Ruang di Rumah Pendidikan

Ruang GTK

Ruang Murid

Ruang Sekolah

Ruang Bahasa

Ruang Pemerintah

Ruang Mitra

Ruang Publi</div>
                    </div>
                </div>
                
                    </div>
                </div>
            </div>
            
            <div class="scenario-section">
                <div class="scenario-header" onclick="toggleScenario(this)">
                    <div class="scenario-info">
                        <span class="scenario-icon">❌</span>
                        <span class="scenario-title">TEST_RP-112</span>
                        <span class="scenario-status failed">FAILED</span>
                    </div>
                    <div class="scenario-meta">
                        <span class="execution-time">⏱️ 54.1s</span>
                        <span class="steps-summary">📊 3/6 passed</span>
                        <span class="toggle-arrow">▼</span>
                    </div>
                </div>
                <div class="scenario-content" style="display: none;">
                    <div class="steps-container">
                        
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 1</span>
                        <span class="step-text">Given Mengakses halaman Beranda pada aplikasi Rumah Pendidikan</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Navigated to beranda pada aplikasi rumah pendidikan</div>
                        
                    </div>
                </div>
                
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 2</span>
                        <span class="step-text">When Klik "Ruang Bahasa"</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Clicked ruang bahasa</div>
                        
                    </div>
                </div>
                
                <div class="step failed">
                    <div class="step-header">
                        <span class="step-icon">❌</span>
                        <span class="step-number">Step 3</span>
                        <span class="step-text">And Menampilkan halaman "Ruang Bahasa"</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: FAILED</div>
                        <div class="step-result">Result: Assertion failed for halaman </div>
                        <div class="step-error">Error: Expected: halaman , Actual: Langsung ke konten utama
Rumah Pendidikan
Cari
Pusat Informasi
Masuk
Jelajahi Ruang di Rumah Pendidikan

Ruang GTK

Ruang Murid

Ruang Sekolah

Ruang Bahasa

Ruang Pemerintah

Ruang Mitra

Ruang Publi</div>
                    </div>
                </div>
                
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 4</span>
                        <span class="step-text">And Klik "Penerjemahan Daring" yang tidak tersedia</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Clicked penerjemahan daring</div>
                        
                    </div>
                </div>
                
                <div class="step failed">
                    <div class="step-header">
                        <span class="step-icon">❌</span>
                        <span class="step-number">Step 5</span>
                        <span class="step-text">Then Tidak diarahkan ke Webview Website "https://penerjemahan.kemdikbud.go.id/"</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: FAILED</div>
                        <div class="step-result">Result: Assertion failed for webview website </div>
                        <div class="step-error">Error: Expected: webview website , Actual: Langsung ke konten utama
Rumah Pendidikan
Cari
Pusat Informasi
Masuk
Jelajahi Ruang di Rumah Pendidikan

Ruang GTK

Ruang Murid

Ruang Sekolah

Ruang Bahasa

Ruang Pemerintah

Ruang Mitra

Ruang Publi</div>
                    </div>
                </div>
                
                <div class="step failed">
                    <div class="step-header">
                        <span class="step-icon">❌</span>
                        <span class="step-number">Step 6</span>
                        <span class="step-text">And Gagal Diarahkan ke halaman Webview Website Penerjemahan</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: FAILED</div>
                        <div class="step-result">Result: Assertion failed for halaman webview website penerjemahan</div>
                        <div class="step-error">Error: Expected: halaman webview website penerjemahan, Actual: Langsung ke konten utama
Rumah Pendidikan
Cari
Pusat Informasi
Masuk
Jelajahi Ruang di Rumah Pendidikan

Ruang GTK

Ruang Murid

Ruang Sekolah

Ruang Bahasa

Ruang Pemerintah

Ruang Mitra

Ruang Publi</div>
                    </div>
                </div>
                
                    </div>
                </div>
            </div>
            
            <div class="scenario-section">
                <div class="scenario-header" onclick="toggleScenario(this)">
                    <div class="scenario-info">
                        <span class="scenario-icon">❌</span>
                        <span class="scenario-title">TEST_RP-113</span>
                        <span class="scenario-status failed">FAILED</span>
                    </div>
                    <div class="scenario-meta">
                        <span class="execution-time">⏱️ 49.0s</span>
                        <span class="steps-summary">📊 3/6 passed</span>
                        <span class="toggle-arrow">▼</span>
                    </div>
                </div>
                <div class="scenario-content" style="display: none;">
                    <div class="steps-container">
                        
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 1</span>
                        <span class="step-text">Given Mengakses halaman Beranda pada aplikasi Rumah Pendidikan</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Navigated to beranda pada aplikasi rumah pendidikan</div>
                        
                    </div>
                </div>
                
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 2</span>
                        <span class="step-text">When Klik "Ruang Bahasa"</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Clicked ruang bahasa</div>
                        
                    </div>
                </div>
                
                <div class="step failed">
                    <div class="step-header">
                        <span class="step-icon">❌</span>
                        <span class="step-number">Step 3</span>
                        <span class="step-text">And Menampilkan halaman "Ruang Bahasa"</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: FAILED</div>
                        <div class="step-result">Result: Assertion failed for halaman </div>
                        <div class="step-error">Error: Expected: halaman , Actual: Langsung ke konten utama
Rumah Pendidikan
Cari
Pusat Informasi
Masuk
Jelajahi Ruang di Rumah Pendidikan

Ruang GTK

Ruang Murid

Ruang Sekolah

Ruang Bahasa

Ruang Pemerintah

Ruang Mitra

Ruang Publi</div>
                    </div>
                </div>
                
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 4</span>
                        <span class="step-text">And Klik "Layanan UKBI"</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Clicked layanan ukbi</div>
                        
                    </div>
                </div>
                
                <div class="step failed">
                    <div class="step-header">
                        <span class="step-icon">❌</span>
                        <span class="step-number">Step 5</span>
                        <span class="step-text">Then Akan diarahkan ke Webview Website "https://ukbi.kemdikbud.go.id/"</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: FAILED</div>
                        <div class="step-result">Result: Assertion failed for webview website "https://ukbi.kemdikbud.go.id/"</div>
                        <div class="step-error">Error: Expected: webview website "https://ukbi.kemdikbud.go.id/", Actual: Langsung ke konten utama
Rumah Pendidikan
Cari
Pusat Informasi
Masuk
Jelajahi Ruang di Rumah Pendidikan

Ruang GTK

Ruang Murid

Ruang Sekolah

Ruang Bahasa

Ruang Pemerintah

Ruang Mitra

Ruang Publi</div>
                    </div>
                </div>
                
                <div class="step failed">
                    <div class="step-header">
                        <span class="step-icon">❌</span>
                        <span class="step-number">Step 6</span>
                        <span class="step-text">And Berhasil Diarahkan ke halaman Webview Website UKBI</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: FAILED</div>
                        <div class="step-result">Result: Assertion failed for halaman webview website ukbi</div>
                        <div class="step-error">Error: Expected: halaman webview website ukbi, Actual: Langsung ke konten utama
Rumah Pendidikan
Cari
Pusat Informasi
Masuk
Jelajahi Ruang di Rumah Pendidikan

Ruang GTK

Ruang Murid

Ruang Sekolah

Ruang Bahasa

Ruang Pemerintah

Ruang Mitra

Ruang Publi</div>
                    </div>
                </div>
                
                    </div>
                </div>
            </div>
            
            <div class="scenario-section">
                <div class="scenario-header" onclick="toggleScenario(this)">
                    <div class="scenario-info">
                        <span class="scenario-icon">❌</span>
                        <span class="scenario-title">TEST_RP-124</span>
                        <span class="scenario-status failed">FAILED</span>
                    </div>
                    <div class="scenario-meta">
                        <span class="execution-time">⏱️ 35.8s</span>
                        <span class="steps-summary">📊 4/6 passed</span>
                        <span class="toggle-arrow">▼</span>
                    </div>
                </div>
                <div class="scenario-content" style="display: none;">
                    <div class="steps-container">
                        
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 1</span>
                        <span class="step-text">Given Mengakses halaman Beranda pada aplikasi Rumah Pendidikan</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Navigated to beranda pada aplikasi rumah pendidikan</div>
                        
                    </div>
                </div>
                
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 2</span>
                        <span class="step-text">When Klik "Ruang Pemerintah"</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Clicked ruang pemerintah</div>
                        
                    </div>
                </div>
                
                <div class="step failed">
                    <div class="step-header">
                        <span class="step-icon">❌</span>
                        <span class="step-number">Step 3</span>
                        <span class="step-text">And Menampilkan halaman "Ruang Pemerintah"</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: FAILED</div>
                        <div class="step-result">Result: Assertion failed for halaman </div>
                        <div class="step-error">Error: Expected: halaman , Actual: Langsung ke konten utama
Rumah Pendidikan
Cari
Pusat Informasi
Masuk
Jelajahi Ruang di Rumah Pendidikan

Ruang GTK

Ruang Murid

Ruang Sekolah

Ruang Bahasa

Ruang Pemerintah

Ruang Mitra

Ruang Publi</div>
                    </div>
                </div>
                
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 4</span>
                        <span class="step-text">And Klik "Akun Pendidikan"</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Clicked akun pendidikan</div>
                        
                    </div>
                </div>
                
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 5</span>
                        <span class="step-text">Then diarahkan ke Webview Website "https://belajar.id/"</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Asserted https://belajar.id/</div>
                        
                    </div>
                </div>
                
                <div class="step failed">
                    <div class="step-header">
                        <span class="step-icon">❌</span>
                        <span class="step-number">Step 6</span>
                        <span class="step-text">And Diarahkan ke halaman Webview Website Belajar.id</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: FAILED</div>
                        <div class="step-result">Result: Assertion failed for None</div>
                        <div class="step-error">Error: Expected: None, Actual: Assertion error: 'NoneType' object has no attribute 'lower'</div>
                    </div>
                </div>
                
                    </div>
                </div>
            </div>
            
            <div class="scenario-section">
                <div class="scenario-header" onclick="toggleScenario(this)">
                    <div class="scenario-info">
                        <span class="scenario-icon">❌</span>
                        <span class="scenario-title">TEST_RP-138</span>
                        <span class="scenario-status failed">FAILED</span>
                    </div>
                    <div class="scenario-meta">
                        <span class="execution-time">⏱️ 54.0s</span>
                        <span class="steps-summary">📊 3/6 passed</span>
                        <span class="toggle-arrow">▼</span>
                    </div>
                </div>
                <div class="scenario-content" style="display: none;">
                    <div class="steps-container">
                        
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 1</span>
                        <span class="step-text">Given Mengakses halaman Beranda pada aplikasi Rumah Pendidikan</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Navigated to beranda pada aplikasi rumah pendidikan</div>
                        
                    </div>
                </div>
                
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 2</span>
                        <span class="step-text">When Klik Ruang Publik</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Clicked ruang publik</div>
                        
                    </div>
                </div>
                
                <div class="step failed">
                    <div class="step-header">
                        <span class="step-icon">❌</span>
                        <span class="step-number">Step 3</span>
                        <span class="step-text">And Menampilkan halaman Ruang Publik</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: FAILED</div>
                        <div class="step-result">Result: Assertion failed for halaman ruang publik</div>
                        <div class="step-error">Error: Expected: halaman ruang publik, Actual: Ruang Publik</div>
                    </div>
                </div>
                
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 4</span>
                        <span class="step-text">And Klik "Pusat Perbukuan"</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Clicked pusat perbukuan</div>
                        
                    </div>
                </div>
                
                <div class="step failed">
                    <div class="step-header">
                        <span class="step-icon">❌</span>
                        <span class="step-number">Step 5</span>
                        <span class="step-text">Then Tidak diarahkan ke Webview Website "https://buku.kemdikbud.go.id/"</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: FAILED</div>
                        <div class="step-result">Result: Assertion failed for webview website </div>
                        <div class="step-error">Error: Expected: webview website , Actual: Langsung ke konten utama
Rumah Pendidikan
Cari
Pusat Informasi
Masuk
Jelajahi Ruang di Rumah Pendidikan

Ruang GTK

Ruang Murid

Ruang Sekolah

Ruang Bahasa

Ruang Pemerintah

Ruang Mitra

Ruang Publi</div>
                    </div>
                </div>
                
                <div class="step failed">
                    <div class="step-header">
                        <span class="step-icon">❌</span>
                        <span class="step-number">Step 6</span>
                        <span class="step-text">And Gagal Diarahkan ke halaman Webview Website BUKU</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: FAILED</div>
                        <div class="step-result">Result: Assertion failed for halaman webview website buku</div>
                        <div class="step-error">Error: Expected: halaman webview website buku, Actual: Langsung ke konten utama
Rumah Pendidikan
Cari
Pusat Informasi
Masuk
Jelajahi Ruang di Rumah Pendidikan

Ruang GTK

Ruang Murid

Ruang Sekolah

Ruang Bahasa

Ruang Pemerintah

Ruang Mitra

Ruang Publik

Ruang Orang Tua

Semangat Rumah Pendidikan

Rumah Pendidikan berkomitmen untuk menjadi pusat ruang kolaborasi bagi siswa, guru, orang tua, hingga pemerintah daerah

Pelajari Selengkapnya
Layanan Unggulan di Rumah Pendidikan

Sumber Belajar

Portal pembelajaran digital interaktif untuk semua jenjang.

Buku Bacaan Digital

Pusat Buku Digital untuk Gerakan Literasi Nasional.

Pengelolaan Kinerja

Perencanaan, pelaksanaan, dan penilaian Anda untuk pengembangan diri dan satdik.

Diklat

Pelatihan terbimbing menggunakan LMS.

Pelatihan Mandiri

Materi untuk pengembangan kompetensi Guru dan Tendik.

Siapa saja yang sudah memanfaatkan kemudahan kolaborasi di Rumah Pendidikan?

Simak perjalanan mereka

"Rumah Pendidikan merupakan gagasan yang sangat menarik dan terdepan untuk perkembangan zaman terkini. Di mana pergeseran paradigma baru, pendidikan tidak hanya sekedar di dalam kelas tapi juga bisa di ruang keluarga dan masyarakat."

Arham, S.Pd., M.Pd

Kepala Sekolah SD INPRES 33 Birobuli, Palu

"Kami menyambut baik dengan adanya rumah pendidikan ini sebagai langkah strategis Kemendikdasmen terutama memperluas akses pendidikan dan pemerataan mutu pendidikan. Karena diharapkan dengan adanya Rumdik ini sebagai salah satu langkah pendidikan yang Responsif, Akuntabel, Melayani, Adaptif, dan Harmonis. Semoga menjadi langkah terbaik untuk Indonesia maju."

Dr. Firman Oktora, S.SI, M.PD.

Kabalaitekkomdik Jawa Barat

"Menurut saya Rumah Pendidikan sangat keren dan sangat membantu. Karena bagi saya murid kelas 6, Rumah Pendidikan bisa membantu dalam belajar dengan simpel, praktis, dan bisa mempelajari berbagai macam hal dalam satu platform, jadi sangat membantu."

Alena

Pelajar Gen KiHajar

Informasi Untuk Anda

Berita
Sambut Tahun Ajaran Baru 2025 dengan Penuh Inspirasi!
14 Juli 2025 | 10.32 WIB
Baca Selengkapnya
Berita
Laman Direktori Portal SPMB Berbasis SIAP SPMB Online
11 Juli 2025 | 15.13 WIB
Baca Selengkapnya
Berita
Tanya Jawab seputar Tes Kemampuan Akademik (ТKА)
24 Juni 2025 | 15.00 WIB
Baca Selengkapnya
Lihat lebih banyak
Pertanyaan yang paling sering ditanyakan
Apa manfaat menggunakan Rumah Pendidikan?
Bagaimana cara mengakses Rumah Pendidikan?
Apakah pengguna harus menggunakan Akun belajar.id untuk mengakses Rumah Pendidikan?
Siapa saja pengguna yang disarankan untuk menggunakan platform Rumah Pendidikan
Apa perbedaan rumah pendidikan dengan platform digital pendidikan milik Kemendikdasmen lainnya?
Ke Pusat Informasi

Kementerian Pendidikan Dasar dan Menengah

Kompleks Kementerian Pendidikan dan Kebudayaan, Senayan, Jakarta Pusat 10270

Navigasi

Pusat Bantuan Rumah Pendidikan

Syarat & Ketentuan

Kebijakan Privasi

Unduh Aplikasi Rumah Pendidikan</div>
                    </div>
                </div>
                
                    </div>
                </div>
            </div>
            
            <div class="scenario-section">
                <div class="scenario-header" onclick="toggleScenario(this)">
                    <div class="scenario-info">
                        <span class="scenario-icon">❌</span>
                        <span class="scenario-title">TEST_RP-142</span>
                        <span class="scenario-status failed">FAILED</span>
                    </div>
                    <div class="scenario-meta">
                        <span class="execution-time">⏱️ 54.0s</span>
                        <span class="steps-summary">📊 3/6 passed</span>
                        <span class="toggle-arrow">▼</span>
                    </div>
                </div>
                <div class="scenario-content" style="display: none;">
                    <div class="steps-container">
                        
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 1</span>
                        <span class="step-text">Given Mengakses halaman Beranda pada aplikasi Rumah Pendidikan</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Navigated to beranda pada aplikasi rumah pendidikan</div>
                        
                    </div>
                </div>
                
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 2</span>
                        <span class="step-text">When Klik Ruang Publik</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Clicked ruang publik</div>
                        
                    </div>
                </div>
                
                <div class="step failed">
                    <div class="step-header">
                        <span class="step-icon">❌</span>
                        <span class="step-number">Step 3</span>
                        <span class="step-text">And Menampilkan halaman Ruang Publik</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: FAILED</div>
                        <div class="step-result">Result: Assertion failed for halaman ruang publik</div>
                        <div class="step-error">Error: Expected: halaman ruang publik, Actual: Ruang Publik</div>
                    </div>
                </div>
                
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 4</span>
                        <span class="step-text">And Klik "Bantuan Pendidikan"</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Clicked bantuan pendidikan</div>
                        
                    </div>
                </div>
                
                <div class="step failed">
                    <div class="step-header">
                        <span class="step-icon">❌</span>
                        <span class="step-number">Step 5</span>
                        <span class="step-text">Then Tidak diarahkan ke Webview Website "https://pip.kemdikbud.go.id/"</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: FAILED</div>
                        <div class="step-result">Result: Assertion failed for webview website </div>
                        <div class="step-error">Error: Expected: webview website , Actual: Langsung ke konten utama
Rumah Pendidikan
Cari
Pusat Informasi
Masuk
Jelajahi Ruang di Rumah Pendidikan

Ruang GTK

Ruang Murid

Ruang Sekolah

Ruang Bahasa

Ruang Pemerintah

Ruang Mitra

Ruang Publi</div>
                    </div>
                </div>
                
                <div class="step failed">
                    <div class="step-header">
                        <span class="step-icon">❌</span>
                        <span class="step-number">Step 6</span>
                        <span class="step-text">And Gagal Diarahkan ke halaman Webview Website BUKU</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: FAILED</div>
                        <div class="step-result">Result: Assertion failed for halaman webview website buku</div>
                        <div class="step-error">Error: Expected: halaman webview website buku, Actual: Langsung ke konten utama
Rumah Pendidikan
Cari
Pusat Informasi
Masuk
Jelajahi Ruang di Rumah Pendidikan

Ruang GTK

Ruang Murid

Ruang Sekolah

Ruang Bahasa

Ruang Pemerintah

Ruang Mitra

Ruang Publik

Ruang Orang Tua

Semangat Rumah Pendidikan

Rumah Pendidikan berkomitmen untuk menjadi pusat ruang kolaborasi bagi siswa, guru, orang tua, hingga pemerintah daerah

Pelajari Selengkapnya
Layanan Unggulan di Rumah Pendidikan

Sumber Belajar

Portal pembelajaran digital interaktif untuk semua jenjang.

Buku Bacaan Digital

Pusat Buku Digital untuk Gerakan Literasi Nasional.

Pengelolaan Kinerja

Perencanaan, pelaksanaan, dan penilaian Anda untuk pengembangan diri dan satdik.

Diklat

Pelatihan terbimbing menggunakan LMS.

Pelatihan Mandiri

Materi untuk pengembangan kompetensi Guru dan Tendik.

Siapa saja yang sudah memanfaatkan kemudahan kolaborasi di Rumah Pendidikan?

Simak perjalanan mereka

"Rumah Pendidikan merupakan gagasan yang sangat menarik dan terdepan untuk perkembangan zaman terkini. Di mana pergeseran paradigma baru, pendidikan tidak hanya sekedar di dalam kelas tapi juga bisa di ruang keluarga dan masyarakat."

Arham, S.Pd., M.Pd

Kepala Sekolah SD INPRES 33 Birobuli, Palu

"Kami menyambut baik dengan adanya rumah pendidikan ini sebagai langkah strategis Kemendikdasmen terutama memperluas akses pendidikan dan pemerataan mutu pendidikan. Karena diharapkan dengan adanya Rumdik ini sebagai salah satu langkah pendidikan yang Responsif, Akuntabel, Melayani, Adaptif, dan Harmonis. Semoga menjadi langkah terbaik untuk Indonesia maju."

Dr. Firman Oktora, S.SI, M.PD.

Kabalaitekkomdik Jawa Barat

"Menurut saya Rumah Pendidikan sangat keren dan sangat membantu. Karena bagi saya murid kelas 6, Rumah Pendidikan bisa membantu dalam belajar dengan simpel, praktis, dan bisa mempelajari berbagai macam hal dalam satu platform, jadi sangat membantu."

Alena

Pelajar Gen KiHajar

Informasi Untuk Anda

Berita
Sambut Tahun Ajaran Baru 2025 dengan Penuh Inspirasi!
14 Juli 2025 | 10.32 WIB
Baca Selengkapnya
Berita
Laman Direktori Portal SPMB Berbasis SIAP SPMB Online
11 Juli 2025 | 15.13 WIB
Baca Selengkapnya
Berita
Tanya Jawab seputar Tes Kemampuan Akademik (ТKА)
24 Juni 2025 | 15.00 WIB
Baca Selengkapnya
Lihat lebih banyak
Pertanyaan yang paling sering ditanyakan
Apa manfaat menggunakan Rumah Pendidikan?
Bagaimana cara mengakses Rumah Pendidikan?
Apakah pengguna harus menggunakan Akun belajar.id untuk mengakses Rumah Pendidikan?
Siapa saja pengguna yang disarankan untuk menggunakan platform Rumah Pendidikan
Apa perbedaan rumah pendidikan dengan platform digital pendidikan milik Kemendikdasmen lainnya?
Ke Pusat Informasi

Kementerian Pendidikan Dasar dan Menengah

Kompleks Kementerian Pendidikan dan Kebudayaan, Senayan, Jakarta Pusat 10270

Navigasi

Pusat Bantuan Rumah Pendidikan

Syarat & Ketentuan

Kebijakan Privasi

Unduh Aplikasi Rumah Pendidikan</div>
                    </div>
                </div>
                
                    </div>
                </div>
            </div>
            
            <div class="scenario-section">
                <div class="scenario-header" onclick="toggleScenario(this)">
                    <div class="scenario-info">
                        <span class="scenario-icon">❌</span>
                        <span class="scenario-title">TEST_RP-148</span>
                        <span class="scenario-status failed">FAILED</span>
                    </div>
                    <div class="scenario-meta">
                        <span class="execution-time">⏱️ 54.1s</span>
                        <span class="steps-summary">📊 3/6 passed</span>
                        <span class="toggle-arrow">▼</span>
                    </div>
                </div>
                <div class="scenario-content" style="display: none;">
                    <div class="steps-container">
                        
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 1</span>
                        <span class="step-text">Given Mengakses halaman Beranda pada aplikasi Rumah Pendidikan</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Navigated to beranda pada aplikasi rumah pendidikan</div>
                        
                    </div>
                </div>
                
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 2</span>
                        <span class="step-text">When Klik Ruang Orang Tua</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Clicked ruang orang tua</div>
                        
                    </div>
                </div>
                
                <div class="step failed">
                    <div class="step-header">
                        <span class="step-icon">❌</span>
                        <span class="step-number">Step 3</span>
                        <span class="step-text">And Menampilkan halaman Ruang Orang Tua</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: FAILED</div>
                        <div class="step-result">Result: Assertion failed for halaman ruang orang tua</div>
                        <div class="step-error">Error: Expected: halaman ruang orang tua, Actual: Ruang Orang Tua</div>
                    </div>
                </div>
                
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 4</span>
                        <span class="step-text">And Klik "Layanan Informasi dan Pengaduan" yang tidak tersedia</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Clicked layanan informasi dan pengaduan</div>
                        
                    </div>
                </div>
                
                <div class="step failed">
                    <div class="step-header">
                        <span class="step-icon">❌</span>
                        <span class="step-number">Step 5</span>
                        <span class="step-text">Then Tidak diarahkan ke Webview Website "https://ult.kemdikbud.go.id/"</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: FAILED</div>
                        <div class="step-result">Result: Assertion failed for webview website </div>
                        <div class="step-error">Error: Expected: webview website , Actual: Langsung ke konten utama
Rumah Pendidikan
Cari
Pusat Informasi
Masuk
Jelajahi Ruang di Rumah Pendidikan

Ruang GTK

Ruang Murid

Ruang Sekolah

Ruang Bahasa

Ruang Pemerintah

Ruang Mitra

Ruang Publi</div>
                    </div>
                </div>
                
                <div class="step failed">
                    <div class="step-header">
                        <span class="step-icon">❌</span>
                        <span class="step-number">Step 6</span>
                        <span class="step-text">And Gagal Diarahkan ke halaman Webview Website Layanan Informasi dan Pengaduan</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: FAILED</div>
                        <div class="step-result">Result: Assertion failed for halaman webview website layanan informasi dan pengaduan</div>
                        <div class="step-error">Error: Expected: halaman webview website layanan informasi dan pengaduan, Actual: Langsung ke konten utama
Rumah Pendidikan
Cari
Pusat Informasi
Masuk
Jelajahi Ruang di Rumah Pendidikan

Ruang GTK

Ruang Murid

Ruang Sekolah

Ruang Bahasa

Ruang Pemerintah

Ruang Mitra

Ruang Publik

Ruang Orang Tua

Semangat Rumah Pendidikan

Rumah Pendidikan berkomitmen untuk menjadi pusat ruang kolaborasi bagi siswa, guru, orang tua, hingga pemerintah daerah

Pelajari Selengkapnya
Layanan Unggulan di Rumah Pendidikan

Sumber Belajar

Portal pembelajaran digital interaktif untuk semua jenjang.

Buku Bacaan Digital

Pusat Buku Digital untuk Gerakan Literasi Nasional.

Pengelolaan Kinerja

Perencanaan, pelaksanaan, dan penilaian Anda untuk pengembangan diri dan satdik.

Diklat

Pelatihan terbimbing menggunakan LMS.

Pelatihan Mandiri

Materi untuk pengembangan kompetensi Guru dan Tendik.

Siapa saja yang sudah memanfaatkan kemudahan kolaborasi di Rumah Pendidikan?

Simak perjalanan mereka

"Rumah Pendidikan merupakan gagasan yang sangat menarik dan terdepan untuk perkembangan zaman terkini. Di mana pergeseran paradigma baru, pendidikan tidak hanya sekedar di dalam kelas tapi juga bisa di ruang keluarga dan masyarakat."

Arham, S.Pd., M.Pd

Kepala Sekolah SD INPRES 33 Birobuli, Palu

"Kami menyambut baik dengan adanya rumah pendidikan ini sebagai langkah strategis Kemendikdasmen terutama memperluas akses pendidikan dan pemerataan mutu pendidikan. Karena diharapkan dengan adanya Rumdik ini sebagai salah satu langkah pendidikan yang Responsif, Akuntabel, Melayani, Adaptif, dan Harmonis. Semoga menjadi langkah terbaik untuk Indonesia maju."

Dr. Firman Oktora, S.SI, M.PD.

Kabalaitekkomdik Jawa Barat

"Menurut saya Rumah Pendidikan sangat keren dan sangat membantu. Karena bagi saya murid kelas 6, Rumah Pendidikan bisa membantu dalam belajar dengan simpel, praktis, dan bisa mempelajari berbagai macam hal dalam satu platform, jadi sangat membantu."

Alena

Pelajar Gen KiHajar

Informasi Untuk Anda

Berita
Sambut Tahun Ajaran Baru 2025 dengan Penuh Inspirasi!
14 Juli 2025 | 10.32 WIB
Baca Selengkapnya
Berita
Laman Direktori Portal SPMB Berbasis SIAP SPMB Online
11 Juli 2025 | 15.13 WIB
Baca Selengkapnya
Berita
Tanya Jawab seputar Tes Kemampuan Akademik (ТKА)
24 Juni 2025 | 15.00 WIB
Baca Selengkapnya
Lihat lebih banyak
Pertanyaan yang paling sering ditanyakan
Apa manfaat menggunakan Rumah Pendidikan?
Bagaimana cara mengakses Rumah Pendidikan?
Apakah pengguna harus menggunakan Akun belajar.id untuk mengakses Rumah Pendidikan?
Siapa saja pengguna yang disarankan untuk menggunakan platform Rumah Pendidikan
Apa perbedaan rumah pendidikan dengan platform digital pendidikan milik Kemendikdasmen lainnya?
Ke Pusat Informasi

Kementerian Pendidikan Dasar dan Menengah

Kompleks Kementerian Pendidikan dan Kebudayaan, Senayan, Jakarta Pusat 10270

Navigasi

Pusat Bantuan Rumah Pendidikan

Syarat & Ketentuan

Kebijakan Privasi

Unduh Aplikasi Rumah Pendidikan</div>
                    </div>
                </div>
                
                    </div>
                </div>
            </div>
            
            <div class="scenario-section">
                <div class="scenario-header" onclick="toggleScenario(this)">
                    <div class="scenario-info">
                        <span class="scenario-icon">❌</span>
                        <span class="scenario-title">TEST_RP-295</span>
                        <span class="scenario-status failed">FAILED</span>
                    </div>
                    <div class="scenario-meta">
                        <span class="execution-time">⏱️ 19.8s</span>
                        <span class="steps-summary">📊 3/4 passed</span>
                        <span class="toggle-arrow">▼</span>
                    </div>
                </div>
                <div class="scenario-content" style="display: none;">
                    <div class="steps-container">
                        
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 1</span>
                        <span class="step-text">Given the user navigates to "https://rumah-baru.staging.belajar.id/"</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Navigated to to </div>
                        
                    </div>
                </div>
                
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 2</span>
                        <span class="step-text">When I scroll to the section</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Scrolled page</div>
                        
                    </div>
                </div>
                
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 3</span>
                        <span class="step-text">When I click the element</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Clicked the element</div>
                        
                    </div>
                </div>
                
                <div class="step failed">
                    <div class="step-header">
                        <span class="step-icon">❌</span>
                        <span class="step-number">Step 4</span>
                        <span class="step-text">Then the user should be redirected to the "Kebijakan Privasi" page</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: FAILED</div>
                        <div class="step-result">Result: Assertion failed for the "kebijakan privasi" page</div>
                        <div class="step-error">Error: Expected: the "kebijakan privasi" page, Actual: Langsung ke konten utama
Rumah Pendidikan
Cari
Pusat Informasi
Masuk
Jelajahi Ruang di Rumah Pendidikan

Ruang GTK

Ruang Murid

Ruang Sekolah

Ruang Bahasa

Ruang Pemerintah

Ruang Mitra

Ruang Publi</div>
                    </div>
                </div>
                
                    </div>
                </div>
            </div>
            
            <div class="scenario-section">
                <div class="scenario-header" onclick="toggleScenario(this)">
                    <div class="scenario-info">
                        <span class="scenario-icon">❌</span>
                        <span class="scenario-title">TEST_RP-296</span>
                        <span class="scenario-status failed">FAILED</span>
                    </div>
                    <div class="scenario-meta">
                        <span class="execution-time">⏱️ 19.9s</span>
                        <span class="steps-summary">📊 3/4 passed</span>
                        <span class="toggle-arrow">▼</span>
                    </div>
                </div>
                <div class="scenario-content" style="display: none;">
                    <div class="steps-container">
                        
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 1</span>
                        <span class="step-text">Given the user navigates to "https://rumah-baru.staging.belajar.id/"</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Navigated to to </div>
                        
                    </div>
                </div>
                
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 2</span>
                        <span class="step-text">When I scroll to the section</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Scrolled page</div>
                        
                    </div>
                </div>
                
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 3</span>
                        <span class="step-text">When I click the element</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Clicked the element</div>
                        
                    </div>
                </div>
                
                <div class="step failed">
                    <div class="step-header">
                        <span class="step-icon">❌</span>
                        <span class="step-number">Step 4</span>
                        <span class="step-text">Then the user should be redirected to the "Syarat dan Ketentuan" page</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: FAILED</div>
                        <div class="step-result">Result: Assertion failed for the "syarat dan ketentuan" page</div>
                        <div class="step-error">Error: Expected: the "syarat dan ketentuan" page, Actual: Langsung ke konten utama
Rumah Pendidikan
Cari
Pusat Informasi
Masuk
Jelajahi Ruang di Rumah Pendidikan

Ruang GTK

Ruang Murid

Ruang Sekolah

Ruang Bahasa

Ruang Pemerintah

Ruang Mitra

Ruang Publik

Ruang Orang Tua

Semangat Rumah Pendidikan

Rumah Pendidikan berkomitmen untuk menjadi pusat ruang kolaborasi bagi siswa, guru, orang tua, hingga pemerintah daerah

Pelajari Selengkapnya
Layanan Unggulan di Rumah Pendidikan

Sumber Belajar

Portal pembelajaran digital interaktif untuk semua jenjang.

Buku Bacaan Digital

Pusat Buku Digital untuk Gerakan Literasi Nasional.

Pengelolaan Kinerja

Perencanaan, pelaksanaan, dan penilaian Anda untuk pengembangan diri dan satdik.

Diklat

Pelatihan terbimbing menggunakan LMS.

Pelatihan Mandiri

Materi untuk pengembangan kompetensi Guru dan Tendik.

Siapa saja yang sudah memanfaatkan kemudahan kolaborasi di Rumah Pendidikan?

Simak perjalanan mereka

"Rumah Pendidikan merupakan gagasan yang sangat menarik dan terdepan untuk perkembangan zaman terkini. Di mana pergeseran paradigma baru, pendidikan tidak hanya sekedar di dalam kelas tapi juga bisa di ruang keluarga dan masyarakat."

Arham, S.Pd., M.Pd

Kepala Sekolah SD INPRES 33 Birobuli, Palu

"Kami menyambut baik dengan adanya rumah pendidikan ini sebagai langkah strategis Kemendikdasmen terutama memperluas akses pendidikan dan pemerataan mutu pendidikan. Karena diharapkan dengan adanya Rumdik ini sebagai salah satu langkah pendidikan yang Responsif, Akuntabel, Melayani, Adaptif, dan Harmonis. Semoga menjadi langkah terbaik untuk Indonesia maju."

Dr. Firman Oktora, S.SI, M.PD.

Kabalaitekkomdik Jawa Barat

"Menurut saya Rumah Pendidikan sangat keren dan sangat membantu. Karena bagi saya murid kelas 6, Rumah Pendidikan bisa membantu dalam belajar dengan simpel, praktis, dan bisa mempelajari berbagai macam hal dalam satu platform, jadi sangat membantu."

Alena

Pelajar Gen KiHajar

Informasi Untuk Anda

Berita
Sambut Tahun Ajaran Baru 2025 dengan Penuh Inspirasi!
14 Juli 2025 | 10.32 WIB
Baca Selengkapnya
Berita
Laman Direktori Portal SPMB Berbasis SIAP SPMB Online
11 Juli 2025 | 15.13 WIB
Baca Selengkapnya
Berita
Tanya Jawab seputar Tes Kemampuan Akademik (ТKА)
24 Juni 2025 | 15.00 WIB
Baca Selengkapnya
Lihat lebih banyak
Pertanyaan yang paling sering ditanyakan
Apa manfaat menggunakan Rumah Pendidikan?
Bagaimana cara mengakses Rumah Pendidikan?
Apakah pengguna harus menggunakan Akun belajar.id untuk mengakses Rumah Pendidikan?
Siapa saja pengguna yang disarankan untuk menggunakan platform Rumah Pendidikan
Apa perbedaan rumah pendidikan dengan platform digital pendidikan milik Kemendikdasmen lainnya?
Ke Pusat Informasi

Kementerian Pendidikan Dasar dan Menengah

Kompleks Kementerian Pendidikan dan Kebudayaan, Senayan, Jakarta Pusat 10270

Navigasi

Pusat Bantuan Rumah Pendidikan

Syarat & Ketentuan

Kebijakan Privasi

Unduh Aplikasi Rumah Pendidikan</div>
                    </div>
                </div>
                
                    </div>
                </div>
            </div>
            
            <div class="scenario-section">
                <div class="scenario-header" onclick="toggleScenario(this)">
                    <div class="scenario-info">
                        <span class="scenario-icon">⏭️</span>
                        <span class="scenario-title">TEST_RP-299</span>
                        <span class="scenario-status failed">FAILED</span>
                    </div>
                    <div class="scenario-meta">
                        <span class="execution-time">⏱️ 0.5s</span>
                        <span class="steps-summary">📊 2/4 passed</span>
                        <span class="toggle-arrow">▼</span>
                    </div>
                </div>
                <div class="scenario-content" style="display: none;">
                    <div class="steps-container">
                        
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 1</span>
                        <span class="step-text">Given the user navigates to "https://rumah-baru.staging.belajar.id/"</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Navigated to to </div>
                        
                    </div>
                </div>
                
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 2</span>
                        <span class="step-text">Then the header should display the "Rumah Pendidikan" logo on the left</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Navigated to on the left</div>
                        
                    </div>
                </div>
                
                <div class="step skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-number">Step 3</span>
                        <span class="step-text">And a search box in the middle</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: SKIPPED</div>
                        <div class="step-result">Result: Unknown or unsupported action for step: a search box in the middle</div>
                        <div class="step-error">Error: Unknown or unsupported action</div>
                    </div>
                </div>
                
                <div class="step skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-number">Step 4</span>
                        <span class="step-text">And a "Masuk" button on the right</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: SKIPPED</div>
                        <div class="step-result">Result: Unknown or unsupported action for step: a "Masuk" button on the right</div>
                        <div class="step-error">Error: Unknown or unsupported action</div>
                    </div>
                </div>
                
                    </div>
                </div>
            </div>
            
            <div class="scenario-section">
                <div class="scenario-header" onclick="toggleScenario(this)">
                    <div class="scenario-info">
                        <span class="scenario-icon">❌</span>
                        <span class="scenario-title">TEST_RP-300</span>
                        <span class="scenario-status failed">FAILED</span>
                    </div>
                    <div class="scenario-meta">
                        <span class="execution-time">⏱️ 24.9s</span>
                        <span class="steps-summary">📊 2/3 passed</span>
                        <span class="toggle-arrow">▼</span>
                    </div>
                </div>
                <div class="scenario-content" style="display: none;">
                    <div class="steps-container">
                        
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 1</span>
                        <span class="step-text">Given the user is on any page of "Rumah Pendidikan"</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Navigated to any page of "rumah pendidikan"</div>
                        
                    </div>
                </div>
                
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 2</span>
                        <span class="step-text">When I click the element</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Clicked the element</div>
                        
                    </div>
                </div>
                
                <div class="step failed">
                    <div class="step-header">
                        <span class="step-icon">❌</span>
                        <span class="step-number">Step 3</span>
                        <span class="step-text">Then the user should be redirected to the homepage</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: FAILED</div>
                        <div class="step-result">Result: Assertion failed for the homepage</div>
                        <div class="step-error">Error: Expected: the homepage, Actual: Langsung ke konten utama
Rumah Pendidikan
Cari
Pusat Informasi
Masuk
Jelajahi Ruang di Rumah Pendidikan

Ruang GTK

Ruang Murid

Ruang Sekolah

Ruang Bahasa

Ruang Pemerintah

Ruang Mitra

Ruang Publi</div>
                    </div>
                </div>
                
                    </div>
                </div>
            </div>
            
            <div class="scenario-section">
                <div class="scenario-header" onclick="toggleScenario(this)">
                    <div class="scenario-info">
                        <span class="scenario-icon">❌</span>
                        <span class="scenario-title">TEST_RP-301</span>
                        <span class="scenario-status failed">FAILED</span>
                    </div>
                    <div class="scenario-meta">
                        <span class="execution-time">⏱️ 25.2s</span>
                        <span class="steps-summary">📊 2/3 passed</span>
                        <span class="toggle-arrow">▼</span>
                    </div>
                </div>
                <div class="scenario-content" style="display: none;">
                    <div class="steps-container">
                        
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 1</span>
                        <span class="step-text">Given the user is on the homepage</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Navigated to home</div>
                        
                    </div>
                </div>
                
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 2</span>
                        <span class="step-text">When I enter text</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Typed 'text' in input_field</div>
                        
                    </div>
                </div>
                
                <div class="step failed">
                    <div class="step-header">
                        <span class="step-icon">❌</span>
                        <span class="step-number">Step 3</span>
                        <span class="step-text">Then the search box should display the entered text</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: FAILED</div>
                        <div class="step-result">Result: Assertion failed for display the entered text</div>
                        <div class="step-error">Error: Expected: display the entered text, Actual: Langsung ke konten utama
Rumah Pendidikan
Cari
Pusat Informasi
Masuk
Jelajahi Ruang di Rumah Pendidikan

Ruang GTK

Ruang Murid

Ruang Sekolah

Ruang Bahasa

Ruang Pemerintah

Ruang Mitra

Ruang Publi</div>
                    </div>
                </div>
                
                    </div>
                </div>
            </div>
            
            <div class="scenario-section">
                <div class="scenario-header" onclick="toggleScenario(this)">
                    <div class="scenario-info">
                        <span class="scenario-icon">❌</span>
                        <span class="scenario-title">TEST_RP-302</span>
                        <span class="scenario-status failed">FAILED</span>
                    </div>
                    <div class="scenario-meta">
                        <span class="execution-time">⏱️ 24.3s</span>
                        <span class="steps-summary">📊 2/3 passed</span>
                        <span class="toggle-arrow">▼</span>
                    </div>
                </div>
                <div class="scenario-content" style="display: none;">
                    <div class="steps-container">
                        
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 1</span>
                        <span class="step-text">Given the user is on the homepage</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Navigated to home</div>
                        
                    </div>
                </div>
                
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 2</span>
                        <span class="step-text">When I click "masuk"</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Clicked masuk</div>
                        
                    </div>
                </div>
                
                <div class="step failed">
                    <div class="step-header">
                        <span class="step-icon">❌</span>
                        <span class="step-number">Step 3</span>
                        <span class="step-text">Then the user should be redirected to the login page</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: FAILED</div>
                        <div class="step-result">Result: Assertion failed for the login page</div>
                        <div class="step-error">Error: Expected: the login page, Actual: Langsung ke konten utama
Rumah Pendidikan
Cari
Pusat Informasi
Masuk
Jelajahi Ruang di Rumah Pendidikan

Ruang GTK

Ruang Murid

Ruang Sekolah

Ruang Bahasa

Ruang Pemerintah

Ruang Mitra

Ruang Publi</div>
                    </div>
                </div>
                
                    </div>
                </div>
            </div>
            
            <div class="scenario-section">
                <div class="scenario-header" onclick="toggleScenario(this)">
                    <div class="scenario-info">
                        <span class="scenario-icon">❌</span>
                        <span class="scenario-title">TEST_RP-305</span>
                        <span class="scenario-status failed">FAILED</span>
                    </div>
                    <div class="scenario-meta">
                        <span class="execution-time">⏱️ 94.4s</span>
                        <span class="steps-summary">📊 5/10 passed</span>
                        <span class="toggle-arrow">▼</span>
                    </div>
                </div>
                <div class="scenario-content" style="display: none;">
                    <div class="steps-container">
                        
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 1</span>
                        <span class="step-text">Given the user is on the homepage</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Navigated to home</div>
                        
                    </div>
                </div>
                
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 2</span>
                        <span class="step-text">When I click "masuk"</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Clicked masuk</div>
                        
                    </div>
                </div>
                
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 3</span>
                        <span class="step-text">And selects the "Masuk" option</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Clicked Masuk</div>
                        
                    </div>
                </div>
                
                <div class="step skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-number">Step 4</span>
                        <span class="step-text">And completes the Google login process</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: SKIPPED</div>
                        <div class="step-result">Result: Unknown or unsupported action for step: completes the Google login process</div>
                        <div class="step-error">Error: Unknown or unsupported action</div>
                    </div>
                </div>
                
                <div class="step failed">
                    <div class="step-header">
                        <span class="step-icon">❌</span>
                        <span class="step-number">Step 5</span>
                        <span class="step-text">Then the user should be logged in</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: FAILED</div>
                        <div class="step-result">Result: Assertion failed for be logged in</div>
                        <div class="step-error">Error: Expected: be logged in, Actual: Rumah Pendidikan berkomitmen untuk menjadi pusat ruang kolaborasi bagi siswa, guru, orang tua, hingga pemerintah daerah</div>
                    </div>
                </div>
                
                <div class="step failed">
                    <div class="step-header">
                        <span class="step-icon">❌</span>
                        <span class="step-number">Step 6</span>
                        <span class="step-text">Then And the navbar should display the user's initial name</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: FAILED</div>
                        <div class="step-result">Result: Assertion failed for display the user's initial name</div>
                        <div class="step-error">Error: Expected: display the user's initial name, Actual: Langsung ke konten utama
Rumah Pendidikan
Cari
Pusat Informasi
Masuk
Jelajahi Ruang di Rumah Pendidikan

Ruang GTK

Ruang Murid

Ruang Sekolah

Ruang Bahasa

Ruang Pemerintah

Ruang Mitra

Ruang Publi</div>
                    </div>
                </div>
                
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 7</span>
                        <span class="step-text">When I click the element</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Clicked the element</div>
                        
                    </div>
                </div>
                
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 8</span>
                        <span class="step-text">And selects "Logout"</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Clicked Logout</div>
                        
                    </div>
                </div>
                
                <div class="step failed">
                    <div class="step-header">
                        <span class="step-icon">❌</span>
                        <span class="step-number">Step 9</span>
                        <span class="step-text">Then the user should be logged out</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: FAILED</div>
                        <div class="step-result">Result: Assertion failed for be logged out</div>
                        <div class="step-error">Error: Expected: be logged out, Actual: Langsung ke konten utama
Rumah Pendidikan
Cari
Pusat Informasi
Masuk
Jelajahi Ruang di Rumah Pendidikan

Ruang GTK

Ruang Murid

Ruang Sekolah

Ruang Bahasa

Ruang Pemerintah

Ruang Mitra

Ruang Publi</div>
                    </div>
                </div>
                
                <div class="step failed">
                    <div class="step-header">
                        <span class="step-icon">❌</span>
                        <span class="step-number">Step 10</span>
                        <span class="step-text">Then And the navbar should display the "Masuk" button again</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: FAILED</div>
                        <div class="step-result">Result: Assertion failed for display the </div>
                        <div class="step-error">Error: Expected: display the , Actual: Langsung ke konten utama
Rumah Pendidikan
Cari
Pusat Informasi
Masuk
Jelajahi Ruang di Rumah Pendidikan

Ruang GTK

Ruang Murid

Ruang Sekolah

Ruang Bahasa

Ruang Pemerintah

Ruang Mitra

Ruang Publi</div>
                    </div>
                </div>
                
                    </div>
                </div>
            </div>
            
            <div class="scenario-section">
                <div class="scenario-header" onclick="toggleScenario(this)">
                    <div class="scenario-info">
                        <span class="scenario-icon">❌</span>
                        <span class="scenario-title">TEST_RP-307</span>
                        <span class="scenario-status failed">FAILED</span>
                    </div>
                    <div class="scenario-meta">
                        <span class="execution-time">⏱️ 27.9s</span>
                        <span class="steps-summary">📊 2/4 passed</span>
                        <span class="toggle-arrow">▼</span>
                    </div>
                </div>
                <div class="scenario-content" style="display: none;">
                    <div class="steps-container">
                        
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 1</span>
                        <span class="step-text">Given the user is on the Rumah Pendidikan homepage</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Navigated to rumah pendidikan home</div>
                        
                    </div>
                </div>
                
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 2</span>
                        <span class="step-text">When I click "pelajari selengkapnya"</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Clicked pelajari selengkapnya</div>
                        
                    </div>
                </div>
                
                <div class="step failed">
                    <div class="step-header">
                        <span class="step-icon">❌</span>
                        <span class="step-number">Step 3</span>
                        <span class="step-text">Then the user should be redirected to the "Mengenal Rumah Pendidikan" page</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: FAILED</div>
                        <div class="step-result">Result: Assertion failed for the "mengenal rumah pendidikan" page</div>
                        <div class="step-error">Error: Expected: the "mengenal rumah pendidikan" page, Actual: Mengenal Rumah Pendidikan</div>
                    </div>
                </div>
                
                <div class="step failed">
                    <div class="step-header">
                        <span class="step-icon">❌</span>
                        <span class="step-number">Step 4</span>
                        <span class="step-text">Then And the page should display complete information about Rumah Pendidikan</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: FAILED</div>
                        <div class="step-result">Result: Assertion failed for display complete information about rumah pendidikan</div>
                        <div class="step-error">Error: Expected: display complete information about rumah pendidikan, Actual: Navigasi Rumah Pendidikan
Cari
Masuk
Wujudkan Pendidikan Bermutu untuk Semua Melalui Rumah Pendidikan
Mengenal Rumah Pendidikan

Rumah Pendidikan adalah superaplikasi yang dirancang untuk meningkatkan kualitas pendidikan di Indonesia dengan menggabungkan berbagai layanan digital dan mendorong kolaborasi antar pemangku kepentingan dalam ekosistem pendidikan.

Sehingga aplikasi ini menghadirkan:
Efektivitas sistem dengan integrasi layanan

Dari puluhan layanan digital, kini disederhanakan menjadi 8 Ruang yang terintegrasi.

Kemudahan akses layanan

Guru, Kepala Sekolah, Operator Sekolah, Dinas Pendidikan dan aktor pendidikan lainnya tidak lagi harus mengakses berbagai aplikasi berbeda.

Efisiensi anggaran

Rumah Pendidikan diharapkan mampu menghemat lebih dari 60% biaya pengembangan teknologi pendidikan.

Partisipasi aktor pendidikan di Indonesia

Membuka peluang kolaborasi dengan para pelaku pendidikan, termasuk mitra penyedia konten, untuk mendorong inovasi dan pengembangan bersama.

Lihat rencana kami ke depan
Telusuri Cetak Biru

"Rumah Pendidikan menjadi katalisator dalam meningkatkan akses, mutu, dan efisiensi layanan publik yang RAMAH (Responsif, Akuntabel, Melayani, Adaptif, dan Harmonis). Dengan ekosistem layanan pendidikan yang terintegrasi, generasi mendatang diharapkan memiliki kompetensi yang relevan untuk menghadapi tantangan global, sekaligus mendukung daya saing nasional."

Prof. Dr. Abdul Mu'ti, M.Ed.

Menteri Pendidikan Dasar dan Menengah Republik Indonesia

"Program ini menawarkan solusi utama integrasi layanan digital pendidikan khususnya Pauddasmen melalui Super-Apps Rumah Pendidikan untuk memudahkan pemerintah, sekolah, guru, orang tua, peserta didik, dan masyarakat dalam menggunakan seluruh layanan digital pendidikan untuk mewujudkan Pendidikan Bermutu untuk Semua."

Ir. Suharti, M.A., Ph.D.

Sekretaris Jenderal Kemendikdasmen Republik Indonesia

Prinsip Pengembangan Rumah Pendidikan
Kebijakan Integrasi Data

Membangun landasan kebijakan berbasis data yang saling terhubung dan saling memperkuat layanan.

Partisipasi Semesta Pengembangan Layanan

Melibatkan berbagai pihak mulai dari pemerintah, guru, murid, publik, swasta, hingga orang tua dalam pengembangan layanan pendidikan.

Integrasi Layanan Publik

Menghadirkan layanan yang terpadu sehingga memudahkan pengguna dalam mengakses pendidikan di Indonesia.

Nilai “RAMAH” menjadi landasan kami dalam melayani kebutuhan pengguna

Responsif

Cepat, tepat, dan bermartabat dalam menjawab aspirasi dan tantangan pendidikan.

Akuntabel

Transparan, terbuka, dan bertanggung jawab dalam tata kelola serta kebermanfaatan layanan.

Melayani

Memberi kemudahan dan yang terbaik sebagai wujud pengabdian kepada masyarakat.

Adaptif

Siap menghadapi perubahan dan tantangan masa depan dengan solusi yang relevan dan berbasis nilai.

Harmonis

Mendorong kerja sama yang hangat, profesional, dan saling menghargai dalam semangat partisipasi semesta.

Kementerian Pendidikan Dasar dan Menengah

Kompleks Kementerian Pendidikan dan Kebudayaan, Senayan, Jakarta Pusat 10270

Navigasi

Pusat Bantuan Rumah Pendidikan

Syarat & Ketentuan

Kebijakan Privasi

Unduh Aplikasi Rumah Pendidikan</div>
                    </div>
                </div>
                
                    </div>
                </div>
            </div>
            
            <div class="scenario-section">
                <div class="scenario-header" onclick="toggleScenario(this)">
                    <div class="scenario-info">
                        <span class="scenario-icon">❌</span>
                        <span class="scenario-title">TEST_RP-309</span>
                        <span class="scenario-status failed">FAILED</span>
                    </div>
                    <div class="scenario-meta">
                        <span class="execution-time">⏱️ 40.7s</span>
                        <span class="steps-summary">📊 3/5 passed</span>
                        <span class="toggle-arrow">▼</span>
                    </div>
                </div>
                <div class="scenario-content" style="display: none;">
                    <div class="steps-container">
                        
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 1</span>
                        <span class="step-text">Given the user is on the "Rumah Pendidikan" homepage</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Navigated to "rumah pendidikan" home</div>
                        
                    </div>
                </div>
                
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 2</span>
                        <span class="step-text">When I scroll to the "Pertanyaan yang paling sering ditanyakan" section</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Scrolled page</div>
                        
                    </div>
                </div>
                
                <div class="step failed">
                    <div class="step-header">
                        <span class="step-icon">❌</span>
                        <span class="step-number">Step 3</span>
                        <span class="step-text">Then I should see the content</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: FAILED</div>
                        <div class="step-result">Result: Assertion failed for see the content</div>
                        <div class="step-error">Error: Expected: see the content, Actual: Langsung ke konten utama
Rumah Pendidikan
Cari
Pusat Informasi
Masuk
Jelajahi Ruang di Rumah Pendidikan

Ruang GTK

Ruang Murid

Ruang Sekolah

Ruang Bahasa

Ruang Pemerintah

Ruang Mitra

Ruang Publi</div>
                    </div>
                </div>
                
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 4</span>
                        <span class="step-text">When I click the element</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Clicked the element</div>
                        
                    </div>
                </div>
                
                <div class="step failed">
                    <div class="step-header">
                        <span class="step-icon">❌</span>
                        <span class="step-number">Step 5</span>
                        <span class="step-text">Then the answer related to the selected question should be displayed</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: FAILED</div>
                        <div class="step-result">Result: Assertion failed for be displayed</div>
                        <div class="step-error">Error: Expected: be displayed, Actual: Rumah Pendidikan berkomitmen untuk menjadi pusat ruang kolaborasi bagi siswa, guru, orang tua, hingga pemerintah daerah</div>
                    </div>
                </div>
                
                    </div>
                </div>
            </div>
            
            <div class="scenario-section">
                <div class="scenario-header" onclick="toggleScenario(this)">
                    <div class="scenario-info">
                        <span class="scenario-icon">❌</span>
                        <span class="scenario-title">TEST_RP-327</span>
                        <span class="scenario-status failed">FAILED</span>
                    </div>
                    <div class="scenario-meta">
                        <span class="execution-time">⏱️ 61.3s</span>
                        <span class="steps-summary">📊 1/6 passed</span>
                        <span class="toggle-arrow">▼</span>
                    </div>
                </div>
                <div class="scenario-content" style="display: none;">
                    <div class="steps-container">
                        
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 1</span>
                        <span class="step-text">Given the user is on the Rumah Pendidikan homepage</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Navigated to rumah pendidikan home</div>
                        
                    </div>
                </div>
                
                <div class="step skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-number">Step 2</span>
                        <span class="step-text">When the user scrolls to the Footer section</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: SKIPPED</div>
                        <div class="step-result">Result: Unknown or unsupported action for step: the user scrolls to the Footer section</div>
                        <div class="step-error">Error: Unknown or unsupported action</div>
                    </div>
                </div>
                
                <div class="step failed">
                    <div class="step-header">
                        <span class="step-icon">❌</span>
                        <span class="step-number">Step 3</span>
                        <span class="step-text">Then the user should see a Logo+Name of Kementrian on Footer</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: FAILED</div>
                        <div class="step-result">Result: Assertion failed for see a logo+name of kementrian on footer</div>
                        <div class="step-error">Error: Expected: see a logo+name of kementrian on footer, Actual: Langsung ke konten utama
Rumah Pendidikan
Cari
Pusat Informasi
Masuk
Jelajahi Ruang di Rumah Pendidikan

Ruang GTK

Ruang Murid

Ruang Sekolah

Ruang Bahasa

Ruang Pemerintah

Ruang Mitra

Ruang Publi</div>
                    </div>
                </div>
                
                <div class="step failed">
                    <div class="step-header">
                        <span class="step-icon">❌</span>
                        <span class="step-number">Step 4</span>
                        <span class="step-text">And the user should see Layanan Ruang Segmen on Footer</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: FAILED</div>
                        <div class="step-result">Result: Assertion failed for see layanan ruang segmen on footer</div>
                        <div class="step-error">Error: Expected: see layanan ruang segmen on footer, Actual: Jelajahi Ruang di Rumah Pendidikan

Ruang GTK

Ruang Murid

Ruang Sekolah

Ruang Bahasa

Ruang Pemerintah

Ruang Mitra

Ruang Publik

Ruang Orang Tua

Semangat Rumah Pendidikan

Rumah Pendidikan berkomitmen untuk menjadi pusat ruang kolaborasi bagi siswa, guru, orang tua, hingga pemerintah daerah

Pelajari Selengkapnya
Layanan Unggulan di Rumah Pendidikan

Sumber Belajar

Portal pembelajaran digital interaktif untuk semua jenjang.

Buku Bacaan Digital

Pusat Buku Digital untuk Gerakan Literasi Nasional.

Pengelolaan Kinerja

Perencanaan, pelaksanaan, dan penilaian Anda untuk pengembangan diri dan satdik.

Diklat

Pelatihan terbimbing menggunakan LMS.

Pelatihan Mandiri

Materi untuk pengembangan kompetensi Guru dan Tendik.

Siapa saja yang sudah memanfaatkan kemudahan kolaborasi di Rumah Pendidikan?

Simak perjalanan mereka

"Rumah Pendidikan merupakan gagasan yang sangat menarik dan terdepan untuk perkembangan zaman terkini. Di mana pergeseran paradigma baru, pendidikan tidak hanya sekedar di dalam kelas tapi juga bisa di ruang keluarga dan masyarakat."

Arham, S.Pd., M.Pd

Kepala Sekolah SD INPRES 33 Birobuli, Palu

"Kami menyambut baik dengan adanya rumah pendidikan ini sebagai langkah strategis Kemendikdasmen terutama memperluas akses pendidikan dan pemerataan mutu pendidikan. Karena diharapkan dengan adanya Rumdik ini sebagai salah satu langkah pendidikan yang Responsif, Akuntabel, Melayani, Adaptif, dan Harmonis. Semoga menjadi langkah terbaik untuk Indonesia maju."

Dr. Firman Oktora, S.SI, M.PD.

Kabalaitekkomdik Jawa Barat

"Menurut saya Rumah Pendidikan sangat keren dan sangat membantu. Karena bagi saya murid kelas 6, Rumah Pendidikan bisa membantu dalam belajar dengan simpel, praktis, dan bisa mempelajari berbagai macam hal dalam satu platform, jadi sangat membantu."

Alena

Pelajar Gen KiHajar

Informasi Untuk Anda

Berita
Sambut Tahun Ajaran Baru 2025 dengan Penuh Inspirasi!
14 Juli 2025 | 10.32 WIB
Baca Selengkapnya
Berita
Laman Direktori Portal SPMB Berbasis SIAP SPMB Online
11 Juli 2025 | 15.13 WIB
Baca Selengkapnya
Berita
Tanya Jawab seputar Tes Kemampuan Akademik (ТKА)
24 Juni 2025 | 15.00 WIB
Baca Selengkapnya
Lihat lebih banyak
Pertanyaan yang paling sering ditanyakan
Apa manfaat menggunakan Rumah Pendidikan?
Bagaimana cara mengakses Rumah Pendidikan?
Apakah pengguna harus menggunakan Akun belajar.id untuk mengakses Rumah Pendidikan?
Siapa saja pengguna yang disarankan untuk menggunakan platform Rumah Pendidikan
Apa perbedaan rumah pendidikan dengan platform digital pendidikan milik Kemendikdasmen lainnya?
Ke Pusat Informasi</div>
                    </div>
                </div>
                
                <div class="step failed">
                    <div class="step-header">
                        <span class="step-icon">❌</span>
                        <span class="step-number">Step 5</span>
                        <span class="step-text">And the user should see Navigasi Segmen on Footer</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: FAILED</div>
                        <div class="step-result">Result: Assertion failed for see navigasi segmen on footer</div>
                        <div class="step-error">Error: Expected: see navigasi segmen on footer, Actual: Langsung ke konten utama
Rumah Pendidikan
Cari
Pusat Informasi
Masuk
Jelajahi Ruang di Rumah Pendidikan

Ruang GTK

Ruang Murid

Ruang Sekolah

Ruang Bahasa

Ruang Pemerintah

Ruang Mitra

Ruang Publik

Ruang Orang Tua

Semangat Rumah Pendidikan

Rumah Pendidikan berkomitmen untuk menjadi pusat ruang kolaborasi bagi siswa, guru, orang tua, hingga pemerintah daerah

Pelajari Selengkapnya
Layanan Unggulan di Rumah Pendidikan

Sumber Belajar

Portal pembelajaran digital interaktif untuk semua jenjang.

Buku Bacaan Digital

Pusat Buku Digital untuk Gerakan Literasi Nasional.

Pengelolaan Kinerja

Perencanaan, pelaksanaan, dan penilaian Anda untuk pengembangan diri dan satdik.

Diklat

Pelatihan terbimbing menggunakan LMS.

Pelatihan Mandiri

Materi untuk pengembangan kompetensi Guru dan Tendik.

Siapa saja yang sudah memanfaatkan kemudahan kolaborasi di Rumah Pendidikan?

Simak perjalanan mereka

"Rumah Pendidikan merupakan gagasan yang sangat menarik dan terdepan untuk perkembangan zaman terkini. Di mana pergeseran paradigma baru, pendidikan tidak hanya sekedar di dalam kelas tapi juga bisa di ruang keluarga dan masyarakat."

Arham, S.Pd., M.Pd

Kepala Sekolah SD INPRES 33 Birobuli, Palu

"Kami menyambut baik dengan adanya rumah pendidikan ini sebagai langkah strategis Kemendikdasmen terutama memperluas akses pendidikan dan pemerataan mutu pendidikan. Karena diharapkan dengan adanya Rumdik ini sebagai salah satu langkah pendidikan yang Responsif, Akuntabel, Melayani, Adaptif, dan Harmonis. Semoga menjadi langkah terbaik untuk Indonesia maju."

Dr. Firman Oktora, S.SI, M.PD.

Kabalaitekkomdik Jawa Barat

"Menurut saya Rumah Pendidikan sangat keren dan sangat membantu. Karena bagi saya murid kelas 6, Rumah Pendidikan bisa membantu dalam belajar dengan simpel, praktis, dan bisa mempelajari berbagai macam hal dalam satu platform, jadi sangat membantu."

Alena

Pelajar Gen KiHajar

Informasi Untuk Anda

Berita
Sambut Tahun Ajaran Baru 2025 dengan Penuh Inspirasi!
14 Juli 2025 | 10.32 WIB
Baca Selengkapnya
Berita
Laman Direktori Portal SPMB Berbasis SIAP SPMB Online
11 Juli 2025 | 15.13 WIB
Baca Selengkapnya
Berita
Tanya Jawab seputar Tes Kemampuan Akademik (ТKА)
24 Juni 2025 | 15.00 WIB
Baca Selengkapnya
Lihat lebih banyak
Pertanyaan yang paling sering ditanyakan
Apa manfaat menggunakan Rumah Pendidikan?
Bagaimana cara mengakses Rumah Pendidikan?
Apakah pengguna harus menggunakan Akun belajar.id untuk mengakses Rumah Pendidikan?
Siapa saja pengguna yang disarankan untuk menggunakan platform Rumah Pendidikan
Apa perbedaan rumah pendidikan dengan platform digital pendidikan milik Kemendikdasmen lainnya?
Ke Pusat Informasi

Kementerian Pendidikan Dasar dan Menengah

Kompleks Kementerian Pendidikan dan Kebudayaan, Senayan, Jakarta Pusat 10270

Navigasi

Pusat Bantuan Rumah Pendidikan

Syarat & Ketentuan

Kebijakan Privasi

Unduh Aplikasi Rumah Pendidikan</div>
                    </div>
                </div>
                
                <div class="step failed">
                    <div class="step-header">
                        <span class="step-icon">❌</span>
                        <span class="step-number">Step 6</span>
                        <span class="step-text">And the user should see Entry Point to Native Apps Segmen on Footer</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: FAILED</div>
                        <div class="step-result">Result: Assertion failed for see entry point to native apps segmen on footer</div>
                        <div class="step-error">Error: Expected: see entry point to native apps segmen on footer, Actual: Langsung ke konten utama
Rumah Pendidikan
Cari
Pusat Informasi
Masuk
Jelajahi Ruang di Rumah Pendidikan

Ruang GTK

Ruang Murid

Ruang Sekolah

Ruang Bahasa

Ruang Pemerintah

Ruang Mitra

Ruang Publi</div>
                    </div>
                </div>
                
                    </div>
                </div>
            </div>
            
            <div class="scenario-section">
                <div class="scenario-header" onclick="toggleScenario(this)">
                    <div class="scenario-info">
                        <span class="scenario-icon">⏭️</span>
                        <span class="scenario-title">TEST_RP-328</span>
                        <span class="scenario-status failed">FAILED</span>
                    </div>
                    <div class="scenario-meta">
                        <span class="execution-time">⏱️ 1.9s</span>
                        <span class="steps-summary">📊 3/4 passed</span>
                        <span class="toggle-arrow">▼</span>
                    </div>
                </div>
                <div class="scenario-content" style="display: none;">
                    <div class="steps-container">
                        
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 1</span>
                        <span class="step-text">Given the user is on the Rumah Pendidikan homepage</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Navigated to rumah pendidikan home</div>
                        
                    </div>
                </div>
                
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 2</span>
                        <span class="step-text">When I scroll to the section</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Scrolled page</div>
                        
                    </div>
                </div>
                
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 3</span>
                        <span class="step-text">And user click "Ruang GTK"</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Clicked ruang gtk</div>
                        
                    </div>
                </div>
                
                <div class="step skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-number">Step 4</span>
                        <span class="step-text">Then user direct to Ruang GTK Pages "https://rumah-baru.staging.belajar.id/ruang/gtk"</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: SKIPPED</div>
                        <div class="step-result">Result: Unknown or unsupported action for step: user direct to Ruang GTK Pages "https://rumah-baru.staging.belajar.id/ruang/gtk"</div>
                        <div class="step-error">Error: Unknown or unsupported action</div>
                    </div>
                </div>
                
                    </div>
                </div>
            </div>
            
            <div class="scenario-section">
                <div class="scenario-header" onclick="toggleScenario(this)">
                    <div class="scenario-info">
                        <span class="scenario-icon">⏭️</span>
                        <span class="scenario-title">TEST_RP-329</span>
                        <span class="scenario-status failed">FAILED</span>
                    </div>
                    <div class="scenario-meta">
                        <span class="execution-time">⏱️ 1.1s</span>
                        <span class="steps-summary">📊 3/4 passed</span>
                        <span class="toggle-arrow">▼</span>
                    </div>
                </div>
                <div class="scenario-content" style="display: none;">
                    <div class="steps-container">
                        
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 1</span>
                        <span class="step-text">Given the user is on the Rumah Pendidikan homepage</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Navigated to rumah pendidikan home</div>
                        
                    </div>
                </div>
                
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 2</span>
                        <span class="step-text">When I scroll to the section</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Scrolled page</div>
                        
                    </div>
                </div>
                
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 3</span>
                        <span class="step-text">And user click "Ruang Murid"</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Clicked ruang murid</div>
                        
                    </div>
                </div>
                
                <div class="step skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-number">Step 4</span>
                        <span class="step-text">Then user direct to Ruang Murid Pages "https://rumah-baru.staging.belajar.id/ruang/murid"</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: SKIPPED</div>
                        <div class="step-result">Result: Unknown or unsupported action for step: user direct to Ruang Murid Pages "https://rumah-baru.staging.belajar.id/ruang/murid"</div>
                        <div class="step-error">Error: Unknown or unsupported action</div>
                    </div>
                </div>
                
                    </div>
                </div>
            </div>
            
            <div class="scenario-section">
                <div class="scenario-header" onclick="toggleScenario(this)">
                    <div class="scenario-info">
                        <span class="scenario-icon">⏭️</span>
                        <span class="scenario-title">TEST_RP-330</span>
                        <span class="scenario-status failed">FAILED</span>
                    </div>
                    <div class="scenario-meta">
                        <span class="execution-time">⏱️ 1.5s</span>
                        <span class="steps-summary">📊 3/4 passed</span>
                        <span class="toggle-arrow">▼</span>
                    </div>
                </div>
                <div class="scenario-content" style="display: none;">
                    <div class="steps-container">
                        
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 1</span>
                        <span class="step-text">Given the user is on the Rumah Pendidikan homepage</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Navigated to rumah pendidikan home</div>
                        
                    </div>
                </div>
                
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 2</span>
                        <span class="step-text">When I scroll to the section</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Scrolled page</div>
                        
                    </div>
                </div>
                
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 3</span>
                        <span class="step-text">And user click "Ruang Sekolah"</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Clicked ruang sekolah</div>
                        
                    </div>
                </div>
                
                <div class="step skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-number">Step 4</span>
                        <span class="step-text">Then user direct to Ruang Sekolah Pages "https://rumah-baru.staging.belajar.id/ruang/sekolah"</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: SKIPPED</div>
                        <div class="step-result">Result: Unknown or unsupported action for step: user direct to Ruang Sekolah Pages "https://rumah-baru.staging.belajar.id/ruang/sekolah"</div>
                        <div class="step-error">Error: Unknown or unsupported action</div>
                    </div>
                </div>
                
                    </div>
                </div>
            </div>
            
            <div class="scenario-section">
                <div class="scenario-header" onclick="toggleScenario(this)">
                    <div class="scenario-info">
                        <span class="scenario-icon">⏭️</span>
                        <span class="scenario-title">TEST_RP-331</span>
                        <span class="scenario-status failed">FAILED</span>
                    </div>
                    <div class="scenario-meta">
                        <span class="execution-time">⏱️ 1.2s</span>
                        <span class="steps-summary">📊 3/4 passed</span>
                        <span class="toggle-arrow">▼</span>
                    </div>
                </div>
                <div class="scenario-content" style="display: none;">
                    <div class="steps-container">
                        
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 1</span>
                        <span class="step-text">Given the user is on the Rumah Pendidikan homepage</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Navigated to rumah pendidikan home</div>
                        
                    </div>
                </div>
                
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 2</span>
                        <span class="step-text">When I scroll to the section</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Scrolled page</div>
                        
                    </div>
                </div>
                
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 3</span>
                        <span class="step-text">And user click "Ruang Bahasa"</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Clicked ruang bahasa</div>
                        
                    </div>
                </div>
                
                <div class="step skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-number">Step 4</span>
                        <span class="step-text">Then user direct to Ruang Bahasa Pages "https://rumah-baru.staging.belajar.id/ruang/bahasa"</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: SKIPPED</div>
                        <div class="step-result">Result: Unknown or unsupported action for step: user direct to Ruang Bahasa Pages "https://rumah-baru.staging.belajar.id/ruang/bahasa"</div>
                        <div class="step-error">Error: Unknown or unsupported action</div>
                    </div>
                </div>
                
                    </div>
                </div>
            </div>
            
            <div class="scenario-section">
                <div class="scenario-header" onclick="toggleScenario(this)">
                    <div class="scenario-info">
                        <span class="scenario-icon">⏭️</span>
                        <span class="scenario-title">TEST_RP-332</span>
                        <span class="scenario-status failed">FAILED</span>
                    </div>
                    <div class="scenario-meta">
                        <span class="execution-time">⏱️ 2.1s</span>
                        <span class="steps-summary">📊 3/4 passed</span>
                        <span class="toggle-arrow">▼</span>
                    </div>
                </div>
                <div class="scenario-content" style="display: none;">
                    <div class="steps-container">
                        
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 1</span>
                        <span class="step-text">Given the user is on the Rumah Pendidikan homepage</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Navigated to rumah pendidikan home</div>
                        
                    </div>
                </div>
                
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 2</span>
                        <span class="step-text">When I scroll to the section</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Scrolled page</div>
                        
                    </div>
                </div>
                
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 3</span>
                        <span class="step-text">And user click "Ruang Pemerintah"</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Clicked ruang pemerintah</div>
                        
                    </div>
                </div>
                
                <div class="step skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-number">Step 4</span>
                        <span class="step-text">Then user direct to Ruang Pemerintah Pages "https://rumah-baru.staging.belajar.id/ruang/pemerintah"</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: SKIPPED</div>
                        <div class="step-result">Result: Unknown or unsupported action for step: user direct to Ruang Pemerintah Pages "https://rumah-baru.staging.belajar.id/ruang/pemerintah"</div>
                        <div class="step-error">Error: Unknown or unsupported action</div>
                    </div>
                </div>
                
                    </div>
                </div>
            </div>
            
            <div class="scenario-section">
                <div class="scenario-header" onclick="toggleScenario(this)">
                    <div class="scenario-info">
                        <span class="scenario-icon">⏭️</span>
                        <span class="scenario-title">TEST_RP-333</span>
                        <span class="scenario-status failed">FAILED</span>
                    </div>
                    <div class="scenario-meta">
                        <span class="execution-time">⏱️ 1.2s</span>
                        <span class="steps-summary">📊 3/4 passed</span>
                        <span class="toggle-arrow">▼</span>
                    </div>
                </div>
                <div class="scenario-content" style="display: none;">
                    <div class="steps-container">
                        
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 1</span>
                        <span class="step-text">Given the user is on the Rumah Pendidikan homepage</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Navigated to rumah pendidikan home</div>
                        
                    </div>
                </div>
                
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 2</span>
                        <span class="step-text">When I scroll to the section</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Scrolled page</div>
                        
                    </div>
                </div>
                
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 3</span>
                        <span class="step-text">And user click "Ruang Mitra"</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Clicked ruang mitra</div>
                        
                    </div>
                </div>
                
                <div class="step skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-number">Step 4</span>
                        <span class="step-text">Then user direct to Ruang Mitra "https://rumah-baru.staging.belajar.id/ruang/mitra"</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: SKIPPED</div>
                        <div class="step-result">Result: Unknown or unsupported action for step: user direct to Ruang Mitra "https://rumah-baru.staging.belajar.id/ruang/mitra"</div>
                        <div class="step-error">Error: Unknown or unsupported action</div>
                    </div>
                </div>
                
                    </div>
                </div>
            </div>
            
            <div class="scenario-section">
                <div class="scenario-header" onclick="toggleScenario(this)">
                    <div class="scenario-info">
                        <span class="scenario-icon">⏭️</span>
                        <span class="scenario-title">TEST_RP-334</span>
                        <span class="scenario-status failed">FAILED</span>
                    </div>
                    <div class="scenario-meta">
                        <span class="execution-time">⏱️ 1.0s</span>
                        <span class="steps-summary">📊 3/4 passed</span>
                        <span class="toggle-arrow">▼</span>
                    </div>
                </div>
                <div class="scenario-content" style="display: none;">
                    <div class="steps-container">
                        
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 1</span>
                        <span class="step-text">Given the user is on the Rumah Pendidikan homepage</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Navigated to rumah pendidikan home</div>
                        
                    </div>
                </div>
                
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 2</span>
                        <span class="step-text">When I scroll to the section</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Scrolled page</div>
                        
                    </div>
                </div>
                
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 3</span>
                        <span class="step-text">And user click "Ruang Publik"</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Clicked ruang publik</div>
                        
                    </div>
                </div>
                
                <div class="step skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-number">Step 4</span>
                        <span class="step-text">Then user direct to Ruang Publik Pages "https://rumah-baru.staging.belajar.id/ruang/publik"</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: SKIPPED</div>
                        <div class="step-result">Result: Unknown or unsupported action for step: user direct to Ruang Publik Pages "https://rumah-baru.staging.belajar.id/ruang/publik"</div>
                        <div class="step-error">Error: Unknown or unsupported action</div>
                    </div>
                </div>
                
                    </div>
                </div>
            </div>
            
            <div class="scenario-section">
                <div class="scenario-header" onclick="toggleScenario(this)">
                    <div class="scenario-info">
                        <span class="scenario-icon">⏭️</span>
                        <span class="scenario-title">TEST_RP-335</span>
                        <span class="scenario-status failed">FAILED</span>
                    </div>
                    <div class="scenario-meta">
                        <span class="execution-time">⏱️ 1.4s</span>
                        <span class="steps-summary">📊 3/4 passed</span>
                        <span class="toggle-arrow">▼</span>
                    </div>
                </div>
                <div class="scenario-content" style="display: none;">
                    <div class="steps-container">
                        
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 1</span>
                        <span class="step-text">Given the user is on the Rumah Pendidikan homepage</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Navigated to rumah pendidikan home</div>
                        
                    </div>
                </div>
                
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 2</span>
                        <span class="step-text">When I scroll to the section</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Scrolled page</div>
                        
                    </div>
                </div>
                
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 3</span>
                        <span class="step-text">And user click "Ruang Orang Tua"</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Clicked ruang orang tua</div>
                        
                    </div>
                </div>
                
                <div class="step skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-number">Step 4</span>
                        <span class="step-text">Then user direct to Ruang Orang Tua Pages "https://rumah-baru.staging.belajar.id/ruang/orang-tua"</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: SKIPPED</div>
                        <div class="step-result">Result: Unknown or unsupported action for step: user direct to Ruang Orang Tua Pages "https://rumah-baru.staging.belajar.id/ruang/orang-tua"</div>
                        <div class="step-error">Error: Unknown or unsupported action</div>
                    </div>
                </div>
                
                    </div>
                </div>
            </div>
            
            <div class="scenario-section">
                <div class="scenario-header" onclick="toggleScenario(this)">
                    <div class="scenario-info">
                        <span class="scenario-icon">⏭️</span>
                        <span class="scenario-title">TEST_RP-336</span>
                        <span class="scenario-status failed">FAILED</span>
                    </div>
                    <div class="scenario-meta">
                        <span class="execution-time">⏱️ 1.8s</span>
                        <span class="steps-summary">📊 3/4 passed</span>
                        <span class="toggle-arrow">▼</span>
                    </div>
                </div>
                <div class="scenario-content" style="display: none;">
                    <div class="steps-container">
                        
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 1</span>
                        <span class="step-text">Given the user is on the Rumah Pendidikan homepage</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Navigated to rumah pendidikan home</div>
                        
                    </div>
                </div>
                
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 2</span>
                        <span class="step-text">When I scroll to the section</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Scrolled page</div>
                        
                    </div>
                </div>
                
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 3</span>
                        <span class="step-text">And user click "Pusat Bantuan Rumah Pendidikan"</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Clicked pusat bantuan rumah pendidikan</div>
                        
                    </div>
                </div>
                
                <div class="step skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-number">Step 4</span>
                        <span class="step-text">Then user direct to Pusat Bantuan Rumah Pendidikan Pages "https://pengaduan.ult.kemendikdasmen.go.id/hc/en-gb/requests/new"</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: SKIPPED</div>
                        <div class="step-result">Result: Unknown or unsupported action for step: user direct to Pusat Bantuan Rumah Pendidikan Pages "https://pengaduan.ult.kemendikdasmen.go.id/hc/en-gb/requests/new"</div>
                        <div class="step-error">Error: Unknown or unsupported action</div>
                    </div>
                </div>
                
                    </div>
                </div>
            </div>
            
            <div class="scenario-section">
                <div class="scenario-header" onclick="toggleScenario(this)">
                    <div class="scenario-info">
                        <span class="scenario-icon">⏭️</span>
                        <span class="scenario-title">TEST_RP-337</span>
                        <span class="scenario-status failed">FAILED</span>
                    </div>
                    <div class="scenario-meta">
                        <span class="execution-time">⏱️ 0.9s</span>
                        <span class="steps-summary">📊 3/4 passed</span>
                        <span class="toggle-arrow">▼</span>
                    </div>
                </div>
                <div class="scenario-content" style="display: none;">
                    <div class="steps-container">
                        
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 1</span>
                        <span class="step-text">Given the user is on the Rumah Pendidikan homepage</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Navigated to rumah pendidikan home</div>
                        
                    </div>
                </div>
                
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 2</span>
                        <span class="step-text">When I scroll to the section</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Scrolled page</div>
                        
                    </div>
                </div>
                
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 3</span>
                        <span class="step-text">And user click "Syarat & Ketentuan"</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Clicked syarat & ketentuan</div>
                        
                    </div>
                </div>
                
                <div class="step skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-number">Step 4</span>
                        <span class="step-text">Then user direct to Syarat & Ketentuan Pages "https://rumah-baru.staging.belajar.id/syarat-dan-ketentuan"</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: SKIPPED</div>
                        <div class="step-result">Result: Unknown or unsupported action for step: user direct to Syarat & Ketentuan Pages "https://rumah-baru.staging.belajar.id/syarat-dan-ketentuan"</div>
                        <div class="step-error">Error: Unknown or unsupported action</div>
                    </div>
                </div>
                
                    </div>
                </div>
            </div>
            
            <div class="scenario-section">
                <div class="scenario-header" onclick="toggleScenario(this)">
                    <div class="scenario-info">
                        <span class="scenario-icon">⏭️</span>
                        <span class="scenario-title">TEST_RP-338</span>
                        <span class="scenario-status failed">FAILED</span>
                    </div>
                    <div class="scenario-meta">
                        <span class="execution-time">⏱️ 1.6s</span>
                        <span class="steps-summary">📊 3/4 passed</span>
                        <span class="toggle-arrow">▼</span>
                    </div>
                </div>
                <div class="scenario-content" style="display: none;">
                    <div class="steps-container">
                        
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 1</span>
                        <span class="step-text">Given the user is on the Rumah Pendidikan homepage</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Navigated to rumah pendidikan home</div>
                        
                    </div>
                </div>
                
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 2</span>
                        <span class="step-text">When I scroll to the section</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Scrolled page</div>
                        
                    </div>
                </div>
                
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 3</span>
                        <span class="step-text">And user click "Kebijakan Privasi"</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Clicked kebijakan privasi</div>
                        
                    </div>
                </div>
                
                <div class="step skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-number">Step 4</span>
                        <span class="step-text">Then user direct to Kebijakan Privasi Pages "https://rumah-baru.staging.belajar.id/kebijakan-privasi"</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: SKIPPED</div>
                        <div class="step-result">Result: Unknown or unsupported action for step: user direct to Kebijakan Privasi Pages "https://rumah-baru.staging.belajar.id/kebijakan-privasi"</div>
                        <div class="step-error">Error: Unknown or unsupported action</div>
                    </div>
                </div>
                
                    </div>
                </div>
            </div>
            
            <div class="scenario-section">
                <div class="scenario-header" onclick="toggleScenario(this)">
                    <div class="scenario-info">
                        <span class="scenario-icon">⏭️</span>
                        <span class="scenario-title">TEST_RP-339</span>
                        <span class="scenario-status failed">FAILED</span>
                    </div>
                    <div class="scenario-meta">
                        <span class="execution-time">⏱️ 10.3s</span>
                        <span class="steps-summary">📊 3/4 passed</span>
                        <span class="toggle-arrow">▼</span>
                    </div>
                </div>
                <div class="scenario-content" style="display: none;">
                    <div class="steps-container">
                        
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 1</span>
                        <span class="step-text">Given the user is on the Rumah Pendidikan homepage</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Navigated to rumah pendidikan home</div>
                        
                    </div>
                </div>
                
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 2</span>
                        <span class="step-text">When I scroll to the section</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Scrolled page</div>
                        
                    </div>
                </div>
                
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 3</span>
                        <span class="step-text">And user click Apps Store Logo</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Clicked apps store logo</div>
                        
                    </div>
                </div>
                
                <div class="step skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-number">Step 4</span>
                        <span class="step-text">Then user direct to Native Apps Rumah Pendidikan IOS</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: SKIPPED</div>
                        <div class="step-result">Result: Unknown or unsupported action for step: user direct to Native Apps Rumah Pendidikan IOS</div>
                        <div class="step-error">Error: Unknown or unsupported action</div>
                    </div>
                </div>
                
                    </div>
                </div>
            </div>
            
            <div class="scenario-section">
                <div class="scenario-header" onclick="toggleScenario(this)">
                    <div class="scenario-info">
                        <span class="scenario-icon">⏭️</span>
                        <span class="scenario-title">TEST_RP-340</span>
                        <span class="scenario-status failed">FAILED</span>
                    </div>
                    <div class="scenario-meta">
                        <span class="execution-time">⏱️ 10.3s</span>
                        <span class="steps-summary">📊 3/4 passed</span>
                        <span class="toggle-arrow">▼</span>
                    </div>
                </div>
                <div class="scenario-content" style="display: none;">
                    <div class="steps-container">
                        
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 1</span>
                        <span class="step-text">Given the user is on the Rumah Pendidikan homepage</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Navigated to rumah pendidikan home</div>
                        
                    </div>
                </div>
                
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 2</span>
                        <span class="step-text">When I scroll to the section</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Scrolled page</div>
                        
                    </div>
                </div>
                
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 3</span>
                        <span class="step-text">And user click Google Play Store Logo</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Clicked google play store logo</div>
                        
                    </div>
                </div>
                
                <div class="step skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-number">Step 4</span>
                        <span class="step-text">Then user direct to Native Apps Rumah Pendidikan Android</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: SKIPPED</div>
                        <div class="step-result">Result: Unknown or unsupported action for step: user direct to Native Apps Rumah Pendidikan Android</div>
                        <div class="step-error">Error: Unknown or unsupported action</div>
                    </div>
                </div>
                
                    </div>
                </div>
            </div>
            
            <div class="scenario-section">
                <div class="scenario-header" onclick="toggleScenario(this)">
                    <div class="scenario-info">
                        <span class="scenario-icon">❌</span>
                        <span class="scenario-title">TEST_RP-342</span>
                        <span class="scenario-status failed">FAILED</span>
                    </div>
                    <div class="scenario-meta">
                        <span class="execution-time">⏱️ 15.6s</span>
                        <span class="steps-summary">📊 1/3 passed</span>
                        <span class="toggle-arrow">▼</span>
                    </div>
                </div>
                <div class="scenario-content" style="display: none;">
                    <div class="steps-container">
                        
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 1</span>
                        <span class="step-text">Given user is on the Rumah Pendidikan homepage</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Navigated to rumah pendidikan home</div>
                        
                    </div>
                </div>
                
                <div class="step skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-number">Step 2</span>
                        <span class="step-text">When user scrolls to the middle page</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: SKIPPED</div>
                        <div class="step-result">Result: Unknown or unsupported action for step: user scrolls to the middle page</div>
                        <div class="step-error">Error: Unknown or unsupported action</div>
                    </div>
                </div>
                
                <div class="step failed">
                    <div class="step-header">
                        <span class="step-icon">❌</span>
                        <span class="step-number">Step 3</span>
                        <span class="step-text">Then user should see The Title and Description of Web "Semangat Rumah Pendidikan"</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: FAILED</div>
                        <div class="step-result">Result: Assertion failed for see the title and description of web </div>
                        <div class="step-error">Error: Expected: see the title and description of web , Actual: Langsung ke konten utama
Rumah Pendidikan
Cari
Pusat Informasi
Masuk
Jelajahi Ruang di Rumah Pendidikan

Ruang GTK

Ruang Murid

Ruang Sekolah

Ruang Bahasa

Ruang Pemerintah

Ruang Mitra

Ruang Publik

Ruang Orang Tua

Semangat Rumah Pendidikan

Rumah Pendidikan berkomitmen untuk menjadi pusat ruang kolaborasi bagi siswa, guru, orang tua, hingga pemerintah daerah

Pelajari Selengkapnya
Layanan Unggulan di Rumah Pendidikan

Sumber Belajar

Portal pembelajaran digital interaktif untuk semua jenjang.

Buku Bacaan Digital

Pusat Buku Digital untuk Gerakan Literasi Nasional.

Pengelolaan Kinerja

Perencanaan, pelaksanaan, dan penilaian Anda untuk pengembangan diri dan satdik.

Diklat

Pelatihan terbimbing menggunakan LMS.

Pelatihan Mandiri

Materi untuk pengembangan kompetensi Guru dan Tendik.

Siapa saja yang sudah memanfaatkan kemudahan kolaborasi di Rumah Pendidikan?

Simak perjalanan mereka

"Rumah Pendidikan merupakan gagasan yang sangat menarik dan terdepan untuk perkembangan zaman terkini. Di mana pergeseran paradigma baru, pendidikan tidak hanya sekedar di dalam kelas tapi juga bisa di ruang keluarga dan masyarakat."

Arham, S.Pd., M.Pd

Kepala Sekolah SD INPRES 33 Birobuli, Palu

"Kami menyambut baik dengan adanya rumah pendidikan ini sebagai langkah strategis Kemendikdasmen terutama memperluas akses pendidikan dan pemerataan mutu pendidikan. Karena diharapkan dengan adanya Rumdik ini sebagai salah satu langkah pendidikan yang Responsif, Akuntabel, Melayani, Adaptif, dan Harmonis. Semoga menjadi langkah terbaik untuk Indonesia maju."

Dr. Firman Oktora, S.SI, M.PD.

Kabalaitekkomdik Jawa Barat

"Menurut saya Rumah Pendidikan sangat keren dan sangat membantu. Karena bagi saya murid kelas 6, Rumah Pendidikan bisa membantu dalam belajar dengan simpel, praktis, dan bisa mempelajari berbagai macam hal dalam satu platform, jadi sangat membantu."

Alena

Pelajar Gen KiHajar

Informasi Untuk Anda

Berita
Sambut Tahun Ajaran Baru 2025 dengan Penuh Inspirasi!
14 Juli 2025 | 10.32 WIB
Baca Selengkapnya
Berita
Laman Direktori Portal SPMB Berbasis SIAP SPMB Online
11 Juli 2025 | 15.13 WIB
Baca Selengkapnya
Berita
Tanya Jawab seputar Tes Kemampuan Akademik (ТKА)
24 Juni 2025 | 15.00 WIB
Baca Selengkapnya
Lihat lebih banyak
Pertanyaan yang paling sering ditanyakan
Apa manfaat menggunakan Rumah Pendidikan?
Bagaimana cara mengakses Rumah Pendidikan?
Apakah pengguna harus menggunakan Akun belajar.id untuk mengakses Rumah Pendidikan?
Siapa saja pengguna yang disarankan untuk menggunakan platform Rumah Pendidikan
Apa perbedaan rumah pendidikan dengan platform digital pendidikan milik Kemendikdasmen lainnya?
Ke Pusat Informasi

Kementerian Pendidikan Dasar dan Menengah

Kompleks Kementerian Pendidikan dan Kebudayaan, Senayan, Jakarta Pusat 10270

Navigasi

Pusat Bantuan Rumah Pendidikan

Syarat & Ketentuan

Kebijakan Privasi

Unduh Aplikasi Rumah Pendidikan</div>
                    </div>
                </div>
                
                    </div>
                </div>
            </div>
            
            <div class="scenario-section">
                <div class="scenario-header" onclick="toggleScenario(this)">
                    <div class="scenario-info">
                        <span class="scenario-icon">⏭️</span>
                        <span class="scenario-title">TEST_RP-344</span>
                        <span class="scenario-status failed">FAILED</span>
                    </div>
                    <div class="scenario-meta">
                        <span class="execution-time">⏱️ 10.5s</span>
                        <span class="steps-summary">📊 2/3 passed</span>
                        <span class="toggle-arrow">▼</span>
                    </div>
                </div>
                <div class="scenario-content" style="display: none;">
                    <div class="steps-container">
                        
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 1</span>
                        <span class="step-text">Given user is on the Rumah Pendidikan homepage</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Navigated to rumah pendidikan home</div>
                        
                    </div>
                </div>
                
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 2</span>
                        <span class="step-text">When I click Icon "Ruang GTK"</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Clicked icon </div>
                        
                    </div>
                </div>
                
                <div class="step skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-number">Step 3</span>
                        <span class="step-text">Then user direct to Ruang GTK Pages "https://rumah-baru.staging.belajar.id/ruang/gtk"</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: SKIPPED</div>
                        <div class="step-result">Result: Unknown or unsupported action for step: user direct to Ruang GTK Pages "https://rumah-baru.staging.belajar.id/ruang/gtk"</div>
                        <div class="step-error">Error: Unknown or unsupported action</div>
                    </div>
                </div>
                
                    </div>
                </div>
            </div>
            
            <div class="scenario-section">
                <div class="scenario-header" onclick="toggleScenario(this)">
                    <div class="scenario-info">
                        <span class="scenario-icon">⏭️</span>
                        <span class="scenario-title">TEST_RP-345</span>
                        <span class="scenario-status failed">FAILED</span>
                    </div>
                    <div class="scenario-meta">
                        <span class="execution-time">⏱️ 10.1s</span>
                        <span class="steps-summary">📊 2/3 passed</span>
                        <span class="toggle-arrow">▼</span>
                    </div>
                </div>
                <div class="scenario-content" style="display: none;">
                    <div class="steps-container">
                        
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 1</span>
                        <span class="step-text">Given user is on the Rumah Pendidikan homepage</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Navigated to rumah pendidikan home</div>
                        
                    </div>
                </div>
                
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 2</span>
                        <span class="step-text">When I click Icon "Ruang Murid"</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Clicked icon </div>
                        
                    </div>
                </div>
                
                <div class="step skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-number">Step 3</span>
                        <span class="step-text">Then user direct to Ruang Murid Pages "https://rumah-baru.staging.belajar.id/ruang/murid"</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: SKIPPED</div>
                        <div class="step-result">Result: Unknown or unsupported action for step: user direct to Ruang Murid Pages "https://rumah-baru.staging.belajar.id/ruang/murid"</div>
                        <div class="step-error">Error: Unknown or unsupported action</div>
                    </div>
                </div>
                
                    </div>
                </div>
            </div>
            
            <div class="scenario-section">
                <div class="scenario-header" onclick="toggleScenario(this)">
                    <div class="scenario-info">
                        <span class="scenario-icon">⏭️</span>
                        <span class="scenario-title">TEST_RP-346</span>
                        <span class="scenario-status failed">FAILED</span>
                    </div>
                    <div class="scenario-meta">
                        <span class="execution-time">⏱️ 10.2s</span>
                        <span class="steps-summary">📊 2/3 passed</span>
                        <span class="toggle-arrow">▼</span>
                    </div>
                </div>
                <div class="scenario-content" style="display: none;">
                    <div class="steps-container">
                        
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 1</span>
                        <span class="step-text">Given user is on the Rumah Pendidikan homepage</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Navigated to rumah pendidikan home</div>
                        
                    </div>
                </div>
                
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 2</span>
                        <span class="step-text">When I click Icon "Ruang Sekolah"</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Clicked icon </div>
                        
                    </div>
                </div>
                
                <div class="step skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-number">Step 3</span>
                        <span class="step-text">Then user direct to Ruang Sekolah Pages "https://rumah-baru.staging.belajar.id/ruang/sekolah"</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: SKIPPED</div>
                        <div class="step-result">Result: Unknown or unsupported action for step: user direct to Ruang Sekolah Pages "https://rumah-baru.staging.belajar.id/ruang/sekolah"</div>
                        <div class="step-error">Error: Unknown or unsupported action</div>
                    </div>
                </div>
                
                    </div>
                </div>
            </div>
            
            <div class="scenario-section">
                <div class="scenario-header" onclick="toggleScenario(this)">
                    <div class="scenario-info">
                        <span class="scenario-icon">⏭️</span>
                        <span class="scenario-title">TEST_RP-347</span>
                        <span class="scenario-status failed">FAILED</span>
                    </div>
                    <div class="scenario-meta">
                        <span class="execution-time">⏱️ 9.8s</span>
                        <span class="steps-summary">📊 2/3 passed</span>
                        <span class="toggle-arrow">▼</span>
                    </div>
                </div>
                <div class="scenario-content" style="display: none;">
                    <div class="steps-container">
                        
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 1</span>
                        <span class="step-text">Given user is on the Rumah Pendidikan homepage</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Navigated to rumah pendidikan home</div>
                        
                    </div>
                </div>
                
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 2</span>
                        <span class="step-text">When I click Icon "Ruang Bahasa"</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Clicked icon </div>
                        
                    </div>
                </div>
                
                <div class="step skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-number">Step 3</span>
                        <span class="step-text">Then user direct to Ruang Bahasa Pages "https://rumah-baru.staging.belajar.id/ruang/bahasa"</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: SKIPPED</div>
                        <div class="step-result">Result: Unknown or unsupported action for step: user direct to Ruang Bahasa Pages "https://rumah-baru.staging.belajar.id/ruang/bahasa"</div>
                        <div class="step-error">Error: Unknown or unsupported action</div>
                    </div>
                </div>
                
                    </div>
                </div>
            </div>
            
            <div class="scenario-section">
                <div class="scenario-header" onclick="toggleScenario(this)">
                    <div class="scenario-info">
                        <span class="scenario-icon">⏭️</span>
                        <span class="scenario-title">TEST_RP-348</span>
                        <span class="scenario-status failed">FAILED</span>
                    </div>
                    <div class="scenario-meta">
                        <span class="execution-time">⏱️ 9.5s</span>
                        <span class="steps-summary">📊 2/3 passed</span>
                        <span class="toggle-arrow">▼</span>
                    </div>
                </div>
                <div class="scenario-content" style="display: none;">
                    <div class="steps-container">
                        
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 1</span>
                        <span class="step-text">Given user is on the Rumah Pendidikan homepage</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Navigated to rumah pendidikan home</div>
                        
                    </div>
                </div>
                
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 2</span>
                        <span class="step-text">When I click Icon "Ruang Pemerintah"</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Clicked icon </div>
                        
                    </div>
                </div>
                
                <div class="step skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-number">Step 3</span>
                        <span class="step-text">Then user direct to Ruang Pemerintah Pages "https://rumah-baru.staging.belajar.id/ruang/pemerintah"</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: SKIPPED</div>
                        <div class="step-result">Result: Unknown or unsupported action for step: user direct to Ruang Pemerintah Pages "https://rumah-baru.staging.belajar.id/ruang/pemerintah"</div>
                        <div class="step-error">Error: Unknown or unsupported action</div>
                    </div>
                </div>
                
                    </div>
                </div>
            </div>
            
            <div class="scenario-section">
                <div class="scenario-header" onclick="toggleScenario(this)">
                    <div class="scenario-info">
                        <span class="scenario-icon">⏭️</span>
                        <span class="scenario-title">TEST_RP-349</span>
                        <span class="scenario-status failed">FAILED</span>
                    </div>
                    <div class="scenario-meta">
                        <span class="execution-time">⏱️ 9.7s</span>
                        <span class="steps-summary">📊 2/3 passed</span>
                        <span class="toggle-arrow">▼</span>
                    </div>
                </div>
                <div class="scenario-content" style="display: none;">
                    <div class="steps-container">
                        
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 1</span>
                        <span class="step-text">Given user is on the Rumah Pendidikan homepage</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Navigated to rumah pendidikan home</div>
                        
                    </div>
                </div>
                
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 2</span>
                        <span class="step-text">When I click Icon "Ruang Mitra"</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Clicked icon </div>
                        
                    </div>
                </div>
                
                <div class="step skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-number">Step 3</span>
                        <span class="step-text">Then user direct to Ruang Mitra "https://rumah-baru.staging.belajar.id/ruang/mitra"</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: SKIPPED</div>
                        <div class="step-result">Result: Unknown or unsupported action for step: user direct to Ruang Mitra "https://rumah-baru.staging.belajar.id/ruang/mitra"</div>
                        <div class="step-error">Error: Unknown or unsupported action</div>
                    </div>
                </div>
                
                    </div>
                </div>
            </div>
            
            <div class="scenario-section">
                <div class="scenario-header" onclick="toggleScenario(this)">
                    <div class="scenario-info">
                        <span class="scenario-icon">⏭️</span>
                        <span class="scenario-title">TEST_RP-350</span>
                        <span class="scenario-status failed">FAILED</span>
                    </div>
                    <div class="scenario-meta">
                        <span class="execution-time">⏱️ 10.9s</span>
                        <span class="steps-summary">📊 2/3 passed</span>
                        <span class="toggle-arrow">▼</span>
                    </div>
                </div>
                <div class="scenario-content" style="display: none;">
                    <div class="steps-container">
                        
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 1</span>
                        <span class="step-text">Given user is on the Rumah Pendidikan homepage</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Navigated to rumah pendidikan home</div>
                        
                    </div>
                </div>
                
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 2</span>
                        <span class="step-text">When I click Icon "Ruang Publik"</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Clicked icon </div>
                        
                    </div>
                </div>
                
                <div class="step skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-number">Step 3</span>
                        <span class="step-text">Then user direct to Ruang Publik Pages "https://rumah-baru.staging.belajar.id/ruang/publik"</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: SKIPPED</div>
                        <div class="step-result">Result: Unknown or unsupported action for step: user direct to Ruang Publik Pages "https://rumah-baru.staging.belajar.id/ruang/publik"</div>
                        <div class="step-error">Error: Unknown or unsupported action</div>
                    </div>
                </div>
                
                    </div>
                </div>
            </div>
            
            <div class="scenario-section">
                <div class="scenario-header" onclick="toggleScenario(this)">
                    <div class="scenario-info">
                        <span class="scenario-icon">⏭️</span>
                        <span class="scenario-title">TEST_RP-351</span>
                        <span class="scenario-status failed">FAILED</span>
                    </div>
                    <div class="scenario-meta">
                        <span class="execution-time">⏱️ 3.6s</span>
                        <span class="steps-summary">📊 3/4 passed</span>
                        <span class="toggle-arrow">▼</span>
                    </div>
                </div>
                <div class="scenario-content" style="display: none;">
                    <div class="steps-container">
                        
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 1</span>
                        <span class="step-text">Given the user is on the Rumah Pendidikan homepage</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Navigated to rumah pendidikan home</div>
                        
                    </div>
                </div>
                
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 2</span>
                        <span class="step-text">When I scroll to the section</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Scrolled page</div>
                        
                    </div>
                </div>
                
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 3</span>
                        <span class="step-text">And user click "Ruang Orang Tua"</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Clicked ruang orang tua</div>
                        
                    </div>
                </div>
                
                <div class="step skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-number">Step 4</span>
                        <span class="step-text">Then user direct to Ruang Orang Tua Pages "https://rumah-baru.staging.belajar.id/ruang/orang-tua"</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: SKIPPED</div>
                        <div class="step-result">Result: Unknown or unsupported action for step: user direct to Ruang Orang Tua Pages "https://rumah-baru.staging.belajar.id/ruang/orang-tua"</div>
                        <div class="step-error">Error: Unknown or unsupported action</div>
                    </div>
                </div>
                
                    </div>
                </div>
            </div>
            
            <div class="scenario-section">
                <div class="scenario-header" onclick="toggleScenario(this)">
                    <div class="scenario-info">
                        <span class="scenario-icon">⏭️</span>
                        <span class="scenario-title">TEST_RP-355</span>
                        <span class="scenario-status failed">FAILED</span>
                    </div>
                    <div class="scenario-meta">
                        <span class="execution-time">⏱️ 19.3s</span>
                        <span class="steps-summary">📊 3/4 passed</span>
                        <span class="toggle-arrow">▼</span>
                    </div>
                </div>
                <div class="scenario-content" style="display: none;">
                    <div class="steps-container">
                        
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 1</span>
                        <span class="step-text">Given the user is on the Rumah Pendidikan homepage</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Navigated to rumah pendidikan home</div>
                        
                    </div>
                </div>
                
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 2</span>
                        <span class="step-text">When I click Icon "ruang mitra"</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Clicked icon </div>
                        
                    </div>
                </div>
                
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 3</span>
                        <span class="step-text">When I click Card</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Clicked card</div>
                        
                    </div>
                </div>
                
                <div class="step skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-number">Step 4</span>
                        <span class="step-text">Then user direct to Relawan Pendidikan Pages "https://rumah.pendidikan.go.id/relawan-pendidikan.html"</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: SKIPPED</div>
                        <div class="step-result">Result: Unknown or unsupported action for step: user direct to Relawan Pendidikan Pages "https://rumah.pendidikan.go.id/relawan-pendidikan.html"</div>
                        <div class="step-error">Error: Unknown or unsupported action</div>
                    </div>
                </div>
                
                    </div>
                </div>
            </div>
            
            <div class="scenario-section">
                <div class="scenario-header" onclick="toggleScenario(this)">
                    <div class="scenario-info">
                        <span class="scenario-icon">❌</span>
                        <span class="scenario-title">TEST_RP-356</span>
                        <span class="scenario-status failed">FAILED</span>
                    </div>
                    <div class="scenario-meta">
                        <span class="execution-time">⏱️ 25.4s</span>
                        <span class="steps-summary">📊 2/4 passed</span>
                        <span class="toggle-arrow">▼</span>
                    </div>
                </div>
                <div class="scenario-content" style="display: none;">
                    <div class="steps-container">
                        
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 1</span>
                        <span class="step-text">Given the user is on the Rumah Pendidikan homepage</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Navigated to rumah pendidikan home</div>
                        
                    </div>
                </div>
                
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 2</span>
                        <span class="step-text">When I enter text</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Typed 'text' in input_field</div>
                        
                    </div>
                </div>
                
                <div class="step skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-number">Step 3</span>
                        <span class="step-text">And the keyword matches a title or description of one or more services</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: SKIPPED</div>
                        <div class="step-result">Result: Unknown or unsupported action for step: the keyword matches a title or description of one or more services</div>
                        <div class="step-error">Error: Unknown or unsupported action</div>
                    </div>
                </div>
                
                <div class="step failed">
                    <div class="step-header">
                        <span class="step-icon">❌</span>
                        <span class="step-number">Step 4</span>
                        <span class="step-text">Then a list of matching services should be displayed to the user</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: FAILED</div>
                        <div class="step-result">Result: Assertion failed for be displayed to the user</div>
                        <div class="step-error">Error: Expected: be displayed to the user, Actual: Langsung ke konten utama
Rumah Pendidikan
Cari
Pusat Informasi
Masuk
Jelajahi Ruang di Rumah Pendidikan

Ruang GTK

Ruang Murid

Ruang Sekolah

Ruang Bahasa

Ruang Pemerintah

Ruang Mitra

Ruang Publi</div>
                    </div>
                </div>
                
                    </div>
                </div>
            </div>
            
            <div class="scenario-section">
                <div class="scenario-header" onclick="toggleScenario(this)">
                    <div class="scenario-info">
                        <span class="scenario-icon">❌</span>
                        <span class="scenario-title">TEST_RP-357</span>
                        <span class="scenario-status failed">FAILED</span>
                    </div>
                    <div class="scenario-meta">
                        <span class="execution-time">⏱️ 40.6s</span>
                        <span class="steps-summary">📊 2/4 passed</span>
                        <span class="toggle-arrow">▼</span>
                    </div>
                </div>
                <div class="scenario-content" style="display: none;">
                    <div class="steps-container">
                        
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 1</span>
                        <span class="step-text">Given the user is on the Rumah Pendidikan homepage</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Navigated to rumah pendidikan home</div>
                        
                    </div>
                </div>
                
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 2</span>
                        <span class="step-text">When I enter text</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Typed 'text' in input_field</div>
                        
                    </div>
                </div>
                
                <div class="step failed">
                    <div class="step-header">
                        <span class="step-icon">❌</span>
                        <span class="step-number">Step 3</span>
                        <span class="step-text">And the keyword does not match any service title or description</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: FAILED</div>
                        <div class="step-result">Result: Assertion failed for no_results</div>
                        <div class="step-error">Error: Expected: no_results, Actual: Langsung ke konten utama
Rumah Pendidikan
Cari
Pusat Informasi
Masuk
Jelajahi Ruang di Rumah Pendidikan

Ruang GTK

Ruang Murid

Ruang Sekolah

Ruang Bahasa

Ruang Pemerintah

Ruang Mitra

Ruang Publi</div>
                    </div>
                </div>
                
                <div class="step failed">
                    <div class="step-header">
                        <span class="step-icon">❌</span>
                        <span class="step-number">Step 4</span>
                        <span class="step-text">Then the system should display a message stating "Tidak Ada Hasil yang Sesuai"</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: FAILED</div>
                        <div class="step-result">Result: Assertion failed for display a message stating </div>
                        <div class="step-error">Error: Expected: display a message stating , Actual: Langsung ke konten utama
Rumah Pendidikan
Cari
Pusat Informasi
Masuk
Jelajahi Ruang di Rumah Pendidikan

Ruang GTK

Ruang Murid

Ruang Sekolah

Ruang Bahasa

Ruang Pemerintah

Ruang Mitra

Ruang Publi</div>
                    </div>
                </div>
                
                    </div>
                </div>
            </div>
            
            <div class="scenario-section">
                <div class="scenario-header" onclick="toggleScenario(this)">
                    <div class="scenario-info">
                        <span class="scenario-icon">❌</span>
                        <span class="scenario-title">TEST_RP-370</span>
                        <span class="scenario-status failed">FAILED</span>
                    </div>
                    <div class="scenario-meta">
                        <span class="execution-time">⏱️ 167.9s</span>
                        <span class="steps-summary">📊 2/15 passed</span>
                        <span class="toggle-arrow">▼</span>
                    </div>
                </div>
                <div class="scenario-content" style="display: none;">
                    <div class="steps-container">
                        
                <div class="step failed">
                    <div class="step-header">
                        <span class="step-icon">❌</span>
                        <span class="step-number">Step 1</span>
                        <span class="step-text">Given the user accesses the Rumah Pendidikan homepage</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: FAILED</div>
                        <div class="step-result">Result: Assertion failed for None</div>
                        <div class="step-error">Error: Expected: None, Actual: Assertion error: 'NoneType' object has no attribute 'lower'</div>
                    </div>
                </div>
                
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 2</span>
                        <span class="step-text">Then the user should see the following components in order from top to bottom:</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Context step acknowledged</div>
                        
                    </div>
                </div>
                
                <div class="step failed">
                    <div class="step-header">
                        <span class="step-icon">❌</span>
                        <span class="step-number">Step 3</span>
                        <span class="step-text">And | Header with logo, search box, and login button |</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: FAILED</div>
                        <div class="step-result">Result: Assertion failed for Header with logo, search box, and login button</div>
                        <div class="step-error">Error: Expected: Header with logo, search box, and login button, Actual: Langsung ke konten utama
Rumah Pendidikan
Cari
Pusat Informasi
Masuk
Jelajahi Ruang di Rumah Pendidikan

Ruang GTK

Ruang Murid

Ruang Sekolah

Ruang Bahasa

Ruang Pemerintah

Ruang Mitra

Ruang Publik

Ruang Orang Tua

Semangat Rumah Pendidikan

Rumah Pendidikan berkomitmen untuk menjadi pusat ruang kolaborasi bagi siswa, guru, orang tua, hingga pemerintah daerah

Pelajari Selengkapnya
Layanan Unggulan di Rumah Pendidikan

Sumber Belajar

Portal pembelajaran digital interaktif untuk semua jenjang.

Buku Bacaan Digital

Pusat Buku Digital untuk Gerakan Literasi Nasional.

Pengelolaan Kinerja

Perencanaan, pelaksanaan, dan penilaian Anda untuk pengembangan diri dan satdik.

Diklat

Pelatihan terbimbing menggunakan LMS.

Pelatihan Mandiri

Materi untuk pengembangan kompetensi Guru dan Tendik.

Siapa saja yang sudah memanfaatkan kemudahan kolaborasi di Rumah Pendidikan?

Simak perjalanan mereka

"Rumah Pendidikan merupakan gagasan yang sangat menarik dan terdepan untuk perkembangan zaman terkini. Di mana pergeseran paradigma baru, pendidikan tidak hanya sekedar di dalam kelas tapi juga bisa di ruang keluarga dan masyarakat."

Arham, S.Pd., M.Pd

Kepala Sekolah SD INPRES 33 Birobuli, Palu

"Kami menyambut baik dengan adanya rumah pendidikan ini sebagai langkah strategis Kemendikdasmen terutama memperluas akses pendidikan dan pemerataan mutu pendidikan. Karena diharapkan dengan adanya Rumdik ini sebagai salah satu langkah pendidikan yang Responsif, Akuntabel, Melayani, Adaptif, dan Harmonis. Semoga menjadi langkah terbaik untuk Indonesia maju."

Dr. Firman Oktora, S.SI, M.PD.

Kabalaitekkomdik Jawa Barat

"Menurut saya Rumah Pendidikan sangat keren dan sangat membantu. Karena bagi saya murid kelas 6, Rumah Pendidikan bisa membantu dalam belajar dengan simpel, praktis, dan bisa mempelajari berbagai macam hal dalam satu platform, jadi sangat membantu."

Alena

Pelajar Gen KiHajar

Informasi Untuk Anda

Berita
Sambut Tahun Ajaran Baru 2025 dengan Penuh Inspirasi!
14 Juli 2025 | 10.32 WIB
Baca Selengkapnya
Berita
Laman Direktori Portal SPMB Berbasis SIAP SPMB Online
11 Juli 2025 | 15.13 WIB
Baca Selengkapnya
Berita
Tanya Jawab seputar Tes Kemampuan Akademik (ТKА)
24 Juni 2025 | 15.00 WIB
Baca Selengkapnya
Lihat lebih banyak
Pertanyaan yang paling sering ditanyakan
Apa manfaat menggunakan Rumah Pendidikan?
Bagaimana cara mengakses Rumah Pendidikan?
Apakah pengguna harus menggunakan Akun belajar.id untuk mengakses Rumah Pendidikan?
Siapa saja pengguna yang disarankan untuk menggunakan platform Rumah Pendidikan
Apa perbedaan rumah pendidikan dengan platform digital pendidikan milik Kemendikdasmen lainnya?
Ke Pusat Informasi

Kementerian Pendidikan Dasar dan Menengah

Kompleks Kementerian Pendidikan dan Kebudayaan, Senayan, Jakarta Pusat 10270

Navigasi

Pusat Bantuan Rumah Pendidikan

Syarat & Ketentuan

Kebijakan Privasi

Unduh Aplikasi Rumah Pendidikan</div>
                    </div>
                </div>
                
                <div class="step failed">
                    <div class="step-header">
                        <span class="step-icon">❌</span>
                        <span class="step-number">Step 4</span>
                        <span class="step-text">And | Hero banner with copytext (static image, unclickable) |</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: FAILED</div>
                        <div class="step-result">Result: Assertion failed for Hero banner with copytext (static image, unclickable)</div>
                        <div class="step-error">Error: Expected: Hero banner with copytext (static image, unclickable), Actual: Langsung ke konten utama
Rumah Pendidikan
Cari
Pusat Informasi
Masuk
Jelajahi Ruang di Rumah Pendidikan

Ruang GTK

Ruang Murid

Ruang Sekolah

Ruang Bahasa

Ruang Pemerintah

Ruang Mitra

Ruang Publi</div>
                    </div>
                </div>
                
                <div class="step failed">
                    <div class="step-header">
                        <span class="step-icon">❌</span>
                        <span class="step-number">Step 5</span>
                        <span class="step-text">And | 8 room icons with segment titles |</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: FAILED</div>
                        <div class="step-result">Result: Assertion failed for 8 room icons with segment titles</div>
                        <div class="step-error">Error: Expected: 8 room icons with segment titles, Actual: Langsung ke konten utama
Rumah Pendidikan
Cari
Pusat Informasi
Masuk
Jelajahi Ruang di Rumah Pendidikan

Ruang GTK

Ruang Murid

Ruang Sekolah

Ruang Bahasa

Ruang Pemerintah

Ruang Mitra

Ruang Publi</div>
                    </div>
                </div>
                
                <div class="step failed">
                    <div class="step-header">
                        <span class="step-icon">❌</span>
                        <span class="step-number">Step 6</span>
                        <span class="step-text">And | 4 live photos with text "Semangat Rumah Pendidikan Nasional 2025" |</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: FAILED</div>
                        <div class="step-result">Result: Assertion failed for 4 live photos with text "Semangat Rumah Pendidikan Nasional 2025"</div>
                        <div class="step-error">Error: Expected: 4 live photos with text "Semangat Rumah Pendidikan Nasional 2025", Actual: Rumah Pendidikan</div>
                    </div>
                </div>
                
                <div class="step failed">
                    <div class="step-header">
                        <span class="step-icon">❌</span>
                        <span class="step-number">Step 7</span>
                        <span class="step-text">And | Carousel of 5 featured services with segment titles |</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: FAILED</div>
                        <div class="step-result">Result: Assertion failed for Carousel of 5 featured services with segment titles</div>
                        <div class="step-error">Error: Expected: Carousel of 5 featured services with segment titles, Actual: Langsung ke konten utama
Rumah Pendidikan
Cari
Pusat Informasi
Masuk
Jelajahi Ruang di Rumah Pendidikan

Ruang GTK

Ruang Murid

Ruang Sekolah

Ruang Bahasa

Ruang Pemerintah

Ruang Mitra

Ruang Publi</div>
                    </div>
                </div>
                
                <div class="step failed">
                    <div class="step-header">
                        <span class="step-icon">❌</span>
                        <span class="step-number">Step 8</span>
                        <span class="step-text">And | 3 education actors' testimonials about Rumah Pendidikan |</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: FAILED</div>
                        <div class="step-result">Result: Assertion failed for 3 education actors' testimonials about Rumah Pendidikan</div>
                        <div class="step-error">Error: Expected: 3 education actors' testimonials about Rumah Pendidikan, Actual: Rumah Pendidikan</div>
                    </div>
                </div>
                
                <div class="step failed">
                    <div class="step-header">
                        <span class="step-icon">❌</span>
                        <span class="step-number">Step 9</span>
                        <span class="step-text">And | FAQ section with button linking to the complaint form |</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: FAILED</div>
                        <div class="step-result">Result: Assertion failed for FAQ section with button linking to the complaint form</div>
                        <div class="step-error">Error: Expected: FAQ section with button linking to the complaint form, Actual: Langsung ke konten utama
Rumah Pendidikan
Cari
Pusat Informasi
Masuk
Jelajahi Ruang di Rumah Pendidikan

Ruang GTK

Ruang Murid

Ruang Sekolah

Ruang Bahasa

Ruang Pemerintah

Ruang Mitra

Ruang Publik

Ruang Orang Tua

Semangat Rumah Pendidikan

Rumah Pendidikan berkomitmen untuk menjadi pusat ruang kolaborasi bagi siswa, guru, orang tua, hingga pemerintah daerah

Pelajari Selengkapnya
Layanan Unggulan di Rumah Pendidikan

Sumber Belajar

Portal pembelajaran digital interaktif untuk semua jenjang.

Buku Bacaan Digital

Pusat Buku Digital untuk Gerakan Literasi Nasional.

Pengelolaan Kinerja

Perencanaan, pelaksanaan, dan penilaian Anda untuk pengembangan diri dan satdik.

Diklat

Pelatihan terbimbing menggunakan LMS.

Pelatihan Mandiri

Materi untuk pengembangan kompetensi Guru dan Tendik.

Siapa saja yang sudah memanfaatkan kemudahan kolaborasi di Rumah Pendidikan?

Simak perjalanan mereka

"Rumah Pendidikan merupakan gagasan yang sangat menarik dan terdepan untuk perkembangan zaman terkini. Di mana pergeseran paradigma baru, pendidikan tidak hanya sekedar di dalam kelas tapi juga bisa di ruang keluarga dan masyarakat."

Arham, S.Pd., M.Pd

Kepala Sekolah SD INPRES 33 Birobuli, Palu

"Kami menyambut baik dengan adanya rumah pendidikan ini sebagai langkah strategis Kemendikdasmen terutama memperluas akses pendidikan dan pemerataan mutu pendidikan. Karena diharapkan dengan adanya Rumdik ini sebagai salah satu langkah pendidikan yang Responsif, Akuntabel, Melayani, Adaptif, dan Harmonis. Semoga menjadi langkah terbaik untuk Indonesia maju."

Dr. Firman Oktora, S.SI, M.PD.

Kabalaitekkomdik Jawa Barat

"Menurut saya Rumah Pendidikan sangat keren dan sangat membantu. Karena bagi saya murid kelas 6, Rumah Pendidikan bisa membantu dalam belajar dengan simpel, praktis, dan bisa mempelajari berbagai macam hal dalam satu platform, jadi sangat membantu."

Alena

Pelajar Gen KiHajar

Informasi Untuk Anda

Berita
Sambut Tahun Ajaran Baru 2025 dengan Penuh Inspirasi!
14 Juli 2025 | 10.32 WIB
Baca Selengkapnya
Berita
Laman Direktori Portal SPMB Berbasis SIAP SPMB Online
11 Juli 2025 | 15.13 WIB
Baca Selengkapnya
Berita
Tanya Jawab seputar Tes Kemampuan Akademik (ТKА)
24 Juni 2025 | 15.00 WIB
Baca Selengkapnya
Lihat lebih banyak
Pertanyaan yang paling sering ditanyakan
Apa manfaat menggunakan Rumah Pendidikan?
Bagaimana cara mengakses Rumah Pendidikan?
Apakah pengguna harus menggunakan Akun belajar.id untuk mengakses Rumah Pendidikan?
Siapa saja pengguna yang disarankan untuk menggunakan platform Rumah Pendidikan
Apa perbedaan rumah pendidikan dengan platform digital pendidikan milik Kemendikdasmen lainnya?
Ke Pusat Informasi

Kementerian Pendidikan Dasar dan Menengah

Kompleks Kementerian Pendidikan dan Kebudayaan, Senayan, Jakarta Pusat 10270

Navigasi

Pusat Bantuan Rumah Pendidikan

Syarat & Ketentuan

Kebijakan Privasi

Unduh Aplikasi Rumah Pendidikan</div>
                    </div>
                </div>
                
                <div class="step failed">
                    <div class="step-header">
                        <span class="step-icon">❌</span>
                        <span class="step-number">Step 10</span>
                        <span class="step-text">And | Footer with logo, navigation, and links to Google Play Store & App Store |</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: FAILED</div>
                        <div class="step-result">Result: Assertion failed for Footer with logo, navigation, and links to Google Play Store & App Store</div>
                        <div class="step-error">Error: Expected: Footer with logo, navigation, and links to Google Play Store & App Store, Actual: Langsung ke konten utama
Rumah Pendidikan
Cari
Pusat Informasi
Masuk
Jelajahi Ruang di Rumah Pendidikan

Ruang GTK

Ruang Murid

Ruang Sekolah

Ruang Bahasa

Ruang Pemerintah

Ruang Mitra

Ruang Publik

Ruang Orang Tua

Semangat Rumah Pendidikan

Rumah Pendidikan berkomitmen untuk menjadi pusat ruang kolaborasi bagi siswa, guru, orang tua, hingga pemerintah daerah

Pelajari Selengkapnya
Layanan Unggulan di Rumah Pendidikan

Sumber Belajar

Portal pembelajaran digital interaktif untuk semua jenjang.

Buku Bacaan Digital

Pusat Buku Digital untuk Gerakan Literasi Nasional.

Pengelolaan Kinerja

Perencanaan, pelaksanaan, dan penilaian Anda untuk pengembangan diri dan satdik.

Diklat

Pelatihan terbimbing menggunakan LMS.

Pelatihan Mandiri

Materi untuk pengembangan kompetensi Guru dan Tendik.

Siapa saja yang sudah memanfaatkan kemudahan kolaborasi di Rumah Pendidikan?

Simak perjalanan mereka

"Rumah Pendidikan merupakan gagasan yang sangat menarik dan terdepan untuk perkembangan zaman terkini. Di mana pergeseran paradigma baru, pendidikan tidak hanya sekedar di dalam kelas tapi juga bisa di ruang keluarga dan masyarakat."

Arham, S.Pd., M.Pd

Kepala Sekolah SD INPRES 33 Birobuli, Palu

"Kami menyambut baik dengan adanya rumah pendidikan ini sebagai langkah strategis Kemendikdasmen terutama memperluas akses pendidikan dan pemerataan mutu pendidikan. Karena diharapkan dengan adanya Rumdik ini sebagai salah satu langkah pendidikan yang Responsif, Akuntabel, Melayani, Adaptif, dan Harmonis. Semoga menjadi langkah terbaik untuk Indonesia maju."

Dr. Firman Oktora, S.SI, M.PD.

Kabalaitekkomdik Jawa Barat

"Menurut saya Rumah Pendidikan sangat keren dan sangat membantu. Karena bagi saya murid kelas 6, Rumah Pendidikan bisa membantu dalam belajar dengan simpel, praktis, dan bisa mempelajari berbagai macam hal dalam satu platform, jadi sangat membantu."

Alena

Pelajar Gen KiHajar

Informasi Untuk Anda

Berita
Sambut Tahun Ajaran Baru 2025 dengan Penuh Inspirasi!
14 Juli 2025 | 10.32 WIB
Baca Selengkapnya
Berita
Laman Direktori Portal SPMB Berbasis SIAP SPMB Online
11 Juli 2025 | 15.13 WIB
Baca Selengkapnya
Berita
Tanya Jawab seputar Tes Kemampuan Akademik (ТKА)
24 Juni 2025 | 15.00 WIB
Baca Selengkapnya
Lihat lebih banyak
Pertanyaan yang paling sering ditanyakan
Apa manfaat menggunakan Rumah Pendidikan?
Bagaimana cara mengakses Rumah Pendidikan?
Apakah pengguna harus menggunakan Akun belajar.id untuk mengakses Rumah Pendidikan?
Siapa saja pengguna yang disarankan untuk menggunakan platform Rumah Pendidikan
Apa perbedaan rumah pendidikan dengan platform digital pendidikan milik Kemendikdasmen lainnya?
Ke Pusat Informasi

Kementerian Pendidikan Dasar dan Menengah

Kompleks Kementerian Pendidikan dan Kebudayaan, Senayan, Jakarta Pusat 10270

Navigasi

Pusat Bantuan Rumah Pendidikan

Syarat & Ketentuan

Kebijakan Privasi

Unduh Aplikasi Rumah Pendidikan</div>
                    </div>
                </div>
                
                <div class="step failed">
                    <div class="step-header">
                        <span class="step-icon">❌</span>
                        <span class="step-number">Step 11</span>
                        <span class="step-text">Then I should see the content</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: FAILED</div>
                        <div class="step-result">Result: Assertion failed for see the content</div>
                        <div class="step-error">Error: Expected: see the content, Actual: Langsung ke konten utama
Rumah Pendidikan
Cari
Pusat Informasi
Masuk
Jelajahi Ruang di Rumah Pendidikan

Ruang GTK

Ruang Murid

Ruang Sekolah

Ruang Bahasa

Ruang Pemerintah

Ruang Mitra

Ruang Publi</div>
                    </div>
                </div>
                
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 12</span>
                        <span class="step-text">When I click the element</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Clicked the element</div>
                        
                    </div>
                </div>
                
                <div class="step failed">
                    <div class="step-header">
                        <span class="step-icon">❌</span>
                        <span class="step-number">Step 13</span>
                        <span class="step-text">Then the user should be redirected to the respective service platform in the same browser tab</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: FAILED</div>
                        <div class="step-result">Result: Assertion failed for the respective service platform in the same browser tab</div>
                        <div class="step-error">Error: Expected: the respective service platform in the same browser tab, Actual: Langsung ke konten utama
Rumah Pendidikan
Cari
Pusat Informasi
Masuk
Jelajahi Ruang di Rumah Pendidikan

Ruang GTK

Ruang Murid

Ruang Sekolah

Ruang Bahasa

Ruang Pemerintah

Ruang Mitra

Ruang Publik

Ruang Orang Tua

Semangat Rumah Pendidikan

Rumah Pendidikan berkomitmen untuk menjadi pusat ruang kolaborasi bagi siswa, guru, orang tua, hingga pemerintah daerah

Pelajari Selengkapnya
Layanan Unggulan di Rumah Pendidikan

Sumber Belajar

Portal pembelajaran digital interaktif untuk semua jenjang.

Buku Bacaan Digital

Pusat Buku Digital untuk Gerakan Literasi Nasional.

Pengelolaan Kinerja

Perencanaan, pelaksanaan, dan penilaian Anda untuk pengembangan diri dan satdik.

Diklat

Pelatihan terbimbing menggunakan LMS.

Pelatihan Mandiri

Materi untuk pengembangan kompetensi Guru dan Tendik.

Siapa saja yang sudah memanfaatkan kemudahan kolaborasi di Rumah Pendidikan?

Simak perjalanan mereka

"Rumah Pendidikan merupakan gagasan yang sangat menarik dan terdepan untuk perkembangan zaman terkini. Di mana pergeseran paradigma baru, pendidikan tidak hanya sekedar di dalam kelas tapi juga bisa di ruang keluarga dan masyarakat."

Arham, S.Pd., M.Pd

Kepala Sekolah SD INPRES 33 Birobuli, Palu

"Kami menyambut baik dengan adanya rumah pendidikan ini sebagai langkah strategis Kemendikdasmen terutama memperluas akses pendidikan dan pemerataan mutu pendidikan. Karena diharapkan dengan adanya Rumdik ini sebagai salah satu langkah pendidikan yang Responsif, Akuntabel, Melayani, Adaptif, dan Harmonis. Semoga menjadi langkah terbaik untuk Indonesia maju."

Dr. Firman Oktora, S.SI, M.PD.

Kabalaitekkomdik Jawa Barat

"Menurut saya Rumah Pendidikan sangat keren dan sangat membantu. Karena bagi saya murid kelas 6, Rumah Pendidikan bisa membantu dalam belajar dengan simpel, praktis, dan bisa mempelajari berbagai macam hal dalam satu platform, jadi sangat membantu."

Alena

Pelajar Gen KiHajar

Informasi Untuk Anda

Berita
Sambut Tahun Ajaran Baru 2025 dengan Penuh Inspirasi!
14 Juli 2025 | 10.32 WIB
Baca Selengkapnya
Berita
Laman Direktori Portal SPMB Berbasis SIAP SPMB Online
11 Juli 2025 | 15.13 WIB
Baca Selengkapnya
Berita
Tanya Jawab seputar Tes Kemampuan Akademik (ТKА)
24 Juni 2025 | 15.00 WIB
Baca Selengkapnya
Lihat lebih banyak
Pertanyaan yang paling sering ditanyakan
Apa manfaat menggunakan Rumah Pendidikan?
Bagaimana cara mengakses Rumah Pendidikan?
Apakah pengguna harus menggunakan Akun belajar.id untuk mengakses Rumah Pendidikan?
Siapa saja pengguna yang disarankan untuk menggunakan platform Rumah Pendidikan
Apa perbedaan rumah pendidikan dengan platform digital pendidikan milik Kemendikdasmen lainnya?
Ke Pusat Informasi

Kementerian Pendidikan Dasar dan Menengah

Kompleks Kementerian Pendidikan dan Kebudayaan, Senayan, Jakarta Pusat 10270

Navigasi

Pusat Bantuan Rumah Pendidikan

Syarat & Ketentuan

Kebijakan Privasi

Unduh Aplikasi Rumah Pendidikan</div>
                    </div>
                </div>
                
                <div class="step skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-number">Step 14</span>
                        <span class="step-text">When the user performs a search with keywords such as "guru" or "murid"</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: SKIPPED</div>
                        <div class="step-result">Result: Unknown or unsupported action for step: the user performs a search with keywords such as "guru" or "murid"</div>
                        <div class="step-error">Error: Unknown or unsupported action</div>
                    </div>
                </div>
                
                <div class="step failed">
                    <div class="step-header">
                        <span class="step-icon">❌</span>
                        <span class="step-number">Step 15</span>
                        <span class="step-text">Then the system should display matching service cards containing the keyword in the title, description, or tags</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: FAILED</div>
                        <div class="step-result">Result: Assertion failed for display matching service cards containing the keyword in the title, description, or tags</div>
                        <div class="step-error">Error: Expected: display matching service cards containing the keyword in the title, description, or tags, Actual: Langsung ke konten utama
Rumah Pendidikan
Cari
Pusat Informasi
Masuk
Jelajahi Ruang di Rumah Pendidikan

Ruang GTK

Ruang Murid

Ruang Sekolah

Ruang Bahasa

Ruang Pemerintah

Ruang Mitra

Ruang Publi</div>
                    </div>
                </div>
                
                    </div>
                </div>
            </div>
            
            <div class="scenario-section">
                <div class="scenario-header" onclick="toggleScenario(this)">
                    <div class="scenario-info">
                        <span class="scenario-icon">❌</span>
                        <span class="scenario-title">TEST_RP-372</span>
                        <span class="scenario-status failed">FAILED</span>
                    </div>
                    <div class="scenario-meta">
                        <span class="execution-time">⏱️ 109.4s</span>
                        <span class="steps-summary">📊 8/11 passed</span>
                        <span class="toggle-arrow">▼</span>
                    </div>
                </div>
                <div class="scenario-content" style="display: none;">
                    <div class="steps-container">
                        
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 1</span>
                        <span class="step-text">Given the user is on the Rumah Pendidikan homepage</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Navigated to rumah pendidikan home</div>
                        
                    </div>
                </div>
                
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 2</span>
                        <span class="step-text">Then the user should see the following text and service options under the Parent Space section:</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Context step acknowledged</div>
                        
                    </div>
                </div>
                
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 3</span>
                        <span class="step-text">And | Sarana partisipasi orang tua melalui pantauan capaian Murid |</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Asserted Sarana partisipasi orang tua melalui pantauan capaian Murid</div>
                        
                    </div>
                </div>
                
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 4</span>
                        <span class="step-text">And | dan dukungan belajar di rumah |</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Asserted dan dukungan belajar di rumah</div>
                        
                    </div>
                </div>
                
                <div class="step failed">
                    <div class="step-header">
                        <span class="step-icon">❌</span>
                        <span class="step-number">Step 5</span>
                        <span class="step-text">And | Layanan yang Tersedia |</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: FAILED</div>
                        <div class="step-result">Result: Assertion failed for Layanan yang Tersedia</div>
                        <div class="step-error">Error: Expected: Layanan yang Tersedia, Actual: Layanan Unggulan di Rumah Pendidikan</div>
                    </div>
                </div>
                
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 6</span>
                        <span class="step-text">And | Konsultasi Pendidikan |</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Asserted Konsultasi Pendidikan</div>
                        
                    </div>
                </div>
                
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 7</span>
                        <span class="step-text">And | Layanan Informasi dan Pengaduan |</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Asserted Layanan Informasi dan Pengaduan</div>
                        
                    </div>
                </div>
                
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 8</span>
                        <span class="step-text">And | Panduan Pendampingan |</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Asserted Panduan Pendampingan</div>
                        
                    </div>
                </div>
                
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 9</span>
                        <span class="step-text">When I click the element</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Clicked the element</div>
                        
                    </div>
                </div>
                
                <div class="step failed">
                    <div class="step-header">
                        <span class="step-icon">❌</span>
                        <span class="step-number">Step 10</span>
                        <span class="step-text">Then the system should redirect the user to the selected service</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: FAILED</div>
                        <div class="step-result">Result: Assertion failed for the selected service</div>
                        <div class="step-error">Error: Expected: the selected service, Actual: Langsung ke konten utama
Rumah Pendidikan
Cari
Pusat Informasi
Masuk
Jelajahi Ruang di Rumah Pendidikan

Ruang GTK

Ruang Murid

Ruang Sekolah

Ruang Bahasa

Ruang Pemerintah

Ruang Mitra

Ruang Publi</div>
                    </div>
                </div>
                
                <div class="step failed">
                    <div class="step-header">
                        <span class="step-icon">❌</span>
                        <span class="step-number">Step 11</span>
                        <span class="step-text">And the redirection should happen in the same browser tab</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: FAILED</div>
                        <div class="step-result">Result: Assertion failed for happen in the same browser tab</div>
                        <div class="step-error">Error: Expected: happen in the same browser tab, Actual: Langsung ke konten utama
Rumah Pendidikan
Cari
Pusat Informasi
Masuk
Jelajahi Ruang di Rumah Pendidikan

Ruang GTK

Ruang Murid

Ruang Sekolah

Ruang Bahasa

Ruang Pemerintah

Ruang Mitra

Ruang Publik

Ruang Orang Tua

Semangat Rumah Pendidikan

Rumah Pendidikan berkomitmen untuk menjadi pusat ruang kolaborasi bagi siswa, guru, orang tua, hingga pemerintah daerah

Pelajari Selengkapnya
Layanan Unggulan di Rumah Pendidikan

Sumber Belajar

Portal pembelajaran digital interaktif untuk semua jenjang.

Buku Bacaan Digital

Pusat Buku Digital untuk Gerakan Literasi Nasional.

Pengelolaan Kinerja

Perencanaan, pelaksanaan, dan penilaian Anda untuk pengembangan diri dan satdik.

Diklat

Pelatihan terbimbing menggunakan LMS.

Pelatihan Mandiri

Materi untuk pengembangan kompetensi Guru dan Tendik.

Siapa saja yang sudah memanfaatkan kemudahan kolaborasi di Rumah Pendidikan?

Simak perjalanan mereka

"Rumah Pendidikan merupakan gagasan yang sangat menarik dan terdepan untuk perkembangan zaman terkini. Di mana pergeseran paradigma baru, pendidikan tidak hanya sekedar di dalam kelas tapi juga bisa di ruang keluarga dan masyarakat."

Arham, S.Pd., M.Pd

Kepala Sekolah SD INPRES 33 Birobuli, Palu

"Kami menyambut baik dengan adanya rumah pendidikan ini sebagai langkah strategis Kemendikdasmen terutama memperluas akses pendidikan dan pemerataan mutu pendidikan. Karena diharapkan dengan adanya Rumdik ini sebagai salah satu langkah pendidikan yang Responsif, Akuntabel, Melayani, Adaptif, dan Harmonis. Semoga menjadi langkah terbaik untuk Indonesia maju."

Dr. Firman Oktora, S.SI, M.PD.

Kabalaitekkomdik Jawa Barat

"Menurut saya Rumah Pendidikan sangat keren dan sangat membantu. Karena bagi saya murid kelas 6, Rumah Pendidikan bisa membantu dalam belajar dengan simpel, praktis, dan bisa mempelajari berbagai macam hal dalam satu platform, jadi sangat membantu."

Alena

Pelajar Gen KiHajar

Informasi Untuk Anda

Berita
Sambut Tahun Ajaran Baru 2025 dengan Penuh Inspirasi!
14 Juli 2025 | 10.32 WIB
Baca Selengkapnya
Berita
Laman Direktori Portal SPMB Berbasis SIAP SPMB Online
11 Juli 2025 | 15.13 WIB
Baca Selengkapnya
Berita
Tanya Jawab seputar Tes Kemampuan Akademik (ТKА)
24 Juni 2025 | 15.00 WIB
Baca Selengkapnya
Lihat lebih banyak
Pertanyaan yang paling sering ditanyakan
Apa manfaat menggunakan Rumah Pendidikan?
Bagaimana cara mengakses Rumah Pendidikan?
Apakah pengguna harus menggunakan Akun belajar.id untuk mengakses Rumah Pendidikan?
Siapa saja pengguna yang disarankan untuk menggunakan platform Rumah Pendidikan
Apa perbedaan rumah pendidikan dengan platform digital pendidikan milik Kemendikdasmen lainnya?
Ke Pusat Informasi

Kementerian Pendidikan Dasar dan Menengah

Kompleks Kementerian Pendidikan dan Kebudayaan, Senayan, Jakarta Pusat 10270

Navigasi

Pusat Bantuan Rumah Pendidikan

Syarat & Ketentuan

Kebijakan Privasi

Unduh Aplikasi Rumah Pendidikan</div>
                    </div>
                </div>
                
                    </div>
                </div>
            </div>
            
            <div class="scenario-section">
                <div class="scenario-header" onclick="toggleScenario(this)">
                    <div class="scenario-info">
                        <span class="scenario-icon">❌</span>
                        <span class="scenario-title">TEST_RP-373</span>
                        <span class="scenario-status failed">FAILED</span>
                    </div>
                    <div class="scenario-meta">
                        <span class="execution-time">⏱️ 222.7s</span>
                        <span class="steps-summary">📊 21/25 passed</span>
                        <span class="toggle-arrow">▼</span>
                    </div>
                </div>
                <div class="scenario-content" style="display: none;">
                    <div class="steps-container">
                        
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 1</span>
                        <span class="step-text">Given the user is on the Rumah Pendidikan homepage</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Navigated to rumah pendidikan home</div>
                        
                    </div>
                </div>
                
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 2</span>
                        <span class="step-text">Then the user should see the following content and service list in the "Ruang GTK" section:</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Context step acknowledged</div>
                        
                    </div>
                </div>
                
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 3</span>
                        <span class="step-text">And | Sumber inspirasi peningkatan kompetensi serta kinerja Guru dan Tenaga Kependidikan (GTK) |</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Asserted Sumber inspirasi peningkatan kompetensi serta kinerja Guru dan Tenaga Kependidikan (GTK)</div>
                        
                    </div>
                </div>
                
                <div class="step failed">
                    <div class="step-header">
                        <span class="step-icon">❌</span>
                        <span class="step-number">Step 4</span>
                        <span class="step-text">And | Layanan yang Tersedia |</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: FAILED</div>
                        <div class="step-result">Result: Assertion failed for Layanan yang Tersedia</div>
                        <div class="step-error">Error: Expected: Layanan yang Tersedia, Actual: Layanan Unggulan di Rumah Pendidikan</div>
                    </div>
                </div>
                
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 5</span>
                        <span class="step-text">And | Diklat |</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Asserted Diklat</div>
                        
                    </div>
                </div>
                
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 6</span>
                        <span class="step-text">And | Sertifikasi Pendidik |</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Asserted Sertifikasi Pendidik</div>
                        
                    </div>
                </div>
                
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 7</span>
                        <span class="step-text">And | Pelatihan Mandiri |</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Asserted Pelatihan Mandiri</div>
                        
                    </div>
                </div>
                
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 8</span>
                        <span class="step-text">And | Komunitas |</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Asserted Komunitas</div>
                        
                    </div>
                </div>
                
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 9</span>
                        <span class="step-text">And | Pengelolaan Kinerja |</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Asserted Pengelolaan Kinerja</div>
                        
                    </div>
                </div>
                
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 10</span>
                        <span class="step-text">And | Seleksi Kepala Sekolah |</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Asserted Seleksi Kepala Sekolah</div>
                        
                    </div>
                </div>
                
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 11</span>
                        <span class="step-text">And | Refleksi Kompetensi |</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Asserted Refleksi Kompetensi</div>
                        
                    </div>
                </div>
                
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 12</span>
                        <span class="step-text">And | Perangkat Ajar |</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Asserted Perangkat Ajar</div>
                        
                    </div>
                </div>
                
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 13</span>
                        <span class="step-text">And | CP/ATP |</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Asserted CP/ATP</div>
                        
                    </div>
                </div>
                
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 14</span>
                        <span class="step-text">And | Ide Praktik |</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Asserted Ide Praktik</div>
                        
                    </div>
                </div>
                
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 15</span>
                        <span class="step-text">And | Bukti Karya |</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Asserted Bukti Karya</div>
                        
                    </div>
                </div>
                
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 16</span>
                        <span class="step-text">And | Video Inspirasi |</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Asserted Video Inspirasi</div>
                        
                    </div>
                </div>
                
                <div class="step failed">
                    <div class="step-header">
                        <span class="step-icon">❌</span>
                        <span class="step-number">Step 17</span>
                        <span class="step-text">And | Asesmen (Asesmen Murid dan AKM Kelas) |</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: FAILED</div>
                        <div class="step-result">Result: Assertion failed for Asesmen (Asesmen Murid dan AKM Kelas)</div>
                        <div class="step-error">Error: Expected: Asesmen (Asesmen Murid dan AKM Kelas), Actual: Langsung ke konten utama
Rumah Pendidikan
Cari
Pusat Informasi
Masuk
Jelajahi Ruang di Rumah Pendidikan

Ruang GTK

Ruang Murid

Ruang Sekolah

Ruang Bahasa

Ruang Pemerintah

Ruang Mitra

Ruang Publik

Ruang Orang Tua

Semangat Rumah Pendidikan

Rumah Pendidikan berkomitmen untuk menjadi pusat ruang kolaborasi bagi siswa, guru, orang tua, hingga pemerintah daerah

Pelajari Selengkapnya
Layanan Unggulan di Rumah Pendidikan

Sumber Belajar

Portal pembelajaran digital interaktif untuk semua jenjang.

Buku Bacaan Digital

Pusat Buku Digital untuk Gerakan Literasi Nasional.

Pengelolaan Kinerja

Perencanaan, pelaksanaan, dan penilaian Anda untuk pengembangan diri dan satdik.

Diklat

Pelatihan terbimbing menggunakan LMS.

Pelatihan Mandiri

Materi untuk pengembangan kompetensi Guru dan Tendik.

Siapa saja yang sudah memanfaatkan kemudahan kolaborasi di Rumah Pendidikan?

Simak perjalanan mereka

"Rumah Pendidikan merupakan gagasan yang sangat menarik dan terdepan untuk perkembangan zaman terkini. Di mana pergeseran paradigma baru, pendidikan tidak hanya sekedar di dalam kelas tapi juga bisa di ruang keluarga dan masyarakat."

Arham, S.Pd., M.Pd

Kepala Sekolah SD INPRES 33 Birobuli, Palu

"Kami menyambut baik dengan adanya rumah pendidikan ini sebagai langkah strategis Kemendikdasmen terutama memperluas akses pendidikan dan pemerataan mutu pendidikan. Karena diharapkan dengan adanya Rumdik ini sebagai salah satu langkah pendidikan yang Responsif, Akuntabel, Melayani, Adaptif, dan Harmonis. Semoga menjadi langkah terbaik untuk Indonesia maju."

Dr. Firman Oktora, S.SI, M.PD.

Kabalaitekkomdik Jawa Barat

"Menurut saya Rumah Pendidikan sangat keren dan sangat membantu. Karena bagi saya murid kelas 6, Rumah Pendidikan bisa membantu dalam belajar dengan simpel, praktis, dan bisa mempelajari berbagai macam hal dalam satu platform, jadi sangat membantu."

Alena

Pelajar Gen KiHajar

Informasi Untuk Anda

Berita
Sambut Tahun Ajaran Baru 2025 dengan Penuh Inspirasi!
14 Juli 2025 | 10.32 WIB
Baca Selengkapnya
Berita
Laman Direktori Portal SPMB Berbasis SIAP SPMB Online
11 Juli 2025 | 15.13 WIB
Baca Selengkapnya
Berita
Tanya Jawab seputar Tes Kemampuan Akademik (ТKА)
24 Juni 2025 | 15.00 WIB
Baca Selengkapnya
Lihat lebih banyak
Pertanyaan yang paling sering ditanyakan
Apa manfaat menggunakan Rumah Pendidikan?
Bagaimana cara mengakses Rumah Pendidikan?
Apakah pengguna harus menggunakan Akun belajar.id untuk mengakses Rumah Pendidikan?
Siapa saja pengguna yang disarankan untuk menggunakan platform Rumah Pendidikan
Apa perbedaan rumah pendidikan dengan platform digital pendidikan milik Kemendikdasmen lainnya?
Ke Pusat Informasi

Kementerian Pendidikan Dasar dan Menengah

Kompleks Kementerian Pendidikan dan Kebudayaan, Senayan, Jakarta Pusat 10270

Navigasi

Pusat Bantuan Rumah Pendidikan

Syarat & Ketentuan

Kebijakan Privasi

Unduh Aplikasi Rumah Pendidikan</div>
                    </div>
                </div>
                
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 18</span>
                        <span class="step-text">And | Kelas |</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Asserted Kelas</div>
                        
                    </div>
                </div>
                
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 19</span>
                        <span class="step-text">And | Dokumen Rujukan Pengelolaan Kinerja |</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Asserted Dokumen Rujukan Pengelolaan Kinerja</div>
                        
                    </div>
                </div>
                
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 20</span>
                        <span class="step-text">And | Dokumen Rujukan Pengelolaan Pembelajaran |</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Asserted Dokumen Rujukan Pengelolaan Pembelajaran</div>
                        
                    </div>
                </div>
                
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 21</span>
                        <span class="step-text">And | Dokumen Rujukan Pengelolaan Satuan Pendidikan |</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Asserted Dokumen Rujukan Pengelolaan Satuan Pendidikan</div>
                        
                    </div>
                </div>
                
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 22</span>
                        <span class="step-text">And | Dokumen Rujukan Peningkatan Kompetensi |</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Asserted Dokumen Rujukan Peningkatan Kompetensi</div>
                        
                    </div>
                </div>
                
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 23</span>
                        <span class="step-text">When I click the element</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Clicked the element</div>
                        
                    </div>
                </div>
                
                <div class="step failed">
                    <div class="step-header">
                        <span class="step-icon">❌</span>
                        <span class="step-number">Step 24</span>
                        <span class="step-text">Then the system should redirect the user to the corresponding service page</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: FAILED</div>
                        <div class="step-result">Result: Assertion failed for the corresponding service page</div>
                        <div class="step-error">Error: Expected: the corresponding service page, Actual: Langsung ke konten utama
Rumah Pendidikan
Cari
Pusat Informasi
Masuk
Jelajahi Ruang di Rumah Pendidikan

Ruang GTK

Ruang Murid

Ruang Sekolah

Ruang Bahasa

Ruang Pemerintah

Ruang Mitra

Ruang Publi</div>
                    </div>
                </div>
                
                <div class="step failed">
                    <div class="step-header">
                        <span class="step-icon">❌</span>
                        <span class="step-number">Step 25</span>
                        <span class="step-text">And the redirection should happen in the same browser tab</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: FAILED</div>
                        <div class="step-result">Result: Assertion failed for happen in the same browser tab</div>
                        <div class="step-error">Error: Expected: happen in the same browser tab, Actual: Langsung ke konten utama
Rumah Pendidikan
Cari
Pusat Informasi
Masuk
Jelajahi Ruang di Rumah Pendidikan

Ruang GTK

Ruang Murid

Ruang Sekolah

Ruang Bahasa

Ruang Pemerintah

Ruang Mitra

Ruang Publik

Ruang Orang Tua

Semangat Rumah Pendidikan

Rumah Pendidikan berkomitmen untuk menjadi pusat ruang kolaborasi bagi siswa, guru, orang tua, hingga pemerintah daerah

Pelajari Selengkapnya
Layanan Unggulan di Rumah Pendidikan

Sumber Belajar

Portal pembelajaran digital interaktif untuk semua jenjang.

Buku Bacaan Digital

Pusat Buku Digital untuk Gerakan Literasi Nasional.

Pengelolaan Kinerja

Perencanaan, pelaksanaan, dan penilaian Anda untuk pengembangan diri dan satdik.

Diklat

Pelatihan terbimbing menggunakan LMS.

Pelatihan Mandiri

Materi untuk pengembangan kompetensi Guru dan Tendik.

Siapa saja yang sudah memanfaatkan kemudahan kolaborasi di Rumah Pendidikan?

Simak perjalanan mereka

"Rumah Pendidikan merupakan gagasan yang sangat menarik dan terdepan untuk perkembangan zaman terkini. Di mana pergeseran paradigma baru, pendidikan tidak hanya sekedar di dalam kelas tapi juga bisa di ruang keluarga dan masyarakat."

Arham, S.Pd., M.Pd

Kepala Sekolah SD INPRES 33 Birobuli, Palu

"Kami menyambut baik dengan adanya rumah pendidikan ini sebagai langkah strategis Kemendikdasmen terutama memperluas akses pendidikan dan pemerataan mutu pendidikan. Karena diharapkan dengan adanya Rumdik ini sebagai salah satu langkah pendidikan yang Responsif, Akuntabel, Melayani, Adaptif, dan Harmonis. Semoga menjadi langkah terbaik untuk Indonesia maju."

Dr. Firman Oktora, S.SI, M.PD.

Kabalaitekkomdik Jawa Barat

"Menurut saya Rumah Pendidikan sangat keren dan sangat membantu. Karena bagi saya murid kelas 6, Rumah Pendidikan bisa membantu dalam belajar dengan simpel, praktis, dan bisa mempelajari berbagai macam hal dalam satu platform, jadi sangat membantu."

Alena

Pelajar Gen KiHajar

Informasi Untuk Anda

Berita
Sambut Tahun Ajaran Baru 2025 dengan Penuh Inspirasi!
14 Juli 2025 | 10.32 WIB
Baca Selengkapnya
Berita
Laman Direktori Portal SPMB Berbasis SIAP SPMB Online
11 Juli 2025 | 15.13 WIB
Baca Selengkapnya
Berita
Tanya Jawab seputar Tes Kemampuan Akademik (ТKА)
24 Juni 2025 | 15.00 WIB
Baca Selengkapnya
Lihat lebih banyak
Pertanyaan yang paling sering ditanyakan
Apa manfaat menggunakan Rumah Pendidikan?
Bagaimana cara mengakses Rumah Pendidikan?
Apakah pengguna harus menggunakan Akun belajar.id untuk mengakses Rumah Pendidikan?
Siapa saja pengguna yang disarankan untuk menggunakan platform Rumah Pendidikan
Apa perbedaan rumah pendidikan dengan platform digital pendidikan milik Kemendikdasmen lainnya?
Ke Pusat Informasi

Kementerian Pendidikan Dasar dan Menengah

Kompleks Kementerian Pendidikan dan Kebudayaan, Senayan, Jakarta Pusat 10270

Navigasi

Pusat Bantuan Rumah Pendidikan

Syarat & Ketentuan

Kebijakan Privasi

Unduh Aplikasi Rumah Pendidikan</div>
                    </div>
                </div>
                
                    </div>
                </div>
            </div>
            
            <div class="scenario-section">
                <div class="scenario-header" onclick="toggleScenario(this)">
                    <div class="scenario-info">
                        <span class="scenario-icon">❓</span>
                        <span class="scenario-title">TEST_RP-374</span>
                        <span class="scenario-status failed">FAILED</span>
                    </div>
                    <div class="scenario-meta">
                        <span class="execution-time">⏱️ 90.2s</span>
                        <span class="steps-summary">📊 5/9 passed</span>
                        <span class="toggle-arrow">▼</span>
                    </div>
                </div>
                <div class="scenario-content" style="display: none;">
                    <div class="steps-container">
                        
                <div class="step failed">
                    <div class="step-header">
                        <span class="step-icon">❌</span>
                        <span class="step-number">Step 1</span>
                        <span class="step-text">Given the user accesses the Rumah Pendidikan homepage</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: FAILED</div>
                        <div class="step-result">Result: Assertion failed for None</div>
                        <div class="step-error">Error: Expected: None, Actual: Assertion error: 'NoneType' object has no attribute 'lower'</div>
                    </div>
                </div>
                
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 2</span>
                        <span class="step-text">Then the user should see the following "Ruang Publik" items:</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Context step acknowledged</div>
                        
                    </div>
                </div>
                
                <div class="step failed">
                    <div class="step-header">
                        <span class="step-icon">❌</span>
                        <span class="step-number">Step 3</span>
                        <span class="step-text">And | Service Name |</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: FAILED</div>
                        <div class="step-result">Result: Assertion failed for Service Name</div>
                        <div class="step-error">Error: Expected: Service Name, Actual: Langsung ke konten utama
Rumah Pendidikan
Cari
Pusat Informasi
Masuk
Jelajahi Ruang di Rumah Pendidikan

Ruang GTK

Ruang Murid

Ruang Sekolah

Ruang Bahasa

Ruang Pemerintah

Ruang Mitra

Ruang Publi</div>
                    </div>
                </div>
                
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 4</span>
                        <span class="step-text">And | Informasi dan materi pendidikan |</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Asserted Informasi dan materi pendidikan</div>
                        
                    </div>
                </div>
                
                <div class="step failed">
                    <div class="step-header">
                        <span class="step-icon">❌</span>
                        <span class="step-number">Step 5</span>
                        <span class="step-text">And | Layanan yang Tersedia |</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: FAILED</div>
                        <div class="step-result">Result: Assertion failed for Layanan yang Tersedia</div>
                        <div class="step-error">Error: Expected: Layanan yang Tersedia, Actual: Layanan Unggulan di Rumah Pendidikan</div>
                    </div>
                </div>
                
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 6</span>
                        <span class="step-text">And | Bantuan Pendidikan |</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Asserted Bantuan Pendidikan</div>
                        
                    </div>
                </div>
                
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 7</span>
                        <span class="step-text">And | Informasi Data Pendidikan |</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Asserted Informasi Data Pendidikan</div>
                        
                    </div>
                </div>
                
                <div class="step failed">
                    <div class="step-header">
                        <span class="step-icon">❌</span>
                        <span class="step-number">Step 8</span>
                        <span class="step-text">And | Kursus Digital |</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: FAILED</div>
                        <div class="step-result">Result: Assertion failed for Kursus Digital</div>
                        <div class="step-error">Error: Expected: Kursus Digital, Actual: Portal pembelajaran digital interaktif untuk semua jenjang.</div>
                    </div>
                </div>
                
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 9</span>
                        <span class="step-text">And | Layanan Informasi dan Pengaduan |</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Asserted Layanan Informasi dan Pengaduan</div>
                        
                    </div>
                </div>
                
                <div class="step undefined">
                    <div class="step-header">
                        <span class="step-icon">❓</span>
                        <span class="step-number">Step 10</span>
                        <span class="step-text">And | Majalah Pendidikan |</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: UNDEFINED</div>
                        
                        
                    </div>
                </div>
                
                <div class="step undefined">
                    <div class="step-header">
                        <span class="step-icon">❓</span>
                        <span class="step-number">Step 11</span>
                        <span class="step-text">And | Pusat Perbukuan |</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: UNDEFINED</div>
                        
                        
                    </div>
                </div>
                
                <div class="step undefined">
                    <div class="step-header">
                        <span class="step-icon">❓</span>
                        <span class="step-number">Step 12</span>
                        <span class="step-text">When I click the element</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: UNDEFINED</div>
                        
                        
                    </div>
                </div>
                
                <div class="step undefined">
                    <div class="step-header">
                        <span class="step-icon">❓</span>
                        <span class="step-number">Step 13</span>
                        <span class="step-text">Then the system should redirect the user to the selected service</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: UNDEFINED</div>
                        
                        
                    </div>
                </div>
                
                <div class="step undefined">
                    <div class="step-header">
                        <span class="step-icon">❓</span>
                        <span class="step-number">Step 14</span>
                        <span class="step-text">And the redirection should happen in the same browser tab</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: UNDEFINED</div>
                        
                        
                    </div>
                </div>
                
                    </div>
                </div>
            </div>
            
            <div class="scenario-section">
                <div class="scenario-header" onclick="toggleScenario(this)">
                    <div class="scenario-info">
                        <span class="scenario-icon">❌</span>
                        <span class="scenario-title">TEST_RP-375</span>
                        <span class="scenario-status failed">FAILED</span>
                    </div>
                    <div class="scenario-meta">
                        <span class="execution-time">⏱️ 125.1s</span>
                        <span class="steps-summary">📊 8/12 passed</span>
                        <span class="toggle-arrow">▼</span>
                    </div>
                </div>
                <div class="scenario-content" style="display: none;">
                    <div class="steps-container">
                        
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 1</span>
                        <span class="step-text">Given the user is on the Rumah Pendidikan homepage</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Navigated to rumah pendidikan home</div>
                        
                    </div>
                </div>
                
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 2</span>
                        <span class="step-text">Then the user should see the following content and list of services in the "Ruang Pemerintah":</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Context step acknowledged</div>
                        
                    </div>
                </div>
                
                <div class="step failed">
                    <div class="step-header">
                        <span class="step-icon">❌</span>
                        <span class="step-number">Step 3</span>
                        <span class="step-text">And | Content Description |</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: FAILED</div>
                        <div class="step-result">Result: Assertion failed for Content Description</div>
                        <div class="step-error">Error: Expected: Content Description, Actual: Langsung ke konten utama
Rumah Pendidikan
Cari
Pusat Informasi
Masuk
Jelajahi Ruang di Rumah Pendidikan

Ruang GTK

Ruang Murid

Ruang Sekolah

Ruang Bahasa

Ruang Pemerintah

Ruang Mitra

Ruang Publi</div>
                    </div>
                </div>
                
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 4</span>
                        <span class="step-text">And | Layanan pemerintah daerah untuk mengelola sumber daya sekolah hingga evaluasi pendidikan |</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Asserted Layanan pemerintah daerah untuk mengelola sumber daya sekolah hingga evaluasi pendidikan</div>
                        
                    </div>
                </div>
                
                <div class="step failed">
                    <div class="step-header">
                        <span class="step-icon">❌</span>
                        <span class="step-number">Step 5</span>
                        <span class="step-text">And | Layanan yang Tersedia |</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: FAILED</div>
                        <div class="step-result">Result: Assertion failed for Layanan yang Tersedia</div>
                        <div class="step-error">Error: Expected: Layanan yang Tersedia, Actual: Layanan Unggulan di Rumah Pendidikan</div>
                    </div>
                </div>
                
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 6</span>
                        <span class="step-text">And | Akun Pendidikan |</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Asserted Akun Pendidikan</div>
                        
                    </div>
                </div>
                
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 7</span>
                        <span class="step-text">And | Neraca Pendidikan Daerah |</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Asserted Neraca Pendidikan Daerah</div>
                        
                    </div>
                </div>
                
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 8</span>
                        <span class="step-text">And | Rapor Pendidikan Daerah |</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Asserted Rapor Pendidikan Daerah</div>
                        
                    </div>
                </div>
                
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 9</span>
                        <span class="step-text">And | Manajemen Aplikasi Rencana Kegiatan dan Anggaran Sekolah |</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Asserted Manajemen Aplikasi Rencana Kegiatan dan Anggaran Sekolah</div>
                        
                    </div>
                </div>
                
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 10</span>
                        <span class="step-text">When I click the element</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Clicked the element</div>
                        
                    </div>
                </div>
                
                <div class="step failed">
                    <div class="step-header">
                        <span class="step-icon">❌</span>
                        <span class="step-number">Step 11</span>
                        <span class="step-text">Then the system should redirect the user to the corresponding service page</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: FAILED</div>
                        <div class="step-result">Result: Assertion failed for the corresponding service page</div>
                        <div class="step-error">Error: Expected: the corresponding service page, Actual: Langsung ke konten utama
Rumah Pendidikan
Cari
Pusat Informasi
Masuk
Jelajahi Ruang di Rumah Pendidikan

Ruang GTK

Ruang Murid

Ruang Sekolah

Ruang Bahasa

Ruang Pemerintah

Ruang Mitra

Ruang Publi</div>
                    </div>
                </div>
                
                <div class="step failed">
                    <div class="step-header">
                        <span class="step-icon">❌</span>
                        <span class="step-number">Step 12</span>
                        <span class="step-text">And the redirection should happen in the same browser tab</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: FAILED</div>
                        <div class="step-result">Result: Assertion failed for happen in the same browser tab</div>
                        <div class="step-error">Error: Expected: happen in the same browser tab, Actual: Langsung ke konten utama
Rumah Pendidikan
Cari
Pusat Informasi
Masuk
Jelajahi Ruang di Rumah Pendidikan

Ruang GTK

Ruang Murid

Ruang Sekolah

Ruang Bahasa

Ruang Pemerintah

Ruang Mitra

Ruang Publik

Ruang Orang Tua

Semangat Rumah Pendidikan

Rumah Pendidikan berkomitmen untuk menjadi pusat ruang kolaborasi bagi siswa, guru, orang tua, hingga pemerintah daerah

Pelajari Selengkapnya
Layanan Unggulan di Rumah Pendidikan

Sumber Belajar

Portal pembelajaran digital interaktif untuk semua jenjang.

Buku Bacaan Digital

Pusat Buku Digital untuk Gerakan Literasi Nasional.

Pengelolaan Kinerja

Perencanaan, pelaksanaan, dan penilaian Anda untuk pengembangan diri dan satdik.

Diklat

Pelatihan terbimbing menggunakan LMS.

Pelatihan Mandiri

Materi untuk pengembangan kompetensi Guru dan Tendik.

Siapa saja yang sudah memanfaatkan kemudahan kolaborasi di Rumah Pendidikan?

Simak perjalanan mereka

"Rumah Pendidikan merupakan gagasan yang sangat menarik dan terdepan untuk perkembangan zaman terkini. Di mana pergeseran paradigma baru, pendidikan tidak hanya sekedar di dalam kelas tapi juga bisa di ruang keluarga dan masyarakat."

Arham, S.Pd., M.Pd

Kepala Sekolah SD INPRES 33 Birobuli, Palu

"Kami menyambut baik dengan adanya rumah pendidikan ini sebagai langkah strategis Kemendikdasmen terutama memperluas akses pendidikan dan pemerataan mutu pendidikan. Karena diharapkan dengan adanya Rumdik ini sebagai salah satu langkah pendidikan yang Responsif, Akuntabel, Melayani, Adaptif, dan Harmonis. Semoga menjadi langkah terbaik untuk Indonesia maju."

Dr. Firman Oktora, S.SI, M.PD.

Kabalaitekkomdik Jawa Barat

"Menurut saya Rumah Pendidikan sangat keren dan sangat membantu. Karena bagi saya murid kelas 6, Rumah Pendidikan bisa membantu dalam belajar dengan simpel, praktis, dan bisa mempelajari berbagai macam hal dalam satu platform, jadi sangat membantu."

Alena

Pelajar Gen KiHajar

Informasi Untuk Anda

Berita
Sambut Tahun Ajaran Baru 2025 dengan Penuh Inspirasi!
14 Juli 2025 | 10.32 WIB
Baca Selengkapnya
Berita
Laman Direktori Portal SPMB Berbasis SIAP SPMB Online
11 Juli 2025 | 15.13 WIB
Baca Selengkapnya
Berita
Tanya Jawab seputar Tes Kemampuan Akademik (ТKА)
24 Juni 2025 | 15.00 WIB
Baca Selengkapnya
Lihat lebih banyak
Pertanyaan yang paling sering ditanyakan
Apa manfaat menggunakan Rumah Pendidikan?
Bagaimana cara mengakses Rumah Pendidikan?
Apakah pengguna harus menggunakan Akun belajar.id untuk mengakses Rumah Pendidikan?
Siapa saja pengguna yang disarankan untuk menggunakan platform Rumah Pendidikan
Apa perbedaan rumah pendidikan dengan platform digital pendidikan milik Kemendikdasmen lainnya?
Ke Pusat Informasi

Kementerian Pendidikan Dasar dan Menengah

Kompleks Kementerian Pendidikan dan Kebudayaan, Senayan, Jakarta Pusat 10270

Navigasi

Pusat Bantuan Rumah Pendidikan

Syarat & Ketentuan

Kebijakan Privasi

Unduh Aplikasi Rumah Pendidikan</div>
                    </div>
                </div>
                
                    </div>
                </div>
            </div>
            
            <div class="scenario-section">
                <div class="scenario-header" onclick="toggleScenario(this)">
                    <div class="scenario-info">
                        <span class="scenario-icon">❌</span>
                        <span class="scenario-title">TEST_RP-46</span>
                        <span class="scenario-status failed">FAILED</span>
                    </div>
                    <div class="scenario-meta">
                        <span class="execution-time">⏱️ 15.3s</span>
                        <span class="steps-summary">📊 1/3 passed</span>
                        <span class="toggle-arrow">▼</span>
                    </div>
                </div>
                <div class="scenario-content" style="display: none;">
                    <div class="steps-container">
                        
                <div class="step failed">
                    <div class="step-header">
                        <span class="step-icon">❌</span>
                        <span class="step-number">Step 1</span>
                        <span class="step-text">Given the user has a web browser open</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: FAILED</div>
                        <div class="step-result">Result: Assertion failed for None</div>
                        <div class="step-error">Error: Expected: None, Actual: Assertion error: 'NoneType' object has no attribute 'lower'</div>
                    </div>
                </div>
                
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 2</span>
                        <span class="step-text">Given When the user navigates to "https://rumah-baru.staging.belajar.id/"</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Navigated to to </div>
                        
                    </div>
                </div>
                
                <div class="step failed">
                    <div class="step-header">
                        <span class="step-icon">❌</span>
                        <span class="step-number">Step 3</span>
                        <span class="step-text">Then the website should be accessible</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: FAILED</div>
                        <div class="step-result">Result: Assertion failed for be accessible</div>
                        <div class="step-error">Error: Expected: be accessible, Actual: Rumah Pendidikan berkomitmen untuk menjadi pusat ruang kolaborasi bagi siswa, guru, orang tua, hingga pemerintah daerah</div>
                    </div>
                </div>
                
                    </div>
                </div>
            </div>
            
            <div class="scenario-section">
                <div class="scenario-header" onclick="toggleScenario(this)">
                    <div class="scenario-info">
                        <span class="scenario-icon">❌</span>
                        <span class="scenario-title">TEST_RP-543</span>
                        <span class="scenario-status failed">FAILED</span>
                    </div>
                    <div class="scenario-meta">
                        <span class="execution-time">⏱️ 16.2s</span>
                        <span class="steps-summary">📊 2/3 passed</span>
                        <span class="toggle-arrow">▼</span>
                    </div>
                </div>
                <div class="scenario-content" style="display: none;">
                    <div class="steps-container">
                        
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 1</span>
                        <span class="step-text">Given the user is on the homepage or news section of Rumah Pendidikan</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Navigated to homepage or news section of rumah pendidikan</div>
                        
                    </div>
                </div>
                
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 2</span>
                        <span class="step-text">When the page loads successfully</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Waited 1.0 seconds</div>
                        
                    </div>
                </div>
                
                <div class="step failed">
                    <div class="step-header">
                        <span class="step-icon">❌</span>
                        <span class="step-number">Step 3</span>
                        <span class="step-text">Then I should see a list of available "Informasi Untuk Anda" displayed with thumbnails and titles</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: FAILED</div>
                        <div class="step-result">Result: Assertion failed for see a list of available </div>
                        <div class="step-error">Error: Expected: see a list of available , Actual: Langsung ke konten utama
Rumah Pendidikan
Cari
Pusat Informasi
Masuk
Jelajahi Ruang di Rumah Pendidikan

Ruang GTK

Ruang Murid

Ruang Sekolah

Ruang Bahasa

Ruang Pemerintah

Ruang Mitra

Ruang Publi</div>
                    </div>
                </div>
                
                    </div>
                </div>
            </div>
            
            <div class="scenario-section">
                <div class="scenario-header" onclick="toggleScenario(this)">
                    <div class="scenario-info">
                        <span class="scenario-icon">❌</span>
                        <span class="scenario-title">TEST_RP-546</span>
                        <span class="scenario-status failed">FAILED</span>
                    </div>
                    <div class="scenario-meta">
                        <span class="execution-time">⏱️ 24.3s</span>
                        <span class="steps-summary">📊 1/3 passed</span>
                        <span class="toggle-arrow">▼</span>
                    </div>
                </div>
                <div class="scenario-content" style="display: none;">
                    <div class="steps-container">
                        
                <div class="step skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-number">Step 1</span>
                        <span class="step-text">Given the user is viewing the "Informasi Untuk Anda" listing</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: SKIPPED</div>
                        <div class="step-result">Result: Unknown or unsupported action for step: the user is viewing the "Informasi Untuk Anda" listing</div>
                        <div class="step-error">Error: Unknown or unsupported action</div>
                    </div>
                </div>
                
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 2</span>
                        <span class="step-text">When I click on a thumbnail or title of a "Informasi Untuk Anda"</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Clicked a thumbnail or title of a </div>
                        
                    </div>
                </div>
                
                <div class="step failed">
                    <div class="step-header">
                        <span class="step-icon">❌</span>
                        <span class="step-number">Step 3</span>
                        <span class="step-text">Then the user should be redirected to the detail page of that Informasi Untuk Anda</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: FAILED</div>
                        <div class="step-result">Result: Assertion failed for the detail page of that informasi untuk anda</div>
                        <div class="step-error">Error: Expected: the detail page of that informasi untuk anda, Actual: Langsung ke konten utama
Rumah Pendidikan
Cari
Pusat Informasi
Masuk
Jelajahi Ruang di Rumah Pendidikan

Ruang GTK

Ruang Murid

Ruang Sekolah

Ruang Bahasa

Ruang Pemerintah

Ruang Mitra

Ruang Publik

Ruang Orang Tua

Semangat Rumah Pendidikan

Rumah Pendidikan berkomitmen untuk menjadi pusat ruang kolaborasi bagi siswa, guru, orang tua, hingga pemerintah daerah

Pelajari Selengkapnya
Layanan Unggulan di Rumah Pendidikan

Sumber Belajar

Portal pembelajaran digital interaktif untuk semua jenjang.

Buku Bacaan Digital

Pusat Buku Digital untuk Gerakan Literasi Nasional.

Pengelolaan Kinerja

Perencanaan, pelaksanaan, dan penilaian Anda untuk pengembangan diri dan satdik.

Diklat

Pelatihan terbimbing menggunakan LMS.

Pelatihan Mandiri

Materi untuk pengembangan kompetensi Guru dan Tendik.

Siapa saja yang sudah memanfaatkan kemudahan kolaborasi di Rumah Pendidikan?

Simak perjalanan mereka

"Rumah Pendidikan merupakan gagasan yang sangat menarik dan terdepan untuk perkembangan zaman terkini. Di mana pergeseran paradigma baru, pendidikan tidak hanya sekedar di dalam kelas tapi juga bisa di ruang keluarga dan masyarakat."

Arham, S.Pd., M.Pd

Kepala Sekolah SD INPRES 33 Birobuli, Palu

"Kami menyambut baik dengan adanya rumah pendidikan ini sebagai langkah strategis Kemendikdasmen terutama memperluas akses pendidikan dan pemerataan mutu pendidikan. Karena diharapkan dengan adanya Rumdik ini sebagai salah satu langkah pendidikan yang Responsif, Akuntabel, Melayani, Adaptif, dan Harmonis. Semoga menjadi langkah terbaik untuk Indonesia maju."

Dr. Firman Oktora, S.SI, M.PD.

Kabalaitekkomdik Jawa Barat

"Menurut saya Rumah Pendidikan sangat keren dan sangat membantu. Karena bagi saya murid kelas 6, Rumah Pendidikan bisa membantu dalam belajar dengan simpel, praktis, dan bisa mempelajari berbagai macam hal dalam satu platform, jadi sangat membantu."

Alena

Pelajar Gen KiHajar

Informasi Untuk Anda

Berita
Sambut Tahun Ajaran Baru 2025 dengan Penuh Inspirasi!
14 Juli 2025 | 10.32 WIB
Baca Selengkapnya
Berita
Laman Direktori Portal SPMB Berbasis SIAP SPMB Online
11 Juli 2025 | 15.13 WIB
Baca Selengkapnya
Berita
Tanya Jawab seputar Tes Kemampuan Akademik (ТKА)
24 Juni 2025 | 15.00 WIB
Baca Selengkapnya
Lihat lebih banyak
Pertanyaan yang paling sering ditanyakan
Apa manfaat menggunakan Rumah Pendidikan?
Bagaimana cara mengakses Rumah Pendidikan?
Apakah pengguna harus menggunakan Akun belajar.id untuk mengakses Rumah Pendidikan?
Siapa saja pengguna yang disarankan untuk menggunakan platform Rumah Pendidikan
Apa perbedaan rumah pendidikan dengan platform digital pendidikan milik Kemendikdasmen lainnya?
Ke Pusat Informasi

Kementerian Pendidikan Dasar dan Menengah

Kompleks Kementerian Pendidikan dan Kebudayaan, Senayan, Jakarta Pusat 10270

Navigasi

Pusat Bantuan Rumah Pendidikan

Syarat & Ketentuan

Kebijakan Privasi

Unduh Aplikasi Rumah Pendidikan</div>
                    </div>
                </div>
                
                    </div>
                </div>
            </div>
            
            <div class="scenario-section">
                <div class="scenario-header" onclick="toggleScenario(this)">
                    <div class="scenario-info">
                        <span class="scenario-icon">❌</span>
                        <span class="scenario-title">TEST_RP-547</span>
                        <span class="scenario-status failed">FAILED</span>
                    </div>
                    <div class="scenario-meta">
                        <span class="execution-time">⏱️ 24.3s</span>
                        <span class="steps-summary">📊 2/3 passed</span>
                        <span class="toggle-arrow">▼</span>
                    </div>
                </div>
                <div class="scenario-content" style="display: none;">
                    <div class="steps-container">
                        
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 1</span>
                        <span class="step-text">Given the user is on the "Informasi Untuk Anda" listing page</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Navigated to "informasi untuk anda" listing</div>
                        
                    </div>
                </div>
                
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 2</span>
                        <span class="step-text">When I click "Next Page" button</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Clicked next page</div>
                        
                    </div>
                </div>
                
                <div class="step failed">
                    <div class="step-header">
                        <span class="step-icon">❌</span>
                        <span class="step-number">Step 3</span>
                        <span class="step-text">Then I should see the next set of articles listed</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: FAILED</div>
                        <div class="step-result">Result: Assertion failed for see the next set of articles listed</div>
                        <div class="step-error">Error: Expected: see the next set of articles listed, Actual: Langsung ke konten utama
Rumah Pendidikan
Cari
Pusat Informasi
Masuk
Jelajahi Ruang di Rumah Pendidikan

Ruang GTK

Ruang Murid

Ruang Sekolah

Ruang Bahasa

Ruang Pemerintah

Ruang Mitra

Ruang Publi</div>
                    </div>
                </div>
                
                    </div>
                </div>
            </div>
            
            <div class="scenario-section">
                <div class="scenario-header" onclick="toggleScenario(this)">
                    <div class="scenario-info">
                        <span class="scenario-icon">❌</span>
                        <span class="scenario-title">TEST_RP-549</span>
                        <span class="scenario-status failed">FAILED</span>
                    </div>
                    <div class="scenario-meta">
                        <span class="execution-time">⏱️ 15.4s</span>
                        <span class="steps-summary">📊 2/3 passed</span>
                        <span class="toggle-arrow">▼</span>
                    </div>
                </div>
                <div class="scenario-content" style="display: none;">
                    <div class="steps-container">
                        
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 1</span>
                        <span class="step-text">Given the user is on the homepage of Rumah Pendidikan</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Navigated to homepage of rumah pendidikan</div>
                        
                    </div>
                </div>
                
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 2</span>
                        <span class="step-text">When I scroll to "Semangat Rumah Pendidikan" segment</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Scrolled to 'semangat rumah pendidikan'</div>
                        
                    </div>
                </div>
                
                <div class="step failed">
                    <div class="step-header">
                        <span class="step-icon">❌</span>
                        <span class="step-number">Step 3</span>
                        <span class="step-text">Then I should see a blue button labeled appropriately (e.g., "pelajari selengkapnya")</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: FAILED</div>
                        <div class="step-result">Result: Assertion failed for see a blue button labeled appropriately (e.g., </div>
                        <div class="step-error">Error: Expected: see a blue button labeled appropriately (e.g., , Actual: Langsung ke konten utama
Rumah Pendidikan
Cari
Pusat Informasi
Masuk
Jelajahi Ruang di Rumah Pendidikan

Ruang GTK

Ruang Murid

Ruang Sekolah

Ruang Bahasa

Ruang Pemerintah

Ruang Mitra

Ruang Publi</div>
                    </div>
                </div>
                
                    </div>
                </div>
            </div>
            
            <div class="scenario-section">
                <div class="scenario-header" onclick="toggleScenario(this)">
                    <div class="scenario-info">
                        <span class="scenario-icon">❌</span>
                        <span class="scenario-title">TEST_RP-552</span>
                        <span class="scenario-status failed">FAILED</span>
                    </div>
                    <div class="scenario-meta">
                        <span class="execution-time">⏱️ 24.3s</span>
                        <span class="steps-summary">📊 2/3 passed</span>
                        <span class="toggle-arrow">▼</span>
                    </div>
                </div>
                <div class="scenario-content" style="display: none;">
                    <div class="steps-container">
                        
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 1</span>
                        <span class="step-text">Given the user is on the Tentang Rumah Pendidikan page</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Navigated to tentang rumah pendidikan</div>
                        
                    </div>
                </div>
                
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 2</span>
                        <span class="step-text">When I click "Cetak Biru Rumah Pendidikan" download button</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Clicked cetak biru rumah pendidikan</div>
                        
                    </div>
                </div>
                
                <div class="step failed">
                    <div class="step-header">
                        <span class="step-icon">❌</span>
                        <span class="step-number">Step 3</span>
                        <span class="step-text">Then the blueprint file should automatically be downloaded to the user’s device</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: FAILED</div>
                        <div class="step-result">Result: Assertion failed for automatically be downloaded to the user’s device</div>
                        <div class="step-error">Error: Expected: automatically be downloaded to the user’s device, Actual: Langsung ke konten utama
Rumah Pendidikan
Cari
Pusat Informasi
Masuk
Jelajahi Ruang di Rumah Pendidikan

Ruang GTK

Ruang Murid

Ruang Sekolah

Ruang Bahasa

Ruang Pemerintah

Ruang Mitra

Ruang Publi</div>
                    </div>
                </div>
                
                    </div>
                </div>
            </div>
            
            <div class="scenario-section">
                <div class="scenario-header" onclick="toggleScenario(this)">
                    <div class="scenario-info">
                        <span class="scenario-icon">❌</span>
                        <span class="scenario-title">TEST_RP-553</span>
                        <span class="scenario-status failed">FAILED</span>
                    </div>
                    <div class="scenario-meta">
                        <span class="execution-time">⏱️ 24.4s</span>
                        <span class="steps-summary">📊 2/4 passed</span>
                        <span class="toggle-arrow">▼</span>
                    </div>
                </div>
                <div class="scenario-content" style="display: none;">
                    <div class="steps-container">
                        
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 1</span>
                        <span class="step-text">Given the user is on the homepage</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Navigated to home</div>
                        
                    </div>
                </div>
                
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 2</span>
                        <span class="step-text">When I click the blue button</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Clicked the blue button</div>
                        
                    </div>
                </div>
                
                <div class="step skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-number">Step 3</span>
                        <span class="step-text">And the redirection URL is broken or misconfigured</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: SKIPPED</div>
                        <div class="step-result">Result: Unknown or unsupported action for step: the redirection URL is broken or misconfigured</div>
                        <div class="step-error">Error: Unknown or unsupported action</div>
                    </div>
                </div>
                
                <div class="step failed">
                    <div class="step-header">
                        <span class="step-icon">❌</span>
                        <span class="step-number">Step 4</span>
                        <span class="step-text">Then I should see an error page (e.g., 404 or 500)</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: FAILED</div>
                        <div class="step-result">Result: Assertion failed for error page</div>
                        <div class="step-error">Error: Expected: error page, Actual: Langsung ke konten utama
Rumah Pendidikan
Cari
Pusat Informasi
Masuk
Jelajahi Ruang di Rumah Pendidikan

Ruang GTK

Ruang Murid

Ruang Sekolah

Ruang Bahasa

Ruang Pemerintah

Ruang Mitra

Ruang Publi</div>
                    </div>
                </div>
                
                    </div>
                </div>
            </div>
            
            <div class="scenario-section">
                <div class="scenario-header" onclick="toggleScenario(this)">
                    <div class="scenario-info">
                        <span class="scenario-icon">⏭️</span>
                        <span class="scenario-title">TEST_RP-83</span>
                        <span class="scenario-status failed">FAILED</span>
                    </div>
                    <div class="scenario-meta">
                        <span class="execution-time">⏱️ 34.3s</span>
                        <span class="steps-summary">📊 3/5 passed</span>
                        <span class="toggle-arrow">▼</span>
                    </div>
                </div>
                <div class="scenario-content" style="display: none;">
                    <div class="steps-container">
                        
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 1</span>
                        <span class="step-text">Given Mengakses halaman Beranda pada aplikasi Rumah Pendidikan</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Navigated to beranda pada aplikasi rumah pendidikan</div>
                        
                    </div>
                </div>
                
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 2</span>
                        <span class="step-text">When Klik pada Search Bar</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Clicked search bar</div>
                        
                    </div>
                </div>
                
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 3</span>
                        <span class="step-text">And Ketik "Belajar"</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Typed 'belajar' in input_field</div>
                        
                    </div>
                </div>
                
                <div class="step failed">
                    <div class="step-header">
                        <span class="step-icon">❌</span>
                        <span class="step-number">Step 4</span>
                        <span class="step-text">Then Sistem menampilkan hasil pencarian dengan kata kunci "Belajar"</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: FAILED</div>
                        <div class="step-result">Result: Assertion failed for pencarian dengan kata kunci </div>
                        <div class="step-error">Error: Expected: pencarian dengan kata kunci , Actual: Cari</div>
                    </div>
                </div>
                
                <div class="step skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-number">Step 5</span>
                        <span class="step-text">And Berhasil searching</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: SKIPPED</div>
                        <div class="step-result">Result: Unknown or unsupported action for step: Berhasil searching</div>
                        <div class="step-error">Error: Unknown or unsupported action</div>
                    </div>
                </div>
                
                    </div>
                </div>
            </div>
            
            <div class="scenario-section">
                <div class="scenario-header" onclick="toggleScenario(this)">
                    <div class="scenario-info">
                        <span class="scenario-icon">❌</span>
                        <span class="scenario-title">TEST_RP-932</span>
                        <span class="scenario-status failed">FAILED</span>
                    </div>
                    <div class="scenario-meta">
                        <span class="execution-time">⏱️ 24.6s</span>
                        <span class="steps-summary">📊 2/4 passed</span>
                        <span class="toggle-arrow">▼</span>
                    </div>
                </div>
                <div class="scenario-content" style="display: none;">
                    <div class="steps-container">
                        
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 1</span>
                        <span class="step-text">Given the user is on the Ruang Bahasa page</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Navigated to ruang bahasa</div>
                        
                    </div>
                </div>
                
                <div class="step failed">
                    <div class="step-header">
                        <span class="step-icon">❌</span>
                        <span class="step-number">Step 2</span>
                        <span class="step-text">When the user sees the Layanan UKBI option</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: FAILED</div>
                        <div class="step-result">Result: Assertion failed for None</div>
                        <div class="step-error">Error: Expected: None, Actual: Assertion error: 'NoneType' object has no attribute 'lower'</div>
                    </div>
                </div>
                
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 3</span>
                        <span class="step-text">When I click "Layanan UKBI"</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Clicked layanan ukbi</div>
                        
                    </div>
                </div>
                
                <div class="step failed">
                    <div class="step-header">
                        <span class="step-icon">❌</span>
                        <span class="step-number">Step 4</span>
                        <span class="step-text">Then the user should be redirected to the Layanan UKBI page</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: FAILED</div>
                        <div class="step-result">Result: Assertion failed for the layanan ukbi page</div>
                        <div class="step-error">Error: Expected: the layanan ukbi page, Actual: Langsung ke konten utama
Rumah Pendidikan
Cari
Pusat Informasi
Masuk
Jelajahi Ruang di Rumah Pendidikan

Ruang GTK

Ruang Murid

Ruang Sekolah

Ruang Bahasa

Ruang Pemerintah

Ruang Mitra

Ruang Publik

Ruang Orang Tua

Semangat Rumah Pendidikan

Rumah Pendidikan berkomitmen untuk menjadi pusat ruang kolaborasi bagi siswa, guru, orang tua, hingga pemerintah daerah

Pelajari Selengkapnya
Layanan Unggulan di Rumah Pendidikan

Sumber Belajar

Portal pembelajaran digital interaktif untuk semua jenjang.

Buku Bacaan Digital

Pusat Buku Digital untuk Gerakan Literasi Nasional.

Pengelolaan Kinerja

Perencanaan, pelaksanaan, dan penilaian Anda untuk pengembangan diri dan satdik.

Diklat

Pelatihan terbimbing menggunakan LMS.

Pelatihan Mandiri

Materi untuk pengembangan kompetensi Guru dan Tendik.

Siapa saja yang sudah memanfaatkan kemudahan kolaborasi di Rumah Pendidikan?

Simak perjalanan mereka

"Rumah Pendidikan merupakan gagasan yang sangat menarik dan terdepan untuk perkembangan zaman terkini. Di mana pergeseran paradigma baru, pendidikan tidak hanya sekedar di dalam kelas tapi juga bisa di ruang keluarga dan masyarakat."

Arham, S.Pd., M.Pd

Kepala Sekolah SD INPRES 33 Birobuli, Palu

"Kami menyambut baik dengan adanya rumah pendidikan ini sebagai langkah strategis Kemendikdasmen terutama memperluas akses pendidikan dan pemerataan mutu pendidikan. Karena diharapkan dengan adanya Rumdik ini sebagai salah satu langkah pendidikan yang Responsif, Akuntabel, Melayani, Adaptif, dan Harmonis. Semoga menjadi langkah terbaik untuk Indonesia maju."

Dr. Firman Oktora, S.SI, M.PD.

Kabalaitekkomdik Jawa Barat

"Menurut saya Rumah Pendidikan sangat keren dan sangat membantu. Karena bagi saya murid kelas 6, Rumah Pendidikan bisa membantu dalam belajar dengan simpel, praktis, dan bisa mempelajari berbagai macam hal dalam satu platform, jadi sangat membantu."

Alena

Pelajar Gen KiHajar

Informasi Untuk Anda

Berita
Sambut Tahun Ajaran Baru 2025 dengan Penuh Inspirasi!
14 Juli 2025 | 10.32 WIB
Baca Selengkapnya
Berita
Laman Direktori Portal SPMB Berbasis SIAP SPMB Online
11 Juli 2025 | 15.13 WIB
Baca Selengkapnya
Berita
Tanya Jawab seputar Tes Kemampuan Akademik (ТKА)
24 Juni 2025 | 15.00 WIB
Baca Selengkapnya
Lihat lebih banyak
Pertanyaan yang paling sering ditanyakan
Apa manfaat menggunakan Rumah Pendidikan?
Bagaimana cara mengakses Rumah Pendidikan?
Apakah pengguna harus menggunakan Akun belajar.id untuk mengakses Rumah Pendidikan?
Siapa saja pengguna yang disarankan untuk menggunakan platform Rumah Pendidikan
Apa perbedaan rumah pendidikan dengan platform digital pendidikan milik Kemendikdasmen lainnya?
Ke Pusat Informasi

Kementerian Pendidikan Dasar dan Menengah

Kompleks Kementerian Pendidikan dan Kebudayaan, Senayan, Jakarta Pusat 10270

Navigasi

Pusat Bantuan Rumah Pendidikan

Syarat & Ketentuan

Kebijakan Privasi

Unduh Aplikasi Rumah Pendidikan</div>
                    </div>
                </div>
                
                    </div>
                </div>
            </div>
            
            <div class="scenario-section">
                <div class="scenario-header" onclick="toggleScenario(this)">
                    <div class="scenario-info">
                        <span class="scenario-icon">❌</span>
                        <span class="scenario-title">TEST_RP-98</span>
                        <span class="scenario-status failed">FAILED</span>
                    </div>
                    <div class="scenario-meta">
                        <span class="execution-time">⏱️ 54.1s</span>
                        <span class="steps-summary">📊 3/6 passed</span>
                        <span class="toggle-arrow">▼</span>
                    </div>
                </div>
                <div class="scenario-content" style="display: none;">
                    <div class="steps-container">
                        
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 1</span>
                        <span class="step-text">Given Mengakses halaman Beranda pada aplikasi Rumah Pendidikan</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Navigated to beranda pada aplikasi rumah pendidikan</div>
                        
                    </div>
                </div>
                
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 2</span>
                        <span class="step-text">When Klik "Ruang Sekolah"</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Clicked ruang sekolah</div>
                        
                    </div>
                </div>
                
                <div class="step failed">
                    <div class="step-header">
                        <span class="step-icon">❌</span>
                        <span class="step-number">Step 3</span>
                        <span class="step-text">And Menampilkan halaman "Ruang Sekolah"</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: FAILED</div>
                        <div class="step-result">Result: Assertion failed for halaman </div>
                        <div class="step-error">Error: Expected: halaman , Actual: Langsung ke konten utama
Rumah Pendidikan
Cari
Pusat Informasi
Masuk
Jelajahi Ruang di Rumah Pendidikan

Ruang GTK

Ruang Murid

Ruang Sekolah

Ruang Bahasa

Ruang Pemerintah

Ruang Mitra

Ruang Publi</div>
                    </div>
                </div>
                
                <div class="step passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-number">Step 4</span>
                        <span class="step-text">And Klik "Profil Sekolah" yang tidak tersedia</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: PASSED</div>
                        <div class="step-result">Result: Clicked profil sekolah</div>
                        
                    </div>
                </div>
                
                <div class="step failed">
                    <div class="step-header">
                        <span class="step-icon">❌</span>
                        <span class="step-number">Step 5</span>
                        <span class="step-text">Then Tidak diarahkan ke Webview "https://sekolah.data.kemendikdasmen.go.id/"</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: FAILED</div>
                        <div class="step-result">Result: Assertion failed for webview </div>
                        <div class="step-error">Error: Expected: webview , Actual: Langsung ke konten utama
Rumah Pendidikan
Cari
Pusat Informasi
Masuk
Jelajahi Ruang di Rumah Pendidikan

Ruang GTK

Ruang Murid

Ruang Sekolah

Ruang Bahasa

Ruang Pemerintah

Ruang Mitra

Ruang Publi</div>
                    </div>
                </div>
                
                <div class="step failed">
                    <div class="step-header">
                        <span class="step-icon">❌</span>
                        <span class="step-number">Step 6</span>
                        <span class="step-text">And Gagal Diarahkan ke halaman Webview Website Sekolah Kita</span>
                    </div>
                    <div class="step-details">
                        <div class="step-status">Status: FAILED</div>
                        <div class="step-result">Result: Assertion failed for halaman webview website sekolah kita</div>
                        <div class="step-error">Error: Expected: halaman webview website sekolah kita, Actual: Langsung ke konten utama
Rumah Pendidikan
Cari
Pusat Informasi
Masuk
Jelajahi Ruang di Rumah Pendidikan

Ruang GTK

Ruang Murid

Ruang Sekolah

Ruang Bahasa

Ruang Pemerintah

Ruang Mitra

Ruang Publik

Ruang Orang Tua

Semangat Rumah Pendidikan

Rumah Pendidikan berkomitmen untuk menjadi pusat ruang kolaborasi bagi siswa, guru, orang tua, hingga pemerintah daerah

Pelajari Selengkapnya
Layanan Unggulan di Rumah Pendidikan

Sumber Belajar

Portal pembelajaran digital interaktif untuk semua jenjang.

Buku Bacaan Digital

Pusat Buku Digital untuk Gerakan Literasi Nasional.

Pengelolaan Kinerja

Perencanaan, pelaksanaan, dan penilaian Anda untuk pengembangan diri dan satdik.

Diklat

Pelatihan terbimbing menggunakan LMS.

Pelatihan Mandiri

Materi untuk pengembangan kompetensi Guru dan Tendik.

Siapa saja yang sudah memanfaatkan kemudahan kolaborasi di Rumah Pendidikan?

Simak perjalanan mereka

"Rumah Pendidikan merupakan gagasan yang sangat menarik dan terdepan untuk perkembangan zaman terkini. Di mana pergeseran paradigma baru, pendidikan tidak hanya sekedar di dalam kelas tapi juga bisa di ruang keluarga dan masyarakat."

Arham, S.Pd., M.Pd

Kepala Sekolah SD INPRES 33 Birobuli, Palu

"Kami menyambut baik dengan adanya rumah pendidikan ini sebagai langkah strategis Kemendikdasmen terutama memperluas akses pendidikan dan pemerataan mutu pendidikan. Karena diharapkan dengan adanya Rumdik ini sebagai salah satu langkah pendidikan yang Responsif, Akuntabel, Melayani, Adaptif, dan Harmonis. Semoga menjadi langkah terbaik untuk Indonesia maju."

Dr. Firman Oktora, S.SI, M.PD.

Kabalaitekkomdik Jawa Barat

"Menurut saya Rumah Pendidikan sangat keren dan sangat membantu. Karena bagi saya murid kelas 6, Rumah Pendidikan bisa membantu dalam belajar dengan simpel, praktis, dan bisa mempelajari berbagai macam hal dalam satu platform, jadi sangat membantu."

Alena

Pelajar Gen KiHajar

Informasi Untuk Anda

Berita
Sambut Tahun Ajaran Baru 2025 dengan Penuh Inspirasi!
14 Juli 2025 | 10.32 WIB
Baca Selengkapnya
Berita
Laman Direktori Portal SPMB Berbasis SIAP SPMB Online
11 Juli 2025 | 15.13 WIB
Baca Selengkapnya
Berita
Tanya Jawab seputar Tes Kemampuan Akademik (ТKА)
24 Juni 2025 | 15.00 WIB
Baca Selengkapnya
Lihat lebih banyak
Pertanyaan yang paling sering ditanyakan
Apa manfaat menggunakan Rumah Pendidikan?
Bagaimana cara mengakses Rumah Pendidikan?
Apakah pengguna harus menggunakan Akun belajar.id untuk mengakses Rumah Pendidikan?
Siapa saja pengguna yang disarankan untuk menggunakan platform Rumah Pendidikan
Apa perbedaan rumah pendidikan dengan platform digital pendidikan milik Kemendikdasmen lainnya?
Ke Pusat Informasi

Kementerian Pendidikan Dasar dan Menengah

Kompleks Kementerian Pendidikan dan Kebudayaan, Senayan, Jakarta Pusat 10270

Navigasi

Pusat Bantuan Rumah Pendidikan

Syarat & Ketentuan

Kebijakan Privasi

Unduh Aplikasi Rumah Pendidikan</div>
                    </div>
                </div>
                
                    </div>
                </div>
            </div>
            
            </div>
        </div>
    </div>
    
    <!-- Tooltip container -->
    <div id="tooltip" class="tooltip" style="display: none;"></div>
    
    <script>
        // DONUT CHART TOOLTIPS - Show passed/failed counts on hover
        function handleDonutHover(event, passed, failed) {
            const tooltip = document.getElementById('tooltip');
            const rect = event.target.getBoundingClientRect();
            const x = rect.left + rect.width / 2;
            const y = rect.top;
            
            tooltip.innerHTML = `Passed: ${passed}<br>Failed: ${failed}`;
            tooltip.style.left = x + 'px';
            tooltip.style.top = y + 'px';
            tooltip.style.display = 'block';
        }
        
        function hideTooltip() {
            document.getElementById('tooltip').style.display = 'none';
        }
        
        // SCENARIOS CLOSED BY DEFAULT - Collapsible scenario sections
        function toggleScenario(header) {
            const content = header.nextElementSibling;
            const arrow = header.querySelector('.toggle-arrow');
            
            if (content.style.display === 'none' || content.style.display === '') {
                content.style.display = 'block';
                arrow.style.transform = 'rotate(180deg)';
            } else {
                content.style.display = 'none';
                arrow.style.transform = 'rotate(0deg)';
            }
        }
        
        // INTERACTIVE ERROR DETAILS - Expandable error details for failed steps
        function toggleErrorDetails(errorDetails) {
            const content = errorDetails.querySelector('.error-content');
            const toggleIcon = errorDetails.querySelector('.toggle-icon');
            
            if (content.style.display === 'none' || content.style.display === '') {
                content.style.display = 'block';
                toggleIcon.textContent = '▲';
            } else {
                content.style.display = 'none';
                toggleIcon.textContent = '▼';
            }
        }
        
        // Filter functionality
        function filterScenarios(status) {
            const scenarios = document.querySelectorAll('.scenario-section');
            const buttons = document.querySelectorAll('.filter-btn');
            
            // Update button states
            buttons.forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');
            
            scenarios.forEach(scenario => {
                const scenarioStatus = scenario.querySelector('.scenario-status').textContent.toLowerCase();
                
                if (status === 'all' || scenarioStatus === status) {
                    scenario.style.display = 'block';
                } else {
                    scenario.style.display = 'none';
                }
            });
        }
    </script>
</body>
</html>
        