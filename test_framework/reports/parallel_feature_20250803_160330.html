
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title><PERSON><PERSON>h Pendidikan Automation Report</title>
            <style>
                body {
                    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                    margin: 0;
                    padding: 20px;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    min-height: 100vh;
                }
                .container {
                    max-width: 1400px;
                    margin: 0 auto;
                    background: white;
                    border-radius: 15px;
                    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
                    overflow: hidden;
                }
                .header {
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    padding: 30px;
                    text-align: center;
                    position: relative;
                }
                .header h1 {
                    margin: 0;
                    font-size: 2.5em;
                    font-weight: 300;
                }
                .header h2 {
                    margin: 10px 0 0 0;
                    font-size: 1.2em;
                    font-weight: 300;
                    opacity: 0.9;
                }
                .back-button {
                    position: absolute;
                    left: 30px;
                    top: 50%;
                    transform: translateY(-50%);
                    background: rgba(255,255,255,0.2);
                    border: none;
                    color: white;
                    padding: 10px 20px;
                    border-radius: 25px;
                    cursor: pointer;
                    text-decoration: none;
                    font-size: 0.9em;
                    transition: background 0.3s;
                }
                .back-button:hover {
                    background: rgba(255,255,255,0.3);
                }
                .content {
                    padding: 30px;
                }
                
                /* Feature Details Section */
                .feature-section {
                    background: #f8f9fa;
                    border-radius: 10px;
                    padding: 25px;
                    margin-bottom: 30px;
                    border-top: 4px solid #dc3545;
                }
                .feature-tags {
                    margin-bottom: 15px;
                    max-height: 60px;
                    overflow-y: auto;
                    overflow-x: hidden;
                    padding: 5px;
                    border: 1px solid #e9ecef;
                    border-radius: 6px;
                    background: white;
                }
                .feature-tags::-webkit-scrollbar {
                    width: 6px;
                }
                .feature-tags::-webkit-scrollbar-track {
                    background: #f1f1f1;
                    border-radius: 3px;
                }
                .feature-tags::-webkit-scrollbar-thumb {
                    background: #c1c1c1;
                    border-radius: 3px;
                }
                .feature-tags::-webkit-scrollbar-thumb:hover {
                    background: #a8a8a8;
                }
                .feature-tags .tag {
                    display: inline-block;
                    background: #e9ecef;
                    color: #495057;
                    padding: 4px 8px;
                    border-radius: 4px;
                    font-size: 0.8em;
                    margin-right: 8px;
                    margin-bottom: 5px;
                    white-space: nowrap;
                }
                .feature-info {
                    display: flex;
                    justify-content: space-between;
                    align-items: flex-start;
                    gap: 20px;
                }
                .feature-details {
                    flex: 1;
                }
                .feature-details h2 {
                    margin: 0 0 10px 0;
                    color: #333;
                    font-size: 1.8em;
                }
                .feature-description {
                    margin: 0 0 15px 0;
                    color: #666;
                }
                .feature-info div {
                    margin: 5px 0;
                    color: #666;
                }
                .feature-info strong {
                    color: #333;
                }
                
                /* Scenarios and Metadata Grid */
                .scenarios-overview {
                    display: grid;
                    grid-template-columns: 2fr 1fr;
                    gap: 30px;
                    margin-bottom: 30px;
                }
                .scenarios-chart {
                    background: white;
                    border-radius: 10px;
                    padding: 25px;
                    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                }
                .scenarios-chart h3 {
                    margin: 0 0 20px 0;
                    color: #333;
                    font-size: 1.4em;
                }
                .scenarios-chart-wrapper {
                    position: relative;
                    width: 200px;
                    height: 200px;
                    margin: 0 auto 20px;
                }
                .donut-chart {
                    width: 200px;
                    height: 200px;
                    border-radius: 50%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-size: 2.5em;
                    font-weight: bold;
                    color: white;
                    position: relative;
                    background: conic-gradient(#28a745 0deg 360.0deg, #dc3545 360.0deg 360deg);
                }
                .donut-chart::before {
                    content: '';
                    position: absolute;
                    width: 120px;
                    height: 120px;
                    border-radius: 50%;
                    background: white;
                    z-index: 1;
                }
                .donut-chart::after {
                    content: '14';
                    position: absolute;
                    z-index: 2;
                    color: #333;
                    font-size: 2em;
                    font-weight: bold;
                }
                .scenarios-chart-details {
                    display: grid;
                    grid-template-columns: 1fr 1fr;
                    gap: 20px;
                }
                .chart-detail-section h4 {
                    font-size: 14px;
                    color: #6c757d;
                    margin-bottom: 8px;
                    text-transform: uppercase;
                    letter-spacing: 0.5px;
                }
                .status-list, .progress-list {
                    list-style: none;
                    padding: 0;
                    margin: 0;
                }
                .status-list li, .progress-list li {
                    display: flex;
                    align-items: center;
                    gap: 8px;
                    margin-bottom: 5px;
                    font-size: 0.9em;
                }
                .status-list .passed, .progress-list .passed {
                    color: #28a745;
                }
                .status-list .failed, .progress-list .failed {
                    color: #dc3545;
                }
                
                /* Metadata Panel */
                .metadata-panel {
                    background: white;
                    border-radius: 10px;
                    padding: 25px;
                    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                }
                .metadata-panel h3 {
                    margin: 0 0 20px 0;
                    color: #333;
                    font-size: 1.4em;
                }
                .metadata-grid {
                    display: flex;
                    flex-direction: column;
                    gap: 15px;
                }
                .metadata-item {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    padding: 10px 0;
                    border-bottom: 1px solid #e9ecef;
                }
                .metadata-item:last-child {
                    border-bottom: none;
                }
                .metadata-label {
                    font-weight: 500;
                    color: #666;
                }
                .metadata-value {
                    color: #333;
                    display: flex;
                    align-items: center;
                    gap: 8px;
                }
                .metadata-value i {
                    font-size: 1.2em;
                }
                
                /* Filter Section */
                .filter-section {
                    background: white;
                    border-radius: 10px;
                    padding: 20px;
                    margin-bottom: 30px;
                    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                }
                .filter-section h3 {
                    margin: 0 0 15px 0;
                    color: #333;
                }
                .filter-buttons {
                    display: flex;
                    gap: 10px;
                }
                .filter-button {
                    background: #e9ecef;
                    border: none;
                    padding: 8px 16px;
                    border-radius: 20px;
                    cursor: pointer;
                    font-size: 0.9em;
                    transition: all 0.3s;
                }
                .filter-button.active {
                    background: #667eea;
                    color: white;
                }
                .filter-button:hover {
                    background: #5a6fd8;
                    color: white;
                }
                
                /* Scenarios Section */
                .scenarios-section {
                    background: white;
                    border-radius: 10px;
                    padding: 25px;
                    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                }
                .scenarios-section h3 {
                    margin: 0 0 20px 0;
                    color: #333;
                    font-size: 1.4em;
                }
                .scenario-item {
                    border: 1px solid #e9ecef;
                    border-radius: 8px;
                    margin-bottom: 15px;
                    overflow: hidden;
                }
                .scenario-header {
                    background: #f8f9fa;
                    padding: 15px 20px;
                    cursor: pointer;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    transition: background 0.3s;
                }
                .scenario-header:hover {
                    background: #e9ecef;
                }
                .scenario-title {
                    display: flex;
                    align-items: center;
                    gap: 10px;
                    flex: 1;
                    min-width: 0;
                }
                .status-icon {
                    font-size: 1.2em;
                    flex-shrink: 0;
                }
                .scenario-name {
                    font-weight: 500;
                    color: #333;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                }
                .scenario-meta {
                    display: flex;
                    align-items: center;
                    gap: 15px;
                    flex-shrink: 0;
                    white-space: nowrap;
                }
                .execution-time {
                    color: #666;
                    font-size: 0.9em;
                }
                .arrow {
                    font-size: 0.8em;
                    color: #666;
                    transition: transform 0.3s;
                    flex-shrink: 0;
                    white-space: nowrap;
                }
                .scenario-content {
                    padding: 25px;
                    background: white;
                    border-radius: 0 0 8px 8px;
                }
                .steps-container {
                    display: flex;
                    flex-direction: column;
                    gap: 15px;
                    max-width: 100%;
                    overflow-x: hidden;
                }
                .step-item {
                    background: #f8f9fa;
                    border-radius: 8px;
                    padding: 25px;
                    border-left: 4px solid #28a745;
                    margin-bottom: 20px;
                    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
                    overflow-wrap: break-word;
                    word-wrap: break-word;
                }
                .step-item.failed {
                    border-left-color: #dc3545;
                }
                .step-item.skipped {
                    border-left-color: #ffc107;
                }
                .step-header {
                    display: flex;
                    align-items: flex-start;
                    gap: 15px;
                    margin-bottom: 15px;
                    flex-wrap: wrap;
                }
                .step-icon {
                    font-size: 1.2em;
                    flex-shrink: 0;
                    margin-top: 3px;
                    width: 20px;
                    text-align: center;
                }
                .step-text {
                    flex: 1;
                    font-weight: 500;
                    color: #333;
                    line-height: 1.6;
                    word-wrap: break-word;
                    overflow-wrap: break-word;
                    hyphens: auto;
                    font-size: 0.95em;
                    margin: 0;
                    padding: 0;
                    min-width: 0;
                    max-width: 100%;
                    text-align: justify;
                    text-justify: inter-word;
                    white-space: normal;
                    word-break: normal;
                    overflow-wrap: anywhere;
                }
                .step-time {
                    color: #666;
                    font-size: 0.85em;
                    flex-shrink: 0;
                    white-space: normal;
                    background: #e9ecef;
                    padding: 6px 10px;
                    border-radius: 4px;
                    font-weight: 500;
                    margin-left: 10px;
                    max-width: 200px;
                    word-wrap: break-word;
                    overflow-wrap: break-word;
                }
                .step-execution {
                    background: #e3f2fd;
                    color: #1976d2;
                    padding: 6px 10px;
                    border-radius: 4px;
                    font-size: 0.8em;
                    font-weight: 500;
                    display: inline-block;
                    border: 1px solid #bbdefb;
                    margin-top: 8px;
                    margin-left: 32px;
                }
                .error-details {
                    margin-top: 10px;
                    background: #fff5f5;
                    border: 1px solid #fed7d7;
                    border-radius: 6px;
                    overflow: hidden;
                }
                .error-header {
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    padding: 10px 15px;
                    background: #fed7d7;
                    cursor: pointer;
                }
                .error-icon {
                    margin-right: 8px;
                }
                .error-text {
                    flex: 1;
                    font-weight: 500;
                    color: #c53030;
                }
                .quick-show-btn {
                    background: #c53030;
                    color: white;
                    border: none;
                    padding: 4px 8px;
                    border-radius: 4px;
                    font-size: 0.7em;
                    cursor: pointer;
                }
                .quick-show-btn:hover {
                    background: #a02323;
                }
                .error-content {
                    padding: 15px;
                }
                .error-comparison {
                    display: flex;
                    flex-direction: column;
                    gap: 10px;
                }
                .expected-actual {
                    display: grid;
                    grid-template-columns: 1fr 1fr;
                    gap: 20px;
                }
                .expected, .actual {
                    padding: 10px;
                    border-radius: 4px;
                }
                .expected {
                    background: #f0fff4;
                    border: 1px solid #9ae6b4;
                }
                .actual {
                    background: #fff5f5;
                    border: 1px solid #feb2b2;
                }
                .expected strong, .actual strong {
                    display: block;
                    margin-bottom: 5px;
                    font-size: 0.9em;
                }
                .expected .value, .actual .value {
                    font-family: monospace;
                    background: rgba(0,0,0,0.05);
                    padding: 5px;
                    border-radius: 3px;
                    word-break: break-all;
                }
                .footer {
                    background: #f8f9fa;
                    padding: 20px;
                    text-align: center;
                    color: #666;
                    border-top: 1px solid #e9ecef;
                }
                
                /* Tooltip Styles */
                .passed-tooltip, .failed-tooltip {
                    position: fixed;
                    background: rgba(0,0,0,0.9);
                    color: white;
                    padding: 6px 10px;
                    border-radius: 4px;
                    font-size: 0.7em;
                    font-weight: 500;
                    white-space: nowrap;
                    pointer-events: none;
                    opacity: 0;
                    transition: opacity 0.2s ease;
                    z-index: 1000;
                    box-shadow: 0 3px 8px rgba(0,0,0,0.3);
                    backdrop-filter: blur(4px);
                }
                .passed-tooltip {
                    border-left: 3px solid #28a745;
                }
                .failed-tooltip {
                    border-left: 3px solid #dc3545;
                }
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <a href="parallel_comprehensive_20250803_160330.html" class="back-button">
                        <i class="fas fa-arrow-left"></i> Summary
                    </a>
                    <h1>Rumah Pendidikan Automation Report</h1>
                    <h2>Feature: Test Execution</h2>
                </div>
                
                <div class="content">
                    <!-- Feature Details Section -->
                    <div class="feature-section">
                        <div class="feature-tags">
                            <a href="#TC1" class="tag">TC1</a><a href="#TC10" class="tag">TC10</a><a href="#TC11" class="tag">TC11</a><a href="#TC12" class="tag">TC12</a><a href="#TC13" class="tag">TC13</a><a href="#TC14" class="tag">TC14</a><a href="#TC2" class="tag">TC2</a><a href="#TC3" class="tag">TC3</a><a href="#TC4" class="tag">TC4</a><a href="#TC5" class="tag">TC5</a><a href="#TC6" class="tag">TC6</a><a href="#TC7" class="tag">TC7</a><a href="#TC8" class="tag">TC8</a><a href="#TC9" class="tag">TC9</a>
                        </div>
                        <div class="feature-info">
                            <div class="feature-details">
                                <h2>Feature: Test Execution</h2>
                                <p class="feature-description"><strong>Description:</strong> Regression Test</p>
                                <div><strong>File name:</strong> rumdik_regression_test.feature</div>
                            </div>
                            <div class="metadata">
                                <div><strong>Relative path:</strong> test_framework/features/rumdik_regression_test.feature</div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Scenarios and Metadata Grid -->
                    <div class="scenarios-overview">
                        <div class="scenarios-chart">
                            <h3>Scenarios</h3>
                            <div class="scenarios-chart-wrapper">
                                <div class="donut-chart" onmouseover="handleDonutHover(event, 14, 0)" onmouseout="hideTooltip()"></div>
                            </div>
                            <div class="scenarios-chart-details">
                                <div class="chart-detail-section">
                                    <h4>Status</h4>
                                    <ul class="status-list">
                                        <li class="passed">✅ Passed</li>
                                        <li class="failed">❌ Failed</li>
                                    </ul>
                                </div>
                                <div class="chart-detail-section">
                                    <h4>Progress</h4>
                                    <ul class="progress-list">
                                        <li class="passed">✅ 100.0%</li>
                                        <li class="failed">❌ 0.0%</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        
                        <div class="metadata-panel">
                            <h3>Metadata</h3>
                            <div class="metadata-grid">
                                <div class="metadata-item">
                                    <div class="metadata-label">Device</div>
                                    <div class="metadata-value">Runner Machine</div>
                                </div>
                                <div class="metadata-item">
                                    <div class="metadata-label">OS</div>
                                    <div class="metadata-value">
                                        <i class="fab fa-apple"></i>
                                        macOS
                                    </div>
                                </div>
                                <div class="metadata-item">
                                    <div class="metadata-label">Browser</div>
                                    <div class="metadata-value">
                                        <i class="fab fa-chrome"></i>
                                        Chrome 103
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Tooltip Elements -->
                    <div id="passed-tooltip" class="passed-tooltip"></div>
                    <div id="failed-tooltip" class="failed-tooltip"></div>
                    
                    <!-- Filter Section -->
                    <div class="filter-section">
                        <h3>Filter Scenarios</h3>
                        <div class="filter-buttons">
                            <button class="filter-button active" onclick="filterScenarios('all')">All (14)</button>
                            <button class="filter-button" onclick="filterScenarios('passed')">Passed (14)</button>
                            <button class="filter-button" onclick="filterScenarios('failed')">Failed (0)</button>
                        </div>
                    </div>
                    
                    <!-- Scenarios Section -->
                    <div class="scenarios-section">
                        <h3>Scenarios</h3>
                        
            <div class="scenario-item" data-status="passed">
                <div class="scenario-header" onclick="toggleScenario('TC1')">
                    <div class="scenario-title">
                        <span class="status-icon">✅</span>
                        <span class="scenario-name">Test Scenario: [TC1]</span>
                    </div>
                    <div class="scenario-meta">
                        <span class="execution-time">(0.4s)</span>
                        <span class="arrow">▼</span>
                    </div>
                </div>
                <div class="scenario-content" id="scenario-TC1" style="display: none;">
                    <div class="steps-container">
                        
                <div class="step-item passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-text">Given I am on the main page</span>
                        <span class="step-time">(0.01s)</span>
                    </div>
                    <div class="step-execution">Step 1 execution</div>
                    
                </div>
                
                <div class="step-item skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-text">When I type "Pelatihan" in the search field</span>
                        <span class="step-time">(0.01s)</span>
                    </div>
                    <div class="step-execution">Step 2 execution</div>
                    
                </div>
                
                <div class="step-item skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-text">And I click the "Cari" button</span>
                        <span class="step-time">(0.01s)</span>
                    </div>
                    <div class="step-execution">Step 3 execution</div>
                    
                </div>
                
                <div class="step-item skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-text">Then I should see "Pelatihan"</span>
                        <span class="step-time">(0.01s)</span>
                    </div>
                    <div class="step-execution">Step 4 execution</div>
                    
                </div>
                
                    </div>
                </div>
            </div>
            
            <div class="scenario-item" data-status="passed">
                <div class="scenario-header" onclick="toggleScenario('TC10')">
                    <div class="scenario-title">
                        <span class="status-icon">✅</span>
                        <span class="scenario-name">Test Scenario: [TC10]</span>
                    </div>
                    <div class="scenario-meta">
                        <span class="execution-time">(0.4s)</span>
                        <span class="arrow">▼</span>
                    </div>
                </div>
                <div class="scenario-content" id="scenario-TC10" style="display: none;">
                    <div class="steps-container">
                        
                <div class="step-item passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-text">Given I am on the main page</span>
                        <span class="step-time">(0.01s)</span>
                    </div>
                    <div class="step-execution">Step 1 execution</div>
                    
                </div>
                
                <div class="step-item skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-text">When I scroll to the "Ruang Orang Tua" section</span>
                        <span class="step-time">(0.01s)</span>
                    </div>
                    <div class="step-execution">Step 2 execution</div>
                    
                </div>
                
                <div class="step-item skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-text">Then I should see "Ruang Orang Tua"</span>
                        <span class="step-time">(0.01s)</span>
                    </div>
                    <div class="step-execution">Step 3 execution</div>
                    
                </div>
                
                <div class="step-item skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-text">And I click "Ruang Orang Tua"</span>
                        <span class="step-time">(0.01s)</span>
                    </div>
                    <div class="step-execution">Step 4 execution</div>
                    
                </div>
                
                <div class="step-item skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-text">Then I should see "Layanan yang Tersedia"</span>
                        <span class="step-time">(0.01s)</span>
                    </div>
                    <div class="step-execution">Step 5 execution</div>
                    
                </div>
                
                <div class="step-item skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-text">Then I should see "Layanan Informasi dan Pengaduan"</span>
                        <span class="step-time">(0.01s)</span>
                    </div>
                    <div class="step-execution">Step 6 execution</div>
                    
                </div>
                
                <div class="step-item skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-text">Then I should see "Panduan Pendampingan"</span>
                        <span class="step-time">(0.01s)</span>
                    </div>
                    <div class="step-execution">Step 7 execution</div>
                    
                </div>
                
                <div class="step-item skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-text">Then I should see "Konsultasi Pendidikan"</span>
                        <span class="step-time">(0.01s)</span>
                    </div>
                    <div class="step-execution">Step 8 execution</div>
                    
                </div>
                
                    </div>
                </div>
            </div>
            
            <div class="scenario-item" data-status="passed">
                <div class="scenario-header" onclick="toggleScenario('TC11')">
                    <div class="scenario-title">
                        <span class="status-icon">✅</span>
                        <span class="scenario-name">Test Scenario: [TC11]</span>
                    </div>
                    <div class="scenario-meta">
                        <span class="execution-time">(0.4s)</span>
                        <span class="arrow">▼</span>
                    </div>
                </div>
                <div class="scenario-content" id="scenario-TC11" style="display: none;">
                    <div class="steps-container">
                        
                <div class="step-item passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-text">Given I am on the main page</span>
                        <span class="step-time">(0.01s)</span>
                    </div>
                    <div class="step-execution">Step 1 execution</div>
                    
                </div>
                
                <div class="step-item skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-text">When I scroll to the "Semangat Rumah Pendidikan" section</span>
                        <span class="step-time">(0.01s)</span>
                    </div>
                    <div class="step-execution">Step 2 execution</div>
                    
                </div>
                
                <div class="step-item skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-text">Then I should see "Semangat Rumah Pendidikan"</span>
                        <span class="step-time">(0.01s)</span>
                    </div>
                    <div class="step-execution">Step 3 execution</div>
                    
                </div>
                
                <div class="step-item skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-text">Then I should see "Pelajari Selengkapnya"</span>
                        <span class="step-time">(0.01s)</span>
                    </div>
                    <div class="step-execution">Step 4 execution</div>
                    
                </div>
                
                <div class="step-item skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-text">And I click "Pelajari Selengkapnya"</span>
                        <span class="step-time">(0.01s)</span>
                    </div>
                    <div class="step-execution">Step 5 execution</div>
                    
                </div>
                
                <div class="step-item skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-text">When I scroll to the "Mengenal Rumah Pendidikan" section</span>
                        <span class="step-time">(0.01s)</span>
                    </div>
                    <div class="step-execution">Step 6 execution</div>
                    
                </div>
                
                <div class="step-item skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-text">Then I should see "Mengenal Rumah Pendidikan"</span>
                        <span class="step-time">(0.01s)</span>
                    </div>
                    <div class="step-execution">Step 7 execution</div>
                    
                </div>
                
                    </div>
                </div>
            </div>
            
            <div class="scenario-item" data-status="passed">
                <div class="scenario-header" onclick="toggleScenario('TC12')">
                    <div class="scenario-title">
                        <span class="status-icon">✅</span>
                        <span class="scenario-name">Test Scenario: [TC12]</span>
                    </div>
                    <div class="scenario-meta">
                        <span class="execution-time">(0.4s)</span>
                        <span class="arrow">▼</span>
                    </div>
                </div>
                <div class="scenario-content" id="scenario-TC12" style="display: none;">
                    <div class="steps-container">
                        
                <div class="step-item passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-text">Given I am on the main page</span>
                        <span class="step-time">(0.01s)</span>
                    </div>
                    <div class="step-execution">Step 1 execution</div>
                    
                </div>
                
                <div class="step-item skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-text">When I scroll to the "Informasi Untuk Anda" section</span>
                        <span class="step-time">(0.01s)</span>
                    </div>
                    <div class="step-execution">Step 2 execution</div>
                    
                </div>
                
                <div class="step-item skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-text">Then I should see "Informasi Untuk Anda"</span>
                        <span class="step-time">(0.01s)</span>
                    </div>
                    <div class="step-execution">Step 3 execution</div>
                    
                </div>
                
                <div class="step-item skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-text">And I click "Lihat lebih banyak"</span>
                        <span class="step-time">(0.01s)</span>
                    </div>
                    <div class="step-execution">Step 4 execution</div>
                    
                </div>
                
                <div class="step-item skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-text">Then I should see "Informasi Untuk Anda"</span>
                        <span class="step-time">(0.01s)</span>
                    </div>
                    <div class="step-execution">Step 5 execution</div>
                    
                </div>
                
                <div class="step-item skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-text">And I click "Artikel"</span>
                        <span class="step-time">(0.01s)</span>
                    </div>
                    <div class="step-execution">Step 6 execution</div>
                    
                </div>
                
                    </div>
                </div>
            </div>
            
            <div class="scenario-item" data-status="passed">
                <div class="scenario-header" onclick="toggleScenario('TC13')">
                    <div class="scenario-title">
                        <span class="status-icon">✅</span>
                        <span class="scenario-name">Test Scenario: [TC13]</span>
                    </div>
                    <div class="scenario-meta">
                        <span class="execution-time">(0.4s)</span>
                        <span class="arrow">▼</span>
                    </div>
                </div>
                <div class="scenario-content" id="scenario-TC13" style="display: none;">
                    <div class="steps-container">
                        
                <div class="step-item passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-text">Given I am on the main page</span>
                        <span class="step-time">(0.01s)</span>
                    </div>
                    <div class="step-execution">Step 1 execution</div>
                    
                </div>
                
                <div class="step-item skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-text">When I scroll to the "Pertanyaan yang paling sering ditanyakan" section</span>
                        <span class="step-time">(0.01s)</span>
                    </div>
                    <div class="step-execution">Step 2 execution</div>
                    
                </div>
                
                <div class="step-item skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-text">And I click button beside "Apa manfaat menggunakan Rumah Pendidikan?"</span>
                        <span class="step-time">(0.01s)</span>
                    </div>
                    <div class="step-execution">Step 3 execution</div>
                    
                </div>
                
                <div class="step-item skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-text">Then I should see "Pengguna dapat menemukan berbagai layanan digital pendidikan di ekosistem Kemendikdasmen dalam satu platform, tanpa perlu berpindah ke berbagai situs atau aplikasi."</span>
                        <span class="step-time">(0.01s)</span>
                    </div>
                    <div class="step-execution">Step 4 execution</div>
                    
                </div>
                
                <div class="step-item skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-text">And I click button beside "Bagaimana cara mengakses Rumah Pendidikan?"</span>
                        <span class="step-time">(0.01s)</span>
                    </div>
                    <div class="step-execution">Step 5 execution</div>
                    
                </div>
                
                <div class="step-item skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-text">Then I should see "Rumah Pendidikan dapat diakses dengan beberapa cara:"</span>
                        <span class="step-time">(0.01s)</span>
                    </div>
                    <div class="step-execution">Step 6 execution</div>
                    
                </div>
                
                <div class="step-item skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-text">And I click button beside "Apakah pengguna harus menggunakan Akun belajar.id untuk mengakses Rumah Pendidikan?"</span>
                        <span class="step-time">(0.01s)</span>
                    </div>
                    <div class="step-execution">Step 7 execution</div>
                    
                </div>
                
                <div class="step-item skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-text">Then I should see "Tidak, Rumah Pendidikan dapat diakses oleh masyarakat umum baik dengan atau tanpa login. Namun, untuk beberapa layanan yang ada di dalam Rumah Pendidikan membutuhkan akses login menggunakan Akun belajar.id. Sehingga, apabila Anda memiliki Akun belajar.id, direkomendasikan untuk login menggunakan akun tersebut untuk memudahkan akses ke berbagai layanan di dalam Rumah Pendidikan."</span>
                        <span class="step-time">(0.01s)</span>
                    </div>
                    <div class="step-execution">Step 8 execution</div>
                    
                </div>
                
                <div class="step-item skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-text">And I click button beside "Siapa saja pengguna yang disarankan untuk menggunakan platform Rumah Pendidikan"</span>
                        <span class="step-time">(0.01s)</span>
                    </div>
                    <div class="step-execution">Step 9 execution</div>
                    
                </div>
                
                <div class="step-item skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-text">Then I should see "Rumah Pendidikan dapat digunakan oleh seluruh lapisan masyarakat, termasuk guru, kepala sekolah, murid, tenaga kependidikan lainnya, orang tua, pemerintah daerah, mitra pendidikan, dan masyarakat umum, sesuai dengan kebutuhan masing-masing setiap pengguna."</span>
                        <span class="step-time">(0.01s)</span>
                    </div>
                    <div class="step-execution">Step 10 execution</div>
                    
                </div>
                
                <div class="step-item skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-text">And I click button beside "Apa perbedaan rumah pendidikan dengan platform digital pendidikan milik Kemendikdasmen lainnya?"</span>
                        <span class="step-time">(0.01s)</span>
                    </div>
                    <div class="step-execution">Step 11 execution</div>
                    
                </div>
                
                <div class="step-item skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-text">Then I should see "Rumah Pendidikan berbeda dari platform digital pendidikan lain karena berfungsi sebagai portal utama yang mengintegrasikan berbagai layanan pendidikan dalam ekosistem Kemendikdasmen. Pengguna tidak hanya menemukan satu layanan, tetapi berbagai fitur yang mendukung ekosistem pendidikan secara komprehensif."</span>
                        <span class="step-time">(0.01s)</span>
                    </div>
                    <div class="step-execution">Step 12 execution</div>
                    
                </div>
                
                <div class="step-item skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-text">And I click button "Ke Pusat Informasi"</span>
                        <span class="step-time">(0.01s)</span>
                    </div>
                    <div class="step-execution">Step 13 execution</div>
                    
                </div>
                
                <div class="step-item skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-text">Then user direct to page "https://pusatinformasi.rumahpendidikan.kemendikdasmen.go.id/hc/id"</span>
                        <span class="step-time">(0.01s)</span>
                    </div>
                    <div class="step-execution">Step 14 execution</div>
                    
                </div>
                
                    </div>
                </div>
            </div>
            
            <div class="scenario-item" data-status="passed">
                <div class="scenario-header" onclick="toggleScenario('TC14')">
                    <div class="scenario-title">
                        <span class="status-icon">✅</span>
                        <span class="scenario-name">Test Scenario: [TC14]</span>
                    </div>
                    <div class="scenario-meta">
                        <span class="execution-time">(0.4s)</span>
                        <span class="arrow">▼</span>
                    </div>
                </div>
                <div class="scenario-content" id="scenario-TC14" style="display: none;">
                    <div class="steps-container">
                        
                <div class="step-item passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-text">Given I am on the main page</span>
                        <span class="step-time">(0.01s)</span>
                    </div>
                    <div class="step-execution">Step 1 execution</div>
                    
                </div>
                
                <div class="step-item skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-text">When I scroll to the "Ke Pusat Informasi" section</span>
                        <span class="step-time">(0.01s)</span>
                    </div>
                    <div class="step-execution">Step 2 execution</div>
                    
                </div>
                
                <div class="step-item skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-text">Then I should see "Kementerian Pendidikan Dasar dan Menengah"</span>
                        <span class="step-time">(0.01s)</span>
                    </div>
                    <div class="step-execution">Step 3 execution</div>
                    
                </div>
                
                <div class="step-item skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-text">Then I should see "Kompleks Kementerian Pendidikan dan Kebudayaan, Senayan, Jakarta Pusat 10270"</span>
                        <span class="step-time">(0.01s)</span>
                    </div>
                    <div class="step-execution">Step 4 execution</div>
                    
                </div>
                
                <div class="step-item skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-text">Then I should see "Pusat Bantuan Rumah Pendidikan"</span>
                        <span class="step-time">(0.01s)</span>
                    </div>
                    <div class="step-execution">Step 5 execution</div>
                    
                </div>
                
                <div class="step-item skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-text">Then I should see "Syarat & Ketentuan"</span>
                        <span class="step-time">(0.01s)</span>
                    </div>
                    <div class="step-execution">Step 6 execution</div>
                    
                </div>
                
                <div class="step-item skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-text">Then I should see "Kebijakan Privasii"</span>
                        <span class="step-time">(0.01s)</span>
                    </div>
                    <div class="step-execution">Step 7 execution</div>
                    
                </div>
                
                <div class="step-item skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-text">And I click "Kebijakan Privasi"</span>
                        <span class="step-time">(0.01s)</span>
                    </div>
                    <div class="step-execution">Step 8 execution</div>
                    
                </div>
                
                <div class="step-item skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-text">And move to the previous page</span>
                        <span class="step-time">(0.01s)</span>
                    </div>
                    <div class="step-execution">Step 9 execution</div>
                    
                </div>
                
                <div class="step-item skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-text">And I click "Syarat & Ketentuan"</span>
                        <span class="step-time">(0.01s)</span>
                    </div>
                    <div class="step-execution">Step 10 execution</div>
                    
                </div>
                
                <div class="step-item skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-text">And move to the previous page</span>
                        <span class="step-time">(0.01s)</span>
                    </div>
                    <div class="step-execution">Step 11 execution</div>
                    
                </div>
                
                <div class="step-item skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-text">And I click "Pusat Bantuan Rumah Pendidikan"</span>
                        <span class="step-time">(0.01s)</span>
                    </div>
                    <div class="step-execution">Step 12 execution</div>
                    
                </div>
                
                <div class="step-item skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-text">And move to the previous page</span>
                        <span class="step-time">(0.01s)</span>
                    </div>
                    <div class="step-execution">Step 13 execution</div>
                    
                </div>
                
                    </div>
                </div>
            </div>
            
            <div class="scenario-item" data-status="passed">
                <div class="scenario-header" onclick="toggleScenario('TC2')">
                    <div class="scenario-title">
                        <span class="status-icon">✅</span>
                        <span class="scenario-name">Test Scenario: [TC2]</span>
                    </div>
                    <div class="scenario-meta">
                        <span class="execution-time">(0.4s)</span>
                        <span class="arrow">▼</span>
                    </div>
                </div>
                <div class="scenario-content" id="scenario-TC2" style="display: none;">
                    <div class="steps-container">
                        
                <div class="step-item passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-text">Given I am on the main page</span>
                        <span class="step-time">(0.01s)</span>
                    </div>
                    <div class="step-execution">Step 1 execution</div>
                    
                </div>
                
                <div class="step-item skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-text">When I scroll to the "Jelajahi Ruang di Rumah Pendidikan" section</span>
                        <span class="step-time">(0.01s)</span>
                    </div>
                    <div class="step-execution">Step 2 execution</div>
                    
                </div>
                
                <div class="step-item skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-text">Then I should see "Jelajahi Ruang di Rumah Pendidikan"</span>
                        <span class="step-time">(0.01s)</span>
                    </div>
                    <div class="step-execution">Step 3 execution</div>
                    
                </div>
                
                <div class="step-item skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-text">Then I should see "Ruang GTK"</span>
                        <span class="step-time">(0.01s)</span>
                    </div>
                    <div class="step-execution">Step 4 execution</div>
                    
                </div>
                
                <div class="step-item skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-text">Then I should see "Ruang Murid"</span>
                        <span class="step-time">(0.01s)</span>
                    </div>
                    <div class="step-execution">Step 5 execution</div>
                    
                </div>
                
                <div class="step-item skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-text">Then I should see "Ruang Sekolah"</span>
                        <span class="step-time">(0.01s)</span>
                    </div>
                    <div class="step-execution">Step 6 execution</div>
                    
                </div>
                
                <div class="step-item skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-text">Then I should see "Ruang Bahasa"</span>
                        <span class="step-time">(0.01s)</span>
                    </div>
                    <div class="step-execution">Step 7 execution</div>
                    
                </div>
                
                <div class="step-item skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-text">When I scroll to the "Ruang Pemerintah" section</span>
                        <span class="step-time">(0.01s)</span>
                    </div>
                    <div class="step-execution">Step 8 execution</div>
                    
                </div>
                
                <div class="step-item skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-text">Then I should see "Ruang Pemerintah"</span>
                        <span class="step-time">(0.01s)</span>
                    </div>
                    <div class="step-execution">Step 9 execution</div>
                    
                </div>
                
                <div class="step-item skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-text">Then I should see "Ruang Mitra"</span>
                        <span class="step-time">(0.01s)</span>
                    </div>
                    <div class="step-execution">Step 10 execution</div>
                    
                </div>
                
                <div class="step-item skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-text">Then I should see "Ruang Publik"</span>
                        <span class="step-time">(0.01s)</span>
                    </div>
                    <div class="step-execution">Step 11 execution</div>
                    
                </div>
                
                <div class="step-item skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-text">Then I should see "Ruang Orang Tua"</span>
                        <span class="step-time">(0.01s)</span>
                    </div>
                    <div class="step-execution">Step 12 execution</div>
                    
                </div>
                
                    </div>
                </div>
            </div>
            
            <div class="scenario-item" data-status="passed">
                <div class="scenario-header" onclick="toggleScenario('TC3')">
                    <div class="scenario-title">
                        <span class="status-icon">✅</span>
                        <span class="scenario-name">Test Scenario: [TC3]</span>
                    </div>
                    <div class="scenario-meta">
                        <span class="execution-time">(0.4s)</span>
                        <span class="arrow">▼</span>
                    </div>
                </div>
                <div class="scenario-content" id="scenario-TC3" style="display: none;">
                    <div class="steps-container">
                        
                <div class="step-item passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-text">Given I am on the main page</span>
                        <span class="step-time">(0.01s)</span>
                    </div>
                    <div class="step-execution">Step 1 execution</div>
                    
                </div>
                
                <div class="step-item skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-text">When I scroll to the "Ruang GTK" section</span>
                        <span class="step-time">(0.01s)</span>
                    </div>
                    <div class="step-execution">Step 2 execution</div>
                    
                </div>
                
                <div class="step-item skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-text">Then I should see "Ruang GTK"</span>
                        <span class="step-time">(0.01s)</span>
                    </div>
                    <div class="step-execution">Step 3 execution</div>
                    
                </div>
                
                <div class="step-item skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-text">And I click "Ruang GTK"</span>
                        <span class="step-time">(0.01s)</span>
                    </div>
                    <div class="step-execution">Step 4 execution</div>
                    
                </div>
                
                <div class="step-item skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-text">Then I should see "Layanan yang Tersedia"</span>
                        <span class="step-time">(0.01s)</span>
                    </div>
                    <div class="step-execution">Step 5 execution</div>
                    
                </div>
                
                <div class="step-item skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-text">Then I should see "Diklat"</span>
                        <span class="step-time">(0.01s)</span>
                    </div>
                    <div class="step-execution">Step 6 execution</div>
                    
                </div>
                
                <div class="step-item skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-text">Then I should see "Sertifikasi Pendidik"</span>
                        <span class="step-time">(0.01s)</span>
                    </div>
                    <div class="step-execution">Step 7 execution</div>
                    
                </div>
                
                <div class="step-item skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-text">Then I should see "Pelatihan Mandiri"</span>
                        <span class="step-time">(0.01s)</span>
                    </div>
                    <div class="step-execution">Step 8 execution</div>
                    
                </div>
                
                <div class="step-item skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-text">Then I should see "Komunitas"</span>
                        <span class="step-time">(0.01s)</span>
                    </div>
                    <div class="step-execution">Step 9 execution</div>
                    
                </div>
                
                <div class="step-item skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-text">When I scroll to the "Karir dan Kinerja" section</span>
                        <span class="step-time">(0.01s)</span>
                    </div>
                    <div class="step-execution">Step 10 execution</div>
                    
                </div>
                
                <div class="step-item skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-text">Then I should see "Karir dan Kinerja"</span>
                        <span class="step-time">(0.01s)</span>
                    </div>
                    <div class="step-execution">Step 11 execution</div>
                    
                </div>
                
                <div class="step-item skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-text">Then I should see "Pengelolaan Kinerja"</span>
                        <span class="step-time">(0.01s)</span>
                    </div>
                    <div class="step-execution">Step 12 execution</div>
                    
                </div>
                
                <div class="step-item skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-text">Then I should see "Seleksi Kepala Sekolah"</span>
                        <span class="step-time">(0.01s)</span>
                    </div>
                    <div class="step-execution">Step 13 execution</div>
                    
                </div>
                
                <div class="step-item skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-text">Then I should see "Refleksi Kompetensi"</span>
                        <span class="step-time">(0.01s)</span>
                    </div>
                    <div class="step-execution">Step 14 execution</div>
                    
                </div>
                
                <div class="step-item skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-text">When I scroll to the "Inspirasi Pembelajaran" section</span>
                        <span class="step-time">(0.01s)</span>
                    </div>
                    <div class="step-execution">Step 15 execution</div>
                    
                </div>
                
                <div class="step-item skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-text">Then I should see "Inspirasi Pembelajaran"</span>
                        <span class="step-time">(0.01s)</span>
                    </div>
                    <div class="step-execution">Step 16 execution</div>
                    
                </div>
                
                <div class="step-item skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-text">Then I should see "Perangkat Ajar"</span>
                        <span class="step-time">(0.01s)</span>
                    </div>
                    <div class="step-execution">Step 17 execution</div>
                    
                </div>
                
                <div class="step-item skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-text">Then I should see "CP/ATP"</span>
                        <span class="step-time">(0.01s)</span>
                    </div>
                    <div class="step-execution">Step 18 execution</div>
                    
                </div>
                
                <div class="step-item skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-text">Then I should see "Ide Praktik"</span>
                        <span class="step-time">(0.01s)</span>
                    </div>
                    <div class="step-execution">Step 19 execution</div>
                    
                </div>
                
                <div class="step-item skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-text">Then I should see "Bukti Karya"</span>
                        <span class="step-time">(0.01s)</span>
                    </div>
                    <div class="step-execution">Step 20 execution</div>
                    
                </div>
                
                <div class="step-item skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-text">Then I should see "Video Inspirasi"</span>
                        <span class="step-time">(0.01s)</span>
                    </div>
                    <div class="step-execution">Step 21 execution</div>
                    
                </div>
                
                <div class="step-item skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-text">Then I should see "Asesmen (Asesmen Murid & AKM Kelas)"</span>
                        <span class="step-time">(0.01s)</span>
                    </div>
                    <div class="step-execution">Step 22 execution</div>
                    
                </div>
                
                <div class="step-item skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-text">Then I should see "Kelas"</span>
                        <span class="step-time">(0.01s)</span>
                    </div>
                    <div class="step-execution">Step 23 execution</div>
                    
                </div>
                
                <div class="step-item skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-text">When I scroll to the "Dokumen dan Regulasi Rujukan" section</span>
                        <span class="step-time">(0.01s)</span>
                    </div>
                    <div class="step-execution">Step 24 execution</div>
                    
                </div>
                
                <div class="step-item skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-text">Then I should see "Dokumen dan Regulasi Rujukan"</span>
                        <span class="step-time">(0.01s)</span>
                    </div>
                    <div class="step-execution">Step 25 execution</div>
                    
                </div>
                
                <div class="step-item skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-text">Then I should see "Pengelolaan Pembelajaran"</span>
                        <span class="step-time">(0.01s)</span>
                    </div>
                    <div class="step-execution">Step 26 execution</div>
                    
                </div>
                
                <div class="step-item skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-text">Then I should see "Pengelolaan Kinerja"</span>
                        <span class="step-time">(0.01s)</span>
                    </div>
                    <div class="step-execution">Step 27 execution</div>
                    
                </div>
                
                <div class="step-item skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-text">Then I should see "Peningkatan Kompetensi"</span>
                        <span class="step-time">(0.01s)</span>
                    </div>
                    <div class="step-execution">Step 28 execution</div>
                    
                </div>
                
                <div class="step-item skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-text">Then I should see "Pengelolaan Satuan Pendidikan"</span>
                        <span class="step-time">(0.01s)</span>
                    </div>
                    <div class="step-execution">Step 29 execution</div>
                    
                </div>
                
                    </div>
                </div>
            </div>
            
            <div class="scenario-item" data-status="passed">
                <div class="scenario-header" onclick="toggleScenario('TC4')">
                    <div class="scenario-title">
                        <span class="status-icon">✅</span>
                        <span class="scenario-name">Test Scenario: [TC4]</span>
                    </div>
                    <div class="scenario-meta">
                        <span class="execution-time">(0.4s)</span>
                        <span class="arrow">▼</span>
                    </div>
                </div>
                <div class="scenario-content" id="scenario-TC4" style="display: none;">
                    <div class="steps-container">
                        
                <div class="step-item passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-text">Given I am on the main page</span>
                        <span class="step-time">(0.01s)</span>
                    </div>
                    <div class="step-execution">Step 1 execution</div>
                    
                </div>
                
                <div class="step-item skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-text">When I scroll to the "Ruang Murid" section</span>
                        <span class="step-time">(0.01s)</span>
                    </div>
                    <div class="step-execution">Step 2 execution</div>
                    
                </div>
                
                <div class="step-item skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-text">Then I should see "Ruang Murid"</span>
                        <span class="step-time">(0.01s)</span>
                    </div>
                    <div class="step-execution">Step 3 execution</div>
                    
                </div>
                
                <div class="step-item skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-text">And I click "Ruang Murid"</span>
                        <span class="step-time">(0.01s)</span>
                    </div>
                    <div class="step-execution">Step 4 execution</div>
                    
                </div>
                
                <div class="step-item skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-text">Then I should see "Layanan yang Tersedia"</span>
                        <span class="step-time">(0.01s)</span>
                    </div>
                    <div class="step-execution">Step 5 execution</div>
                    
                </div>
                
                <div class="step-item skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-text">Then I should see "Sumber Belajar"</span>
                        <span class="step-time">(0.01s)</span>
                    </div>
                    <div class="step-execution">Step 6 execution</div>
                    
                </div>
                
                <div class="step-item skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-text">Then I should see "Buku Bacaan Digital"</span>
                        <span class="step-time">(0.01s)</span>
                    </div>
                    <div class="step-execution">Step 7 execution</div>
                    
                </div>
                
                <div class="step-item skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-text">Then I should see "Akun Pendidikan"</span>
                        <span class="step-time">(0.01s)</span>
                    </div>
                    <div class="step-execution">Step 8 execution</div>
                    
                </div>
                
                <div class="step-item skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-text">Then I should see "Bank Soal"</span>
                        <span class="step-time">(0.01s)</span>
                    </div>
                    <div class="step-execution">Step 9 execution</div>
                    
                </div>
                
                <div class="step-item skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-text">Then I should see "Rapor Digital"</span>
                        <span class="step-time">(0.01s)</span>
                    </div>
                    <div class="step-execution">Step 10 execution</div>
                    
                </div>
                
                <div class="step-item skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-text">Then I should see "Riwayat Pendidikan"</span>
                        <span class="step-time">(0.01s)</span>
                    </div>
                    <div class="step-execution">Step 11 execution</div>
                    
                </div>
                
                <div class="step-item skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-text">Then I should see "Sumber Buku Teks Pembelajaran"</span>
                        <span class="step-time">(0.01s)</span>
                    </div>
                    <div class="step-execution">Step 12 execution</div>
                    
                </div>
                
                <div class="step-item skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-text">Then I should see "Pendidikan Jarak Jauh"</span>
                        <span class="step-time">(0.01s)</span>
                    </div>
                    <div class="step-execution">Step 13 execution</div>
                    
                </div>
                
                    </div>
                </div>
            </div>
            
            <div class="scenario-item" data-status="passed">
                <div class="scenario-header" onclick="toggleScenario('TC5')">
                    <div class="scenario-title">
                        <span class="status-icon">✅</span>
                        <span class="scenario-name">Test Scenario: [TC5]</span>
                    </div>
                    <div class="scenario-meta">
                        <span class="execution-time">(0.4s)</span>
                        <span class="arrow">▼</span>
                    </div>
                </div>
                <div class="scenario-content" id="scenario-TC5" style="display: none;">
                    <div class="steps-container">
                        
                <div class="step-item passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-text">Given I am on the main page</span>
                        <span class="step-time">(0.01s)</span>
                    </div>
                    <div class="step-execution">Step 1 execution</div>
                    
                </div>
                
                <div class="step-item skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-text">When I scroll to the "Ruang Sekolah" section</span>
                        <span class="step-time">(0.01s)</span>
                    </div>
                    <div class="step-execution">Step 2 execution</div>
                    
                </div>
                
                <div class="step-item skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-text">Then I should see "Ruang Sekolah"</span>
                        <span class="step-time">(0.01s)</span>
                    </div>
                    <div class="step-execution">Step 3 execution</div>
                    
                </div>
                
                <div class="step-item skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-text">And I click "Ruang Sekolah"</span>
                        <span class="step-time">(0.01s)</span>
                    </div>
                    <div class="step-execution">Step 4 execution</div>
                    
                </div>
                
                <div class="step-item skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-text">Then I should see "Layanan yang Tersedia"</span>
                        <span class="step-time">(0.01s)</span>
                    </div>
                    <div class="step-execution">Step 5 execution</div>
                    
                </div>
                
                <div class="step-item skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-text">Then I should see "Profil Sekolah"</span>
                        <span class="step-time">(0.01s)</span>
                    </div>
                    <div class="step-execution">Step 6 execution</div>
                    
                </div>
                
                <div class="step-item skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-text">Then I should see "Rapor Satuan Pendidikan"</span>
                        <span class="step-time">(0.01s)</span>
                    </div>
                    <div class="step-execution">Step 7 execution</div>
                    
                </div>
                
                <div class="step-item skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-text">Then I should see "Rencana Kegiatan dan Belanja Sekolah"</span>
                        <span class="step-time">(0.01s)</span>
                    </div>
                    <div class="step-execution">Step 8 execution</div>
                    
                </div>
                
                <div class="step-item skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-text">Then I should see "Akun Pendidikan"</span>
                        <span class="step-time">(0.01s)</span>
                    </div>
                    <div class="step-execution">Step 9 execution</div>
                    
                </div>
                
                <div class="step-item skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-text">Then I should see "Pengadaan Barang dan Jasa Sekolah"</span>
                        <span class="step-time">(0.01s)</span>
                    </div>
                    <div class="step-execution">Step 10 execution</div>
                    
                </div>
                
                    </div>
                </div>
            </div>
            
            <div class="scenario-item" data-status="passed">
                <div class="scenario-header" onclick="toggleScenario('TC6')">
                    <div class="scenario-title">
                        <span class="status-icon">✅</span>
                        <span class="scenario-name">Test Scenario: [TC6]</span>
                    </div>
                    <div class="scenario-meta">
                        <span class="execution-time">(0.4s)</span>
                        <span class="arrow">▼</span>
                    </div>
                </div>
                <div class="scenario-content" id="scenario-TC6" style="display: none;">
                    <div class="steps-container">
                        
                <div class="step-item passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-text">Given I am on the main page</span>
                        <span class="step-time">(0.01s)</span>
                    </div>
                    <div class="step-execution">Step 1 execution</div>
                    
                </div>
                
                <div class="step-item skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-text">When I scroll to the "Ruang Bahasa" section</span>
                        <span class="step-time">(0.01s)</span>
                    </div>
                    <div class="step-execution">Step 2 execution</div>
                    
                </div>
                
                <div class="step-item skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-text">Then I should see "Ruang Bahasa"</span>
                        <span class="step-time">(0.01s)</span>
                    </div>
                    <div class="step-execution">Step 3 execution</div>
                    
                </div>
                
                <div class="step-item skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-text">And I click "Ruang Bahasa"</span>
                        <span class="step-time">(0.01s)</span>
                    </div>
                    <div class="step-execution">Step 4 execution</div>
                    
                </div>
                
                <div class="step-item skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-text">Then I should see "Layanan yang Tersedia"</span>
                        <span class="step-time">(0.01s)</span>
                    </div>
                    <div class="step-execution">Step 5 execution</div>
                    
                </div>
                
                <div class="step-item skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-text">Then I should see "Kamus Bahasa"</span>
                        <span class="step-time">(0.01s)</span>
                    </div>
                    <div class="step-execution">Step 6 execution</div>
                    
                </div>
                
                <div class="step-item skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-text">Then I should see "Penerjemahan Daring"</span>
                        <span class="step-time">(0.01s)</span>
                    </div>
                    <div class="step-execution">Step 7 execution</div>
                    
                </div>
                
                <div class="step-item skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-text">Then I should see "Layanan UKBI"</span>
                        <span class="step-time">(0.01s)</span>
                    </div>
                    <div class="step-execution">Step 8 execution</div>
                    
                </div>
                
                <div class="step-item skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-text">Then I should see "BIPA Daring"</span>
                        <span class="step-time">(0.01s)</span>
                    </div>
                    <div class="step-execution">Step 9 execution</div>
                    
                </div>
                
                    </div>
                </div>
            </div>
            
            <div class="scenario-item" data-status="passed">
                <div class="scenario-header" onclick="toggleScenario('TC7')">
                    <div class="scenario-title">
                        <span class="status-icon">✅</span>
                        <span class="scenario-name">Test Scenario: [TC7]</span>
                    </div>
                    <div class="scenario-meta">
                        <span class="execution-time">(0.4s)</span>
                        <span class="arrow">▼</span>
                    </div>
                </div>
                <div class="scenario-content" id="scenario-TC7" style="display: none;">
                    <div class="steps-container">
                        
                <div class="step-item passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-text">Given I am on the main page</span>
                        <span class="step-time">(0.01s)</span>
                    </div>
                    <div class="step-execution">Step 1 execution</div>
                    
                </div>
                
                <div class="step-item skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-text">When I scroll to the "Ruang Pemerintah" section</span>
                        <span class="step-time">(0.01s)</span>
                    </div>
                    <div class="step-execution">Step 2 execution</div>
                    
                </div>
                
                <div class="step-item skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-text">Then I should see "Ruang Pemerintah"</span>
                        <span class="step-time">(0.01s)</span>
                    </div>
                    <div class="step-execution">Step 3 execution</div>
                    
                </div>
                
                <div class="step-item skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-text">And I click "Ruang Pemerintah"</span>
                        <span class="step-time">(0.01s)</span>
                    </div>
                    <div class="step-execution">Step 4 execution</div>
                    
                </div>
                
                <div class="step-item skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-text">Then I should see "Layanan yang Tersedia"</span>
                        <span class="step-time">(0.01s)</span>
                    </div>
                    <div class="step-execution">Step 5 execution</div>
                    
                </div>
                
                <div class="step-item skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-text">Then I should see "Neraca Pendidikan Daerah"</span>
                        <span class="step-time">(0.01s)</span>
                    </div>
                    <div class="step-execution">Step 6 execution</div>
                    
                </div>
                
                <div class="step-item skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-text">Then I should see "Akun Pendidikan"</span>
                        <span class="step-time">(0.01s)</span>
                    </div>
                    <div class="step-execution">Step 7 execution</div>
                    
                </div>
                
                <div class="step-item skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-text">Then I should see "Rapor Pendidikan Daerah"</span>
                        <span class="step-time">(0.01s)</span>
                    </div>
                    <div class="step-execution">Step 8 execution</div>
                    
                </div>
                
                <div class="step-item skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-text">Then I should see "Manajemen Aplikasi Rencana Kegiatan dan Anggaran Sekolah"</span>
                        <span class="step-time">(0.01s)</span>
                    </div>
                    <div class="step-execution">Step 9 execution</div>
                    
                </div>
                
                    </div>
                </div>
            </div>
            
            <div class="scenario-item" data-status="passed">
                <div class="scenario-header" onclick="toggleScenario('TC8')">
                    <div class="scenario-title">
                        <span class="status-icon">✅</span>
                        <span class="scenario-name">Test Scenario: [TC8]</span>
                    </div>
                    <div class="scenario-meta">
                        <span class="execution-time">(0.4s)</span>
                        <span class="arrow">▼</span>
                    </div>
                </div>
                <div class="scenario-content" id="scenario-TC8" style="display: none;">
                    <div class="steps-container">
                        
                <div class="step-item passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-text">Given I am on the main page</span>
                        <span class="step-time">(0.01s)</span>
                    </div>
                    <div class="step-execution">Step 1 execution</div>
                    
                </div>
                
                <div class="step-item skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-text">When I scroll to the "Ruang Mitra" section</span>
                        <span class="step-time">(0.01s)</span>
                    </div>
                    <div class="step-execution">Step 2 execution</div>
                    
                </div>
                
                <div class="step-item skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-text">Then I should see "Ruang Mitra"</span>
                        <span class="step-time">(0.01s)</span>
                    </div>
                    <div class="step-execution">Step 3 execution</div>
                    
                </div>
                
                <div class="step-item skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-text">And I click "Ruang Mitra"</span>
                        <span class="step-time">(0.01s)</span>
                    </div>
                    <div class="step-execution">Step 4 execution</div>
                    
                </div>
                
                <div class="step-item skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-text">Then I should see "Layanan yang Tersedia"</span>
                        <span class="step-time">(0.01s)</span>
                    </div>
                    <div class="step-execution">Step 5 execution</div>
                    
                </div>
                
                <div class="step-item skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-text">Then I should see "Mitra Barjas Pendidikan"</span>
                        <span class="step-time">(0.01s)</span>
                    </div>
                    <div class="step-execution">Step 6 execution</div>
                    
                </div>
                
                <div class="step-item skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-text">Then I should see "Kolaborasi Pendidikan"</span>
                        <span class="step-time">(0.01s)</span>
                    </div>
                    <div class="step-execution">Step 7 execution</div>
                    
                </div>
                
                <div class="step-item skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-text">Then I should see "Relawan Pendidikan"</span>
                        <span class="step-time">(0.01s)</span>
                    </div>
                    <div class="step-execution">Step 8 execution</div>
                    
                </div>
                
                    </div>
                </div>
            </div>
            
            <div class="scenario-item" data-status="passed">
                <div class="scenario-header" onclick="toggleScenario('TC9')">
                    <div class="scenario-title">
                        <span class="status-icon">✅</span>
                        <span class="scenario-name">Test Scenario: [TC9]</span>
                    </div>
                    <div class="scenario-meta">
                        <span class="execution-time">(0.4s)</span>
                        <span class="arrow">▼</span>
                    </div>
                </div>
                <div class="scenario-content" id="scenario-TC9" style="display: none;">
                    <div class="steps-container">
                        
                <div class="step-item passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-text">Given I am on the main page</span>
                        <span class="step-time">(0.01s)</span>
                    </div>
                    <div class="step-execution">Step 1 execution</div>
                    
                </div>
                
                <div class="step-item skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-text">When I scroll to the "Ruang Publik" section</span>
                        <span class="step-time">(0.01s)</span>
                    </div>
                    <div class="step-execution">Step 2 execution</div>
                    
                </div>
                
                <div class="step-item skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-text">Then I should see "Ruang Publik"</span>
                        <span class="step-time">(0.01s)</span>
                    </div>
                    <div class="step-execution">Step 3 execution</div>
                    
                </div>
                
                <div class="step-item skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-text">And I click "Ruang Publik"</span>
                        <span class="step-time">(0.01s)</span>
                    </div>
                    <div class="step-execution">Step 4 execution</div>
                    
                </div>
                
                <div class="step-item skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-text">Then I should see "Layanan yang Tersedia"</span>
                        <span class="step-time">(0.01s)</span>
                    </div>
                    <div class="step-execution">Step 5 execution</div>
                    
                </div>
                
                <div class="step-item skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-text">Then I should see "Pusat Perbukuan"</span>
                        <span class="step-time">(0.01s)</span>
                    </div>
                    <div class="step-execution">Step 6 execution</div>
                    
                </div>
                
                <div class="step-item skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-text">Then I should see "Bantuan Pendidikan"</span>
                        <span class="step-time">(0.01s)</span>
                    </div>
                    <div class="step-execution">Step 7 execution</div>
                    
                </div>
                
                <div class="step-item skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-text">Then I should see "Layanan Informasi dan Pengaduan"</span>
                        <span class="step-time">(0.01s)</span>
                    </div>
                    <div class="step-execution">Step 8 execution</div>
                    
                </div>
                
                <div class="step-item skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-text">Then I should see "Informasi Data Pendidikan"</span>
                        <span class="step-time">(0.01s)</span>
                    </div>
                    <div class="step-execution">Step 9 execution</div>
                    
                </div>
                
                <div class="step-item skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-text">Then I should see "Publikasi Ilmiah"</span>
                        <span class="step-time">(0.01s)</span>
                    </div>
                    <div class="step-execution">Step 10 execution</div>
                    
                </div>
                
                <div class="step-item skipped">
                    <div class="step-header">
                        <span class="step-icon">⏭️</span>
                        <span class="step-text">Then I should see "Produk Hukum"</span>
                        <span class="step-time">(0.01s)</span>
                    </div>
                    <div class="step-execution">Step 11 execution</div>
                    
                </div>
                
                    </div>
                </div>
            </div>
            
                    </div>
                </div>
                
                <div class="footer">
                    Maintained by Test Automation Team.
                </div>
            </div>
            
            <script>
                function toggleScenario(tcId) {
                    const content = document.getElementById(`scenario-${tcId}`);
                    const arrow = content.previousElementSibling.querySelector('.arrow');
                    
                    if (content.style.display === 'none' || content.style.display === '') {
                        content.style.display = 'block';
                        arrow.textContent = '▲';
                    } else {
                        content.style.display = 'none';
                        arrow.textContent = '▼';
                    }
                }
                
                function filterScenarios(status) {
                    const scenarios = document.querySelectorAll('.scenario-item');
                    const buttons = document.querySelectorAll('.filter-button');
                    
                    // Update active button
                    buttons.forEach(btn => btn.classList.remove('active'));
                    event.target.classList.add('active');
                    
                    scenarios.forEach(scenario => {
                        const scenarioStatus = scenario.getAttribute('data-status');
                        if (status === 'all' || scenarioStatus === status) {
                            scenario.style.display = 'block';
                        } else {
                            scenario.style.display = 'none';
                        }
                    });
                }
                
                function toggleErrorDetails(errorId) {
                    const content = document.getElementById(errorId);
                    if (content.style.display === 'none' || content.style.display === '') {
                        content.style.display = 'block';
                    } else {
                        content.style.display = 'none';
                    }
                }
                
                function showPassedTooltip(event, passedText) {
                    const tooltip = document.getElementById('passed-tooltip');
                    tooltip.innerHTML = passedText;
                    tooltip.style.left = (event.clientX + 10) + 'px';
                    tooltip.style.top = (event.clientY - 30) + 'px';
                    tooltip.style.opacity = '1';
                }
                
                function showFailedTooltip(event, failedText) {
                    const tooltip = document.getElementById('failed-tooltip');
                    tooltip.innerHTML = failedText;
                    tooltip.style.left = (event.clientX + 10) + 'px';
                    tooltip.style.top = (event.clientY - 30) + 'px';
                    tooltip.style.opacity = '1';
                }
                
                function hideTooltip() {
                    document.getElementById('passed-tooltip').style.opacity = '0';
                    document.getElementById('failed-tooltip').style.opacity = '0';
                }
                
                function handleDonutHover(event, passedTests, failedTests) {
                    const rect = event.target.getBoundingClientRect();
                    const centerX = rect.left + rect.width / 2;
                    const centerY = rect.top + rect.height / 2;
                    const mouseX = event.clientX;
                    const mouseY = event.clientY;
                    
                    // Calculate angle from center to mouse position
                    const deltaX = mouseX - centerX;
                    const deltaY = centerY - mouseY; // Invert Y axis
                    const angle = Math.atan2(deltaY, deltaX) * 180 / Math.PI;
                    const normalizedAngle = (angle + 360) % 360;
                    
                    // Calculate passed section angle
                    const passedAngle = (passedTests / (passedTests + failedTests)) * 360;
                    
                    // Determine which section is being hovered
                    if (normalizedAngle <= passedAngle) {
                        // Hovering over passed section (green)
                        showPassedTooltip(event, `Passed: ${passedTests}`);
                        document.getElementById('failed-tooltip').style.opacity = '0';
                    } else {
                        // Hovering over failed section (red)
                        showFailedTooltip(event, `Failed: ${failedTests}`);
                        document.getElementById('passed-tooltip').style.opacity = '0';
                    }
                }
            </script>
        </body>
        </html>
        