
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Feature: Parallel Test Execution</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }
        .header h2 {
            margin: 10px 0 0 0;
            font-size: 1.2em;
            font-weight: 300;
            opacity: 0.9;
        }
        .nav-bar {
            background: #f8f9fa;
            padding: 15px 30px;
            border-bottom: 1px solid #eee;
        }
        .back-button {
            background: #007bff;
            color: white;
            padding: 8px 16px;
            border-radius: 5px;
            text-decoration: none;
            display: inline-block;
            margin-bottom: 15px;
        }
        .tags {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
        }
        .tag {
            background: #e9ecef;
            color: #495057;
            padding: 5px 12px;
            border-radius: 20px;
            text-decoration: none;
            font-size: 0.9em;
        }
        .tag:hover {
            background: #007bff;
            color: white;
        }
        .feature-summary {
            padding: 30px;
            background: #f8f9fa;
        }
        .feature-details {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 30px;
            align-items: stretch;
        }
        .feature-info {
            background: white;
            border-radius: 10px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            height: 100%;
            display: flex;
            flex-direction: column;
        }
        .feature-info h3 {
            margin: 0 0 20px 0;
            color: #333;
            font-size: 1.5em;
        }
        .donut-chart {
            width: 150px;
            height: 150px;
            border-radius: 50%;
            margin: 0 auto 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.8em;
            font-weight: bold;
            color: white;
            position: relative;
            cursor: pointer;
            background: conic-gradient(#28a745 0deg 360.0deg, #dc3545 360.0deg 360deg);
        }
        .donut-chart::before {
            content: '';
            position: absolute;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background: white;
            z-index: 1;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }
        .donut-chart::after {
            content: '14';
            position: absolute;
            z-index: 2;
            color: #333;
            font-size: 1.5em;
            font-weight: bold;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }
        .donut-chart.passed {
            background: conic-gradient(#28a745 0deg 360deg);
        }
        .donut-chart.failed {
            background: conic-gradient(#dc3545 0deg 360deg);
        }
        .donut-chart.mixed {
            background: conic-gradient(#28a745 0deg 360.0deg, #dc3545 360.0deg 360deg);
        }
        .passed-tooltip, .failed-tooltip {
            position: absolute;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 6px 10px;
            border-radius: 4px;
            font-size: 0.7em;
            white-space: nowrap;
            pointer-events: none;
            opacity: 0;
            transition: opacity 0.3s;
            z-index: 1000;
            top: -50px;
            left: 50%;
            transform: translateX(-50%);
        }
        .passed-tooltip {
            border-left: 3px solid #28a745;
        }
        .failed-tooltip {
            border-left: 3px solid #dc3545;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            text-align: left;
        }
        .stat-item {
            display: flex;
            justify-content: space-between;
            padding: 5px 0;
        }
        .stat-label {
            font-weight: 500;
            color: #666;
        }
        .stat-value {
            font-weight: bold;
            color: #333;
        }
        .metadata {
            background: white;
            border-radius: 10px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            height: 100%;
            display: flex;
            flex-direction: column;
        }
        .metadata h3 {
            margin: 0 0 20px 0;
            color: #333;
            font-size: 1.3em;
        }
        .filter-bar {
            background: white;
            padding: 20px 30px;
            border-bottom: 1px solid #eee;
        }
        .filter-options {
            display: flex;
            gap: 20px;
        }
        .filter-option {
            padding: 8px 16px;
            border-radius: 5px;
            text-decoration: none;
            color: #666;
            background: #f8f9fa;
        }
        .filter-option.active {
            background: #007bff;
            color: white;
        }
        .scenarios {
            padding: 30px;
        }
        .scenario-item {
            background: white;
            border-radius: 10px;
            margin-bottom: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            overflow: hidden;
        }
        .scenario-header {
            padding: 20px;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: #f8f9fa;
        }
        .scenario-header:hover {
            background: #e9ecef;
        }
        .scenario-title {
            display: flex;
            align-items: center;
            gap: 10px;
            font-weight: 600;
            color: #333;
        }
        .scenario-icon {
            font-size: 1.2em;
        }
        .scenario-meta {
            display: flex;
            align-items: center;
            gap: 15px;
            color: #666;
        }
        .scenario-duration {
            font-size: 0.9em;
        }
        .scenario-arrow {
            transition: transform 0.3s;
        }
        .scenario-content {
            display: none;
            padding: 20px;
            border-top: 1px solid #eee;
        }
        .scenario-content.expanded {
            display: block;
        }
        .step-item {
            display: flex;
            align-items: flex-start;
            gap: 15px;
            padding: 15px 0;
            border-bottom: 1px solid #f0f0f0;
        }
        .step-item:last-child {
            border-bottom: none;
        }
        .step-icon {
            font-size: 1.2em;
            margin-top: 2px;
        }
        .step-content {
            flex: 1;
        }
        .step-text {
            font-weight: 500;
            color: #333;
            margin-bottom: 5px;
        }
        .step-details {
            font-size: 0.9em;
            color: #666;
        }
        .step-item.passed .step-icon {
            color: #28a745;
        }
        .step-item.failed .step-icon {
            color: #dc3545;
        }
        .step-item.skipped .step-icon {
            color: #ffc107;
        }
        .footer {
            background: #f8f9fa;
            padding: 20px;
            text-align: center;
            color: #666;
            border-top: 1px solid #eee;
        }
        .social-icons {
            margin-top: 10px;
        }
        .social-icons a {
            margin: 0 10px;
            color: #666;
            text-decoration: none;
            font-size: 1.2em;
        }
    </style>
    <script>
        function toggleScenario(scenarioId) {
            const content = document.getElementById('content-' + scenarioId);
            const arrow = content.previousElementSibling.querySelector('.scenario-arrow');
            
            if (content.classList.contains('expanded')) {
                content.classList.remove('expanded');
                arrow.style.transform = 'rotate(0deg)';
            } else {
                content.classList.add('expanded');
                arrow.style.transform = 'rotate(180deg)';
            }
        }
        
        function showPassedTooltip(event, passedText) {
            const tooltip = document.getElementById('passed-tooltip');
            const rect = event.target.getBoundingClientRect();
            tooltip.innerHTML = passedText;
            tooltip.style.left = (rect.left + rect.width/2 - tooltip.offsetWidth/2) + 'px';
            tooltip.style.top = (rect.top - tooltip.offsetHeight - 15) + 'px';
            tooltip.style.opacity = '1';
        }
        
        function showFailedTooltip(event, failedText) {
            const tooltip = document.getElementById('failed-tooltip');
            const rect = event.target.getBoundingClientRect();
            tooltip.innerHTML = failedText;
            tooltip.style.left = (rect.left + rect.width/2 - tooltip.offsetWidth/2) + 'px';
            tooltip.style.top = (rect.top - tooltip.offsetHeight - 15) + 'px';
            tooltip.style.opacity = '1';
        }
        
        function hideTooltip() {
            document.getElementById('passed-tooltip').style.opacity = '0';
            document.getElementById('failed-tooltip').style.opacity = '0';
        }
        
        function handleDonutHover(event, passedTests, failedTests) {
            const rect = event.target.getBoundingClientRect();
            const centerX = rect.left + rect.width / 2;
            const centerY = rect.top + rect.height / 2;
            const mouseX = event.clientX;
            const mouseY = event.clientY;
            
            // Calculate angle from center to mouse position
            const deltaX = mouseX - centerX;
            const deltaY = centerY - mouseY; // Invert Y axis
            const angle = Math.atan2(deltaY, deltaX) * 180 / Math.PI;
            const normalizedAngle = (angle + 360) % 360;
            
            // Calculate passed section angle
            const passedAngle = (passedTests / (passedTests + failedTests)) * 360;
            
            // Determine which section is being hovered
            if (normalizedAngle <= passedAngle) {
                // Hovering over passed section (green)
                showPassedTooltip(event, `Passed: ${passedTests}`);
                document.getElementById('failed-tooltip').style.opacity = '0';
            } else {
                // Hovering over failed section (red)
                showFailedTooltip(event, `Failed: ${failedTests}`);
                document.getElementById('passed-tooltip').style.opacity = '0';
            }
        }
        
        function filterScenarios(filter, event) {
            // Update active filter button
            document.querySelectorAll('.filter-option').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');
            
            // Filter scenarios
            const scenarios = document.querySelectorAll('.scenario-item');
            scenarios.forEach(scenario => {
                const status = scenario.getAttribute('data-status');
                if (filter === 'all' || status === filter) {
                    scenario.style.display = 'block';
                } else {
                    scenario.style.display = 'none';
                }
            });
        }
        
        // Expand first scenario by default
        window.onload = function() {
            const firstScenario = document.querySelector('.scenario-item');
            if (firstScenario) {
                const scenarioId = firstScenario.querySelector('.scenario-header').getAttribute('onclick').match(/'([^']+)'/)[1];
                toggleScenario(scenarioId);
            }
        };
    </script>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Rumah Pendidikan Automation Report</h1>
            <h2>Comprehensive Test Automation Report</h2>
        </div>
        
        <div class="nav-bar">
            <a href="parallel_comprehensive_20250803_021252.html" class="back-button">← Features Overview</a>
            <div class="tags">
                <a href="#TC1" class="tag">TC1</a><a href="#TC10" class="tag">TC10</a><a href="#TC11" class="tag">TC11</a><a href="#TC12" class="tag">TC12</a><a href="#TC13" class="tag">TC13</a><a href="#TC14" class="tag">TC14</a><a href="#TC2" class="tag">TC2</a><a href="#TC3" class="tag">TC3</a><a href="#TC4" class="tag">TC4</a><a href="#TC5" class="tag">TC5</a><a href="#TC6" class="tag">TC6</a><a href="#TC7" class="tag">TC7</a><a href="#TC8" class="tag">TC8</a><a href="#TC9" class="tag">TC9</a>
            </div>
        </div>
        
        <div class="feature-summary">
            <div class="feature-details">
                <div class="feature-info">
                    <h3>Feature: Parallel Test Execution</h3>
                    <p><strong>Description:</strong> Regression Test</p>
                    <p><strong>File name:</strong> parallel_execution.feature</p>
                    
                    <div class="donut-chart passed" 
                         onmousemove="handleDonutHover(event, 14, 0)" 
                         onmouseout="hideTooltip()">
                        <div class="passed-tooltip" id="passed-tooltip"></div>
                        <div class="failed-tooltip" id="failed-tooltip"></div>
                    </div>
                    
                    <div class="stats-grid">
                        <div class="stat-item">
                            <span class="stat-label">STATUS</span>
                            <span class="stat-value">✅ Passed</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">PROGRESS</span>
                            <span class="stat-value">100.0%</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">STATUS</span>
                            <span class="stat-value">❌ Failed</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">PROGRESS</span>
                            <span class="stat-value">0.0%</span>
                        </div>
                    </div>
                </div>
                
                <div class="metadata">
                    <h3>Metadata</h3>
                    <div class="stat-item">
                        <span class="stat-label">Device</span>
                        <span class="stat-value">Local Machine</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">OS</span>
                        <span class="stat-value">macOS</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">Browser</span>
                        <span class="stat-value">Chrome 103</span>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="filter-bar">
            <div class="filter-options">
                <a href="#" class="filter-option active" onclick="filterScenarios('all', event)">All (14)</a>
                <a href="#" class="filter-option" onclick="filterScenarios('passed', event)">Passed (14)</a>
                <a href="#" class="filter-option" onclick="filterScenarios('failed', event)">Failed (0)</a>
            </div>
        </div>
        
        <div class="scenarios">
            
            <div class="scenario-item" data-status="passed">
                <div class="scenario-header" onclick="toggleScenario('TC1')">
                    <div class="scenario-title">
                        <span class="scenario-icon">✅</span>
                        Parallel Test: [TC1]
                    </div>
                    <div class="scenario-meta">
                        <span class="scenario-duration">0.6s</span>
                        <span class="scenario-arrow">▼</span>
                    </div>
                </div>
                <div class="scenario-content" id="content-TC1">
                    
                <div class="step-item passed">
                    <div class="step-icon">✅</div>
                    <div class="step-content">
                        <div class="step-text">Given I am on the main page</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                <div class="step-item skipped">
                    <div class="step-icon">⏭️</div>
                    <div class="step-content">
                        <div class="step-text">When I type "Pelatihan" in the search field</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                <div class="step-item skipped">
                    <div class="step-icon">⏭️</div>
                    <div class="step-content">
                        <div class="step-text">And I click the "Cari" button</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                <div class="step-item skipped">
                    <div class="step-icon">⏭️</div>
                    <div class="step-content">
                        <div class="step-text">Then I should see "Pelatihan"</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                </div>
            </div>
            
            <div class="scenario-item" data-status="passed">
                <div class="scenario-header" onclick="toggleScenario('TC10')">
                    <div class="scenario-title">
                        <span class="scenario-icon">✅</span>
                        Parallel Test: [TC10]
                    </div>
                    <div class="scenario-meta">
                        <span class="scenario-duration">0.5s</span>
                        <span class="scenario-arrow">▼</span>
                    </div>
                </div>
                <div class="scenario-content" id="content-TC10">
                    
                <div class="step-item passed">
                    <div class="step-icon">✅</div>
                    <div class="step-content">
                        <div class="step-text">Given I am on the main page</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                <div class="step-item skipped">
                    <div class="step-icon">⏭️</div>
                    <div class="step-content">
                        <div class="step-text">When I scroll to the "Ruang Orang Tua" section</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                <div class="step-item skipped">
                    <div class="step-icon">⏭️</div>
                    <div class="step-content">
                        <div class="step-text">Then I should see "Ruang Orang Tua"</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                <div class="step-item skipped">
                    <div class="step-icon">⏭️</div>
                    <div class="step-content">
                        <div class="step-text">And I click "Ruang Orang Tua"</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                <div class="step-item skipped">
                    <div class="step-icon">⏭️</div>
                    <div class="step-content">
                        <div class="step-text">Then I should see "Layanan yang Tersedia"</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                <div class="step-item skipped">
                    <div class="step-icon">⏭️</div>
                    <div class="step-content">
                        <div class="step-text">Then I should see "Layanan Informasi dan Pengaduan"</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                <div class="step-item skipped">
                    <div class="step-icon">⏭️</div>
                    <div class="step-content">
                        <div class="step-text">Then I should see "Panduan Pendampingan"</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                <div class="step-item skipped">
                    <div class="step-icon">⏭️</div>
                    <div class="step-content">
                        <div class="step-text">Then I should see "Konsultasi Pendidikan"</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                </div>
            </div>
            
            <div class="scenario-item" data-status="passed">
                <div class="scenario-header" onclick="toggleScenario('TC11')">
                    <div class="scenario-title">
                        <span class="scenario-icon">✅</span>
                        Parallel Test: [TC11]
                    </div>
                    <div class="scenario-meta">
                        <span class="scenario-duration">0.4s</span>
                        <span class="scenario-arrow">▼</span>
                    </div>
                </div>
                <div class="scenario-content" id="content-TC11">
                    
                <div class="step-item passed">
                    <div class="step-icon">✅</div>
                    <div class="step-content">
                        <div class="step-text">Given I am on the main page</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                <div class="step-item skipped">
                    <div class="step-icon">⏭️</div>
                    <div class="step-content">
                        <div class="step-text">When I scroll to the "Semangat Rumah Pendidikan" section</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                <div class="step-item skipped">
                    <div class="step-icon">⏭️</div>
                    <div class="step-content">
                        <div class="step-text">Then I should see "Semangat Rumah Pendidikan"</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                <div class="step-item skipped">
                    <div class="step-icon">⏭️</div>
                    <div class="step-content">
                        <div class="step-text">Then I should see "Pelajari Selengkapnya"</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                <div class="step-item skipped">
                    <div class="step-icon">⏭️</div>
                    <div class="step-content">
                        <div class="step-text">And I click "Pelajari Selengkapnya"</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                <div class="step-item skipped">
                    <div class="step-icon">⏭️</div>
                    <div class="step-content">
                        <div class="step-text">When I scroll to the "Mengenal Rumah Pendidikan" section</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                <div class="step-item skipped">
                    <div class="step-icon">⏭️</div>
                    <div class="step-content">
                        <div class="step-text">Then I should see "Mengenal Rumah Pendidikan"</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                </div>
            </div>
            
            <div class="scenario-item" data-status="passed">
                <div class="scenario-header" onclick="toggleScenario('TC12')">
                    <div class="scenario-title">
                        <span class="scenario-icon">✅</span>
                        Parallel Test: [TC12]
                    </div>
                    <div class="scenario-meta">
                        <span class="scenario-duration">0.5s</span>
                        <span class="scenario-arrow">▼</span>
                    </div>
                </div>
                <div class="scenario-content" id="content-TC12">
                    
                <div class="step-item passed">
                    <div class="step-icon">✅</div>
                    <div class="step-content">
                        <div class="step-text">Given I am on the main page</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                <div class="step-item skipped">
                    <div class="step-icon">⏭️</div>
                    <div class="step-content">
                        <div class="step-text">When I scroll to the "Informasi Untuk Anda" section</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                <div class="step-item skipped">
                    <div class="step-icon">⏭️</div>
                    <div class="step-content">
                        <div class="step-text">Then I should see "Informasi Untuk Anda"</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                <div class="step-item skipped">
                    <div class="step-icon">⏭️</div>
                    <div class="step-content">
                        <div class="step-text">And I click "Lihat lebih banyak"</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                <div class="step-item skipped">
                    <div class="step-icon">⏭️</div>
                    <div class="step-content">
                        <div class="step-text">Then I should see "Informasi Untuk Anda"</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                <div class="step-item skipped">
                    <div class="step-icon">⏭️</div>
                    <div class="step-content">
                        <div class="step-text">And I click "Artikel"</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                </div>
            </div>
            
            <div class="scenario-item" data-status="passed">
                <div class="scenario-header" onclick="toggleScenario('TC13')">
                    <div class="scenario-title">
                        <span class="scenario-icon">✅</span>
                        Parallel Test: [TC13]
                    </div>
                    <div class="scenario-meta">
                        <span class="scenario-duration">0.5s</span>
                        <span class="scenario-arrow">▼</span>
                    </div>
                </div>
                <div class="scenario-content" id="content-TC13">
                    
                <div class="step-item passed">
                    <div class="step-icon">✅</div>
                    <div class="step-content">
                        <div class="step-text">Given I am on the main page</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                <div class="step-item skipped">
                    <div class="step-icon">⏭️</div>
                    <div class="step-content">
                        <div class="step-text">When I scroll to the "Pertanyaan yang paling sering ditanyakan" section</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                <div class="step-item skipped">
                    <div class="step-icon">⏭️</div>
                    <div class="step-content">
                        <div class="step-text">And I click button beside "Apa manfaat menggunakan Rumah Pendidikan?"</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                <div class="step-item skipped">
                    <div class="step-icon">⏭️</div>
                    <div class="step-content">
                        <div class="step-text">Then I should see "manfaat"</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                <div class="step-item skipped">
                    <div class="step-icon">⏭️</div>
                    <div class="step-content">
                        <div class="step-text">And I click button beside "Bagaimana cara mengakses Rumah Pendidikan?"</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                <div class="step-item skipped">
                    <div class="step-icon">⏭️</div>
                    <div class="step-content">
                        <div class="step-text">Then I should see "cara mengakses"</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                <div class="step-item skipped">
                    <div class="step-icon">⏭️</div>
                    <div class="step-content">
                        <div class="step-text">And I click button beside "Apakah pengguna harus menggunakan Akun belajar.id untuk mengakses Rumah Pendidikan?"</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                <div class="step-item skipped">
                    <div class="step-icon">⏭️</div>
                    <div class="step-content">
                        <div class="step-text">Then I should see "masyarakat umum"</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                <div class="step-item skipped">
                    <div class="step-icon">⏭️</div>
                    <div class="step-content">
                        <div class="step-text">And I click button beside "Siapa saja pengguna yang disarankan untuk menggunakan platform Rumah Pendidikan"</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                <div class="step-item skipped">
                    <div class="step-icon">⏭️</div>
                    <div class="step-content">
                        <div class="step-text">Then I should see "pengguna yang disarankan"</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                <div class="step-item skipped">
                    <div class="step-icon">⏭️</div>
                    <div class="step-content">
                        <div class="step-text">And I click button beside "Apa perbedaan rumah pendidikan dengan platform digital pendidikan milik Kemendikdasmen lainnya?"</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                <div class="step-item skipped">
                    <div class="step-icon">⏭️</div>
                    <div class="step-content">
                        <div class="step-text">Then I should see "perbedaan"</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                <div class="step-item skipped">
                    <div class="step-icon">⏭️</div>
                    <div class="step-content">
                        <div class="step-text">And I click button "Ke Pusat Informasi"</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                <div class="step-item skipped">
                    <div class="step-icon">⏭️</div>
                    <div class="step-content">
                        <div class="step-text">Then user direct to page "https://pusatinformasi.rumahpendidikan.kemendikdasmen.go.id/hc/id"</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                </div>
            </div>
            
            <div class="scenario-item" data-status="passed">
                <div class="scenario-header" onclick="toggleScenario('TC14')">
                    <div class="scenario-title">
                        <span class="scenario-icon">✅</span>
                        Parallel Test: [TC14]
                    </div>
                    <div class="scenario-meta">
                        <span class="scenario-duration">0.5s</span>
                        <span class="scenario-arrow">▼</span>
                    </div>
                </div>
                <div class="scenario-content" id="content-TC14">
                    
                <div class="step-item passed">
                    <div class="step-icon">✅</div>
                    <div class="step-content">
                        <div class="step-text">Given I am on the main page</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                <div class="step-item skipped">
                    <div class="step-icon">⏭️</div>
                    <div class="step-content">
                        <div class="step-text">When I scroll to the "Ke Pusat Informasi" section</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                <div class="step-item skipped">
                    <div class="step-icon">⏭️</div>
                    <div class="step-content">
                        <div class="step-text">Then I should see "Kementerian Pendidikan Dasar dan Menengah"</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                <div class="step-item skipped">
                    <div class="step-icon">⏭️</div>
                    <div class="step-content">
                        <div class="step-text">Then I should see "Kompleks Kementerian Pendidikan dan Kebudayaan, Senayan, Jakarta Pusat 10270"</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                <div class="step-item skipped">
                    <div class="step-icon">⏭️</div>
                    <div class="step-content">
                        <div class="step-text">Then I should see "Pusat Bantuan Rumah Pendidikan"</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                <div class="step-item skipped">
                    <div class="step-icon">⏭️</div>
                    <div class="step-content">
                        <div class="step-text">Then I should see "Syarat & Ketentuan"</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                <div class="step-item skipped">
                    <div class="step-icon">⏭️</div>
                    <div class="step-content">
                        <div class="step-text">Then I should see "Kebijakan Privasi"</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                <div class="step-item skipped">
                    <div class="step-icon">⏭️</div>
                    <div class="step-content">
                        <div class="step-text">And I click "Kebijakan Privasi"</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                <div class="step-item skipped">
                    <div class="step-icon">⏭️</div>
                    <div class="step-content">
                        <div class="step-text">And I click "Syarat & Ketentuan"</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                <div class="step-item skipped">
                    <div class="step-icon">⏭️</div>
                    <div class="step-content">
                        <div class="step-text">And I click "Pusat Bantuan Rumah Pendidikan"</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                </div>
            </div>
            
            <div class="scenario-item" data-status="passed">
                <div class="scenario-header" onclick="toggleScenario('TC2')">
                    <div class="scenario-title">
                        <span class="scenario-icon">✅</span>
                        Parallel Test: [TC2]
                    </div>
                    <div class="scenario-meta">
                        <span class="scenario-duration">0.5s</span>
                        <span class="scenario-arrow">▼</span>
                    </div>
                </div>
                <div class="scenario-content" id="content-TC2">
                    
                <div class="step-item passed">
                    <div class="step-icon">✅</div>
                    <div class="step-content">
                        <div class="step-text">Given I am on the main page</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                <div class="step-item skipped">
                    <div class="step-icon">⏭️</div>
                    <div class="step-content">
                        <div class="step-text">When I scroll to the "Jelajahi Ruang di Rumah Pendidikan" section</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                <div class="step-item skipped">
                    <div class="step-icon">⏭️</div>
                    <div class="step-content">
                        <div class="step-text">Then I should see "Jelajahi Ruang di Rumah Pendidikan"</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                <div class="step-item skipped">
                    <div class="step-icon">⏭️</div>
                    <div class="step-content">
                        <div class="step-text">Then I should see "Ruang GTK"</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                <div class="step-item skipped">
                    <div class="step-icon">⏭️</div>
                    <div class="step-content">
                        <div class="step-text">Then I should see "Ruang Murid"</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                <div class="step-item skipped">
                    <div class="step-icon">⏭️</div>
                    <div class="step-content">
                        <div class="step-text">Then I should see "Ruang Sekolah"</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                <div class="step-item skipped">
                    <div class="step-icon">⏭️</div>
                    <div class="step-content">
                        <div class="step-text">Then I should see "Ruang Bahasa"</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                <div class="step-item skipped">
                    <div class="step-icon">⏭️</div>
                    <div class="step-content">
                        <div class="step-text">When I scroll to the "Ruang Pemerintah" section</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                <div class="step-item skipped">
                    <div class="step-icon">⏭️</div>
                    <div class="step-content">
                        <div class="step-text">Then I should see "Ruang Pemerintah"</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                <div class="step-item skipped">
                    <div class="step-icon">⏭️</div>
                    <div class="step-content">
                        <div class="step-text">Then I should see "Ruang Mitra"</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                <div class="step-item skipped">
                    <div class="step-icon">⏭️</div>
                    <div class="step-content">
                        <div class="step-text">Then I should see "Ruang Publik"</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                <div class="step-item skipped">
                    <div class="step-icon">⏭️</div>
                    <div class="step-content">
                        <div class="step-text">Then I should see "Ruang Orang Tua"</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                </div>
            </div>
            
            <div class="scenario-item" data-status="passed">
                <div class="scenario-header" onclick="toggleScenario('TC3')">
                    <div class="scenario-title">
                        <span class="scenario-icon">✅</span>
                        Parallel Test: [TC3]
                    </div>
                    <div class="scenario-meta">
                        <span class="scenario-duration">0.4s</span>
                        <span class="scenario-arrow">▼</span>
                    </div>
                </div>
                <div class="scenario-content" id="content-TC3">
                    
                <div class="step-item passed">
                    <div class="step-icon">✅</div>
                    <div class="step-content">
                        <div class="step-text">Given I am on the main page</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                <div class="step-item skipped">
                    <div class="step-icon">⏭️</div>
                    <div class="step-content">
                        <div class="step-text">When I scroll to the "Ruang GTK" section</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                <div class="step-item skipped">
                    <div class="step-icon">⏭️</div>
                    <div class="step-content">
                        <div class="step-text">Then I should see "Ruang GTK"</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                <div class="step-item skipped">
                    <div class="step-icon">⏭️</div>
                    <div class="step-content">
                        <div class="step-text">And I click "Ruang GTK"</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                <div class="step-item skipped">
                    <div class="step-icon">⏭️</div>
                    <div class="step-content">
                        <div class="step-text">Then I should see "Layanan yang Tersedia"</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                <div class="step-item skipped">
                    <div class="step-icon">⏭️</div>
                    <div class="step-content">
                        <div class="step-text">Then I should see "Diklat"</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                <div class="step-item skipped">
                    <div class="step-icon">⏭️</div>
                    <div class="step-content">
                        <div class="step-text">Then I should see "Sertifikasi Pendidik"</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                <div class="step-item skipped">
                    <div class="step-icon">⏭️</div>
                    <div class="step-content">
                        <div class="step-text">Then I should see "Pelatihan Mandiri"</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                <div class="step-item skipped">
                    <div class="step-icon">⏭️</div>
                    <div class="step-content">
                        <div class="step-text">Then I should see "Komunitas"</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                <div class="step-item skipped">
                    <div class="step-icon">⏭️</div>
                    <div class="step-content">
                        <div class="step-text">When I scroll to the "Karir dan Kinerja" section</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                <div class="step-item skipped">
                    <div class="step-icon">⏭️</div>
                    <div class="step-content">
                        <div class="step-text">Then I should see "Karir dan Kinerja"</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                <div class="step-item skipped">
                    <div class="step-icon">⏭️</div>
                    <div class="step-content">
                        <div class="step-text">Then I should see "Pengelolaan Kinerja"</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                <div class="step-item skipped">
                    <div class="step-icon">⏭️</div>
                    <div class="step-content">
                        <div class="step-text">Then I should see "Seleksi Kepala Sekolah"</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                <div class="step-item skipped">
                    <div class="step-icon">⏭️</div>
                    <div class="step-content">
                        <div class="step-text">Then I should see "Refleksi Kompetensi"</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                <div class="step-item skipped">
                    <div class="step-icon">⏭️</div>
                    <div class="step-content">
                        <div class="step-text">When I scroll to the "Inspirasi Pembelajaran" section</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                <div class="step-item skipped">
                    <div class="step-icon">⏭️</div>
                    <div class="step-content">
                        <div class="step-text">Then I should see "Inspirasi Pembelajaran"</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                <div class="step-item skipped">
                    <div class="step-icon">⏭️</div>
                    <div class="step-content">
                        <div class="step-text">Then I should see "Perangkat Ajar"</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                <div class="step-item skipped">
                    <div class="step-icon">⏭️</div>
                    <div class="step-content">
                        <div class="step-text">Then I should see "CP/ATP"</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                <div class="step-item skipped">
                    <div class="step-icon">⏭️</div>
                    <div class="step-content">
                        <div class="step-text">Then I should see "Ide Praktik"</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                <div class="step-item skipped">
                    <div class="step-icon">⏭️</div>
                    <div class="step-content">
                        <div class="step-text">Then I should see "Bukti Karya"</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                <div class="step-item skipped">
                    <div class="step-icon">⏭️</div>
                    <div class="step-content">
                        <div class="step-text">Then I should see "Video Inspirasi"</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                <div class="step-item skipped">
                    <div class="step-icon">⏭️</div>
                    <div class="step-content">
                        <div class="step-text">Then I should see "Asesmen (Asesmen Murid & AKM Kelas)"</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                <div class="step-item skipped">
                    <div class="step-icon">⏭️</div>
                    <div class="step-content">
                        <div class="step-text">Then I should see "Kelas"</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                <div class="step-item skipped">
                    <div class="step-icon">⏭️</div>
                    <div class="step-content">
                        <div class="step-text">When I scroll to the "Dokumen dan Regulasi Rujukan" section</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                <div class="step-item skipped">
                    <div class="step-icon">⏭️</div>
                    <div class="step-content">
                        <div class="step-text">Then I should see "Dokumen dan Regulasi Rujukan"</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                <div class="step-item skipped">
                    <div class="step-icon">⏭️</div>
                    <div class="step-content">
                        <div class="step-text">Then I should see "Pengelolaan Pembelajaran"</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                <div class="step-item skipped">
                    <div class="step-icon">⏭️</div>
                    <div class="step-content">
                        <div class="step-text">Then I should see "Pengelolaan Kinerja"</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                <div class="step-item skipped">
                    <div class="step-icon">⏭️</div>
                    <div class="step-content">
                        <div class="step-text">Then I should see "Peningkatan Kompetensi"</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                <div class="step-item skipped">
                    <div class="step-icon">⏭️</div>
                    <div class="step-content">
                        <div class="step-text">Then I should see "Pengelolaan Satuan Pendidikan"</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                </div>
            </div>
            
            <div class="scenario-item" data-status="passed">
                <div class="scenario-header" onclick="toggleScenario('TC4')">
                    <div class="scenario-title">
                        <span class="scenario-icon">✅</span>
                        Parallel Test: [TC4]
                    </div>
                    <div class="scenario-meta">
                        <span class="scenario-duration">0.5s</span>
                        <span class="scenario-arrow">▼</span>
                    </div>
                </div>
                <div class="scenario-content" id="content-TC4">
                    
                <div class="step-item passed">
                    <div class="step-icon">✅</div>
                    <div class="step-content">
                        <div class="step-text">Given I am on the main page</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                <div class="step-item skipped">
                    <div class="step-icon">⏭️</div>
                    <div class="step-content">
                        <div class="step-text">When I scroll to the "Ruang Murid" section</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                <div class="step-item skipped">
                    <div class="step-icon">⏭️</div>
                    <div class="step-content">
                        <div class="step-text">Then I should see "Ruang Murid"</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                <div class="step-item skipped">
                    <div class="step-icon">⏭️</div>
                    <div class="step-content">
                        <div class="step-text">And I click "Ruang Murid"</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                <div class="step-item skipped">
                    <div class="step-icon">⏭️</div>
                    <div class="step-content">
                        <div class="step-text">Then I should see "Layanan yang Tersedia"</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                <div class="step-item skipped">
                    <div class="step-icon">⏭️</div>
                    <div class="step-content">
                        <div class="step-text">Then I should see "Sumber Belajar"</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                <div class="step-item skipped">
                    <div class="step-icon">⏭️</div>
                    <div class="step-content">
                        <div class="step-text">Then I should see "Buku Bacaan Digital"</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                <div class="step-item skipped">
                    <div class="step-icon">⏭️</div>
                    <div class="step-content">
                        <div class="step-text">Then I should see "Akun Pendidikan"</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                <div class="step-item skipped">
                    <div class="step-icon">⏭️</div>
                    <div class="step-content">
                        <div class="step-text">Then I should see "Bank Soal"</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                <div class="step-item skipped">
                    <div class="step-icon">⏭️</div>
                    <div class="step-content">
                        <div class="step-text">Then I should see "Rapor Digital"</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                <div class="step-item skipped">
                    <div class="step-icon">⏭️</div>
                    <div class="step-content">
                        <div class="step-text">Then I should see "Riwayat Pendidikan"</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                <div class="step-item skipped">
                    <div class="step-icon">⏭️</div>
                    <div class="step-content">
                        <div class="step-text">Then I should see "Sumber Buku Teks Pembelajaran"</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                <div class="step-item skipped">
                    <div class="step-icon">⏭️</div>
                    <div class="step-content">
                        <div class="step-text">Then I should see "Pendidikan Jarak Jauh"</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                </div>
            </div>
            
            <div class="scenario-item" data-status="passed">
                <div class="scenario-header" onclick="toggleScenario('TC5')">
                    <div class="scenario-title">
                        <span class="scenario-icon">✅</span>
                        Parallel Test: [TC5]
                    </div>
                    <div class="scenario-meta">
                        <span class="scenario-duration">0.4s</span>
                        <span class="scenario-arrow">▼</span>
                    </div>
                </div>
                <div class="scenario-content" id="content-TC5">
                    
                <div class="step-item passed">
                    <div class="step-icon">✅</div>
                    <div class="step-content">
                        <div class="step-text">Given I am on the main page</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                <div class="step-item skipped">
                    <div class="step-icon">⏭️</div>
                    <div class="step-content">
                        <div class="step-text">When I scroll to the "Ruang Sekolah" section</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                <div class="step-item skipped">
                    <div class="step-icon">⏭️</div>
                    <div class="step-content">
                        <div class="step-text">Then I should see "Ruang Sekolah"</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                <div class="step-item skipped">
                    <div class="step-icon">⏭️</div>
                    <div class="step-content">
                        <div class="step-text">And I click "Ruang Sekolah"</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                <div class="step-item skipped">
                    <div class="step-icon">⏭️</div>
                    <div class="step-content">
                        <div class="step-text">Then I should see "Layanan yang Tersedia"</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                <div class="step-item skipped">
                    <div class="step-icon">⏭️</div>
                    <div class="step-content">
                        <div class="step-text">Then I should see "Profil Sekolah"</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                <div class="step-item skipped">
                    <div class="step-icon">⏭️</div>
                    <div class="step-content">
                        <div class="step-text">Then I should see "Rapor Satuan Pendidikan"</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                <div class="step-item skipped">
                    <div class="step-icon">⏭️</div>
                    <div class="step-content">
                        <div class="step-text">Then I should see "Rencana Kegiatan dan Belanja Sekolah"</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                <div class="step-item skipped">
                    <div class="step-icon">⏭️</div>
                    <div class="step-content">
                        <div class="step-text">Then I should see "Akun Pendidikan"</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                <div class="step-item skipped">
                    <div class="step-icon">⏭️</div>
                    <div class="step-content">
                        <div class="step-text">Then I should see "Pengadaan Barang dan Jasa Sekolah"</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                </div>
            </div>
            
            <div class="scenario-item" data-status="passed">
                <div class="scenario-header" onclick="toggleScenario('TC6')">
                    <div class="scenario-title">
                        <span class="scenario-icon">✅</span>
                        Parallel Test: [TC6]
                    </div>
                    <div class="scenario-meta">
                        <span class="scenario-duration">0.5s</span>
                        <span class="scenario-arrow">▼</span>
                    </div>
                </div>
                <div class="scenario-content" id="content-TC6">
                    
                <div class="step-item passed">
                    <div class="step-icon">✅</div>
                    <div class="step-content">
                        <div class="step-text">Given I am on the main page</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                <div class="step-item skipped">
                    <div class="step-icon">⏭️</div>
                    <div class="step-content">
                        <div class="step-text">When I scroll to the "Ruang Bahasa" section</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                <div class="step-item skipped">
                    <div class="step-icon">⏭️</div>
                    <div class="step-content">
                        <div class="step-text">Then I should see "Ruang Bahasa"</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                <div class="step-item skipped">
                    <div class="step-icon">⏭️</div>
                    <div class="step-content">
                        <div class="step-text">And I click "Ruang Bahasa"</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                <div class="step-item skipped">
                    <div class="step-icon">⏭️</div>
                    <div class="step-content">
                        <div class="step-text">Then I should see "Layanan yang Tersedia"</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                <div class="step-item skipped">
                    <div class="step-icon">⏭️</div>
                    <div class="step-content">
                        <div class="step-text">Then I should see "Kamus Bahasa"</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                <div class="step-item skipped">
                    <div class="step-icon">⏭️</div>
                    <div class="step-content">
                        <div class="step-text">Then I should see "Penerjemahan Daring"</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                <div class="step-item skipped">
                    <div class="step-icon">⏭️</div>
                    <div class="step-content">
                        <div class="step-text">Then I should see "Layanan UKBI"</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                <div class="step-item skipped">
                    <div class="step-icon">⏭️</div>
                    <div class="step-content">
                        <div class="step-text">Then I should see "BIPA Daring"</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                </div>
            </div>
            
            <div class="scenario-item" data-status="passed">
                <div class="scenario-header" onclick="toggleScenario('TC7')">
                    <div class="scenario-title">
                        <span class="scenario-icon">✅</span>
                        Parallel Test: [TC7]
                    </div>
                    <div class="scenario-meta">
                        <span class="scenario-duration">0.4s</span>
                        <span class="scenario-arrow">▼</span>
                    </div>
                </div>
                <div class="scenario-content" id="content-TC7">
                    
                <div class="step-item passed">
                    <div class="step-icon">✅</div>
                    <div class="step-content">
                        <div class="step-text">Given I am on the main page</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                <div class="step-item skipped">
                    <div class="step-icon">⏭️</div>
                    <div class="step-content">
                        <div class="step-text">When I scroll to the "Ruang Pemerintah" section</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                <div class="step-item skipped">
                    <div class="step-icon">⏭️</div>
                    <div class="step-content">
                        <div class="step-text">Then I should see "Ruang Pemerintah"</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                <div class="step-item skipped">
                    <div class="step-icon">⏭️</div>
                    <div class="step-content">
                        <div class="step-text">And I click "Ruang Pemerintah"</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                <div class="step-item skipped">
                    <div class="step-icon">⏭️</div>
                    <div class="step-content">
                        <div class="step-text">Then I should see "Layanan yang Tersedia"</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                <div class="step-item skipped">
                    <div class="step-icon">⏭️</div>
                    <div class="step-content">
                        <div class="step-text">Then I should see "Neraca Pendidikan Daerah"</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                <div class="step-item skipped">
                    <div class="step-icon">⏭️</div>
                    <div class="step-content">
                        <div class="step-text">Then I should see "Akun Pendidikan"</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                <div class="step-item skipped">
                    <div class="step-icon">⏭️</div>
                    <div class="step-content">
                        <div class="step-text">Then I should see "Rapor Pendidikan Daerah"</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                <div class="step-item skipped">
                    <div class="step-icon">⏭️</div>
                    <div class="step-content">
                        <div class="step-text">Then I should see "Manajemen Aplikasi Rencana Kegiatan dan Anggaran Sekolah"</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                </div>
            </div>
            
            <div class="scenario-item" data-status="passed">
                <div class="scenario-header" onclick="toggleScenario('TC8')">
                    <div class="scenario-title">
                        <span class="scenario-icon">✅</span>
                        Parallel Test: [TC8]
                    </div>
                    <div class="scenario-meta">
                        <span class="scenario-duration">0.4s</span>
                        <span class="scenario-arrow">▼</span>
                    </div>
                </div>
                <div class="scenario-content" id="content-TC8">
                    
                <div class="step-item passed">
                    <div class="step-icon">✅</div>
                    <div class="step-content">
                        <div class="step-text">Given I am on the main page</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                <div class="step-item skipped">
                    <div class="step-icon">⏭️</div>
                    <div class="step-content">
                        <div class="step-text">When I scroll to the "Ruang Mitra" section</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                <div class="step-item skipped">
                    <div class="step-icon">⏭️</div>
                    <div class="step-content">
                        <div class="step-text">Then I should see "Ruang Mitra"</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                <div class="step-item skipped">
                    <div class="step-icon">⏭️</div>
                    <div class="step-content">
                        <div class="step-text">And I click "Ruang Mitra"</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                <div class="step-item skipped">
                    <div class="step-icon">⏭️</div>
                    <div class="step-content">
                        <div class="step-text">Then I should see "Layanan yang Tersedia"</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                <div class="step-item skipped">
                    <div class="step-icon">⏭️</div>
                    <div class="step-content">
                        <div class="step-text">Then I should see "Mitra Barjas Pendidikan"</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                <div class="step-item skipped">
                    <div class="step-icon">⏭️</div>
                    <div class="step-content">
                        <div class="step-text">Then I should see "Kolaborasi Pendidikan"</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                <div class="step-item skipped">
                    <div class="step-icon">⏭️</div>
                    <div class="step-content">
                        <div class="step-text">Then I should see "Relawan Pendidikan"</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                </div>
            </div>
            
            <div class="scenario-item" data-status="passed">
                <div class="scenario-header" onclick="toggleScenario('TC9')">
                    <div class="scenario-title">
                        <span class="scenario-icon">✅</span>
                        Parallel Test: [TC9]
                    </div>
                    <div class="scenario-meta">
                        <span class="scenario-duration">0.4s</span>
                        <span class="scenario-arrow">▼</span>
                    </div>
                </div>
                <div class="scenario-content" id="content-TC9">
                    
                <div class="step-item passed">
                    <div class="step-icon">✅</div>
                    <div class="step-content">
                        <div class="step-text">Given I am on the main page</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                <div class="step-item skipped">
                    <div class="step-icon">⏭️</div>
                    <div class="step-content">
                        <div class="step-text">When I scroll to the "Ruang Publik" section</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                <div class="step-item skipped">
                    <div class="step-icon">⏭️</div>
                    <div class="step-content">
                        <div class="step-text">Then I should see "Ruang Publik"</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                <div class="step-item skipped">
                    <div class="step-icon">⏭️</div>
                    <div class="step-content">
                        <div class="step-text">And I click "Ruang Publik"</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                <div class="step-item skipped">
                    <div class="step-icon">⏭️</div>
                    <div class="step-content">
                        <div class="step-text">Then I should see "Layanan yang Tersedia"</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                <div class="step-item skipped">
                    <div class="step-icon">⏭️</div>
                    <div class="step-content">
                        <div class="step-text">Then I should see "Pusat Perbukuan"</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                <div class="step-item skipped">
                    <div class="step-icon">⏭️</div>
                    <div class="step-content">
                        <div class="step-text">Then I should see "Bantuan Pendidikan"</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                <div class="step-item skipped">
                    <div class="step-icon">⏭️</div>
                    <div class="step-content">
                        <div class="step-text">Then I should see "Layanan Informasi dan Pengaduan"</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                <div class="step-item skipped">
                    <div class="step-icon">⏭️</div>
                    <div class="step-content">
                        <div class="step-text">Then I should see "Informasi Data Pendidikan"</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                <div class="step-item skipped">
                    <div class="step-icon">⏭️</div>
                    <div class="step-content">
                        <div class="step-text">Then I should see "Publikasi Ilmiah"</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                <div class="step-item skipped">
                    <div class="step-icon">⏭️</div>
                    <div class="step-content">
                        <div class="step-text">Then I should see "Produk Hukum"</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                </div>
            </div>
            
        </div>
        
        <div class="footer">
            <p>Maintained by Test Automation Team. Find us on:</p>
            <div class="social-icons">
                <a href="https://github.com" target="_blank">🐙</a>
                <a href="https://twitter.com" target="_blank">🐦</a>
                <a href="https://linkedin.com" target="_blank">💼</a>
            </div>
        </div>
    </div>
</body>
</html>
        