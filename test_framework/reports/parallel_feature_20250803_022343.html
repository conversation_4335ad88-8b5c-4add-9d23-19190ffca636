
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Feature: Parallel Test Execution</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }
        .header h2 {
            margin: 10px 0 0 0;
            font-size: 1.2em;
            font-weight: 300;
            opacity: 0.9;
        }
        .nav-bar {
            background: #f8f9fa;
            padding: 15px 30px;
            border-bottom: 1px solid #eee;
        }
        .back-button {
            background: #007bff;
            color: white;
            padding: 8px 16px;
            border-radius: 5px;
            text-decoration: none;
            display: inline-block;
            margin-bottom: 15px;
        }
        .tags {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
        }
        .tag {
            background: #e9ecef;
            color: #495057;
            padding: 5px 12px;
            border-radius: 20px;
            text-decoration: none;
            font-size: 0.9em;
        }
        .tag:hover {
            background: #007bff;
            color: white;
        }
        .feature-summary {
            padding: 30px;
            background: #f8f9fa;
        }
        .feature-details {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 30px;
            align-items: stretch;
        }
        .feature-info {
            background: white;
            border-radius: 10px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            height: 100%;
            display: flex;
            flex-direction: column;
        }
        .feature-info h3 {
            margin: 0 0 20px 0;
            color: #333;
            font-size: 1.5em;
        }
        .donut-chart {
            width: 150px;
            height: 150px;
            border-radius: 50%;
            margin: 0 auto 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.8em;
            font-weight: bold;
            color: white;
            position: relative;
            cursor: pointer;
            background: conic-gradient(#28a745 0deg 360.0deg, #dc3545 360.0deg 360deg);
        }
        .donut-chart::before {
            content: '';
            position: absolute;
            width: 90px;
            height: 90px;
            border-radius: 50%;
            background: white;
            z-index: 1;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }
        .donut-chart::after {
            content: '1';
            position: absolute;
            z-index: 2;
            color: #333;
            font-size: 1.5em;
            font-weight: bold;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }
        .donut-chart.passed {
            background: conic-gradient(#28a745 0deg 360deg);
        }
        .donut-chart.failed {
            background: conic-gradient(#dc3545 0deg 360deg);
        }
        .donut-chart.mixed {
            background: conic-gradient(#28a745 0deg 360.0deg, #dc3545 360.0deg 360deg);
        }
        .passed-tooltip, .failed-tooltip {
            position: fixed;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 6px 10px;
            border-radius: 4px;
            font-size: 0.7em;
            white-space: nowrap;
            pointer-events: none;
            opacity: 0;
            transition: opacity 0.3s;
            z-index: 1000;
        }
        .passed-tooltip {
            border-left: 3px solid #28a745;
        }
        .failed-tooltip {
            border-left: 3px solid #dc3545;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            text-align: left;
        }
        .stat-item {
            display: flex;
            justify-content: space-between;
            padding: 5px 0;
        }
        .stat-label {
            font-weight: 500;
            color: #666;
        }
        .stat-value {
            font-weight: bold;
            color: #333;
        }
        .metadata {
            background: white;
            border-radius: 10px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            height: 100%;
            display: flex;
            flex-direction: column;
        }
        .metadata h3 {
            margin: 0 0 20px 0;
            color: #333;
            font-size: 1.3em;
        }
        .filter-bar {
            background: white;
            padding: 20px 30px;
            border-bottom: 1px solid #eee;
        }
        .filter-options {
            display: flex;
            gap: 20px;
        }
        .filter-option {
            padding: 8px 16px;
            border-radius: 5px;
            text-decoration: none;
            color: #666;
            background: #f8f9fa;
        }
        .filter-option.active {
            background: #007bff;
            color: white;
        }
        .scenarios {
            padding: 30px;
        }
        .scenario-item {
            background: white;
            border-radius: 10px;
            margin-bottom: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            overflow: hidden;
        }
        .scenario-header {
            padding: 20px;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: #f8f9fa;
        }
        .scenario-header:hover {
            background: #e9ecef;
        }
        .scenario-title {
            display: flex;
            align-items: center;
            gap: 10px;
            font-weight: 600;
            color: #333;
        }
        .scenario-icon {
            font-size: 1.2em;
        }
        .scenario-meta {
            display: flex;
            align-items: center;
            gap: 15px;
            color: #666;
        }
        .scenario-duration {
            font-size: 0.9em;
        }
        .scenario-arrow {
            transition: transform 0.3s;
        }
        .scenario-content {
            display: none;
            padding: 20px;
            border-top: 1px solid #eee;
        }
        .scenario-content.expanded {
            display: block;
        }
        .step-item {
            display: flex;
            align-items: flex-start;
            gap: 15px;
            padding: 15px 0;
            border-bottom: 1px solid #f0f0f0;
        }
        .step-item:last-child {
            border-bottom: none;
        }
        .step-icon {
            font-size: 1.2em;
            margin-top: 2px;
        }
        .step-content {
            flex: 1;
        }
        .step-text {
            font-weight: 500;
            color: #333;
            margin-bottom: 5px;
        }
        .step-details {
            font-size: 0.9em;
            color: #666;
        }
        .step-item.passed .step-icon {
            color: #28a745;
        }
        .step-item.failed .step-icon {
            color: #dc3545;
        }
        .step-item.skipped .step-icon {
            color: #ffc107;
        }
        .footer {
            background: #f8f9fa;
            padding: 20px;
            text-align: center;
            color: #666;
            border-top: 1px solid #eee;
        }
        .social-icons {
            margin-top: 10px;
        }
        .social-icons a {
            margin: 0 10px;
            color: #666;
            text-decoration: none;
            font-size: 1.2em;
        }
    </style>
    <script>
        function toggleScenario(scenarioId) {
            const content = document.getElementById('content-' + scenarioId);
            const arrow = content.previousElementSibling.querySelector('.scenario-arrow');
            
            if (content.classList.contains('expanded')) {
                content.classList.remove('expanded');
                arrow.style.transform = 'rotate(0deg)';
            } else {
                content.classList.add('expanded');
                arrow.style.transform = 'rotate(180deg)';
            }
        }
        
        function showPassedTooltip(event, passedText) {
            const tooltip = document.getElementById('passed-tooltip');
            const rect = event.target.getBoundingClientRect();
            tooltip.innerHTML = passedText;
            tooltip.style.left = (rect.left + rect.width/2 - tooltip.offsetWidth/2) + 'px';
            tooltip.style.top = (rect.top - tooltip.offsetHeight - 15) + 'px';
            tooltip.style.opacity = '1';
        }
        
        function showFailedTooltip(event, failedText) {
            const tooltip = document.getElementById('failed-tooltip');
            const rect = event.target.getBoundingClientRect();
            tooltip.innerHTML = failedText;
            tooltip.style.left = (rect.left + rect.width/2 - tooltip.offsetWidth/2) + 'px';
            tooltip.style.top = (rect.top - tooltip.offsetHeight - 15) + 'px';
            tooltip.style.opacity = '1';
        }
        
        function hideTooltip() {
            document.getElementById('passed-tooltip').style.opacity = '0';
            document.getElementById('failed-tooltip').style.opacity = '0';
        }
        
        function handleDonutHover(event, passedTests, failedTests) {
            const rect = event.target.getBoundingClientRect();
            const centerX = rect.left + rect.width / 2;
            const centerY = rect.top + rect.height / 2;
            const mouseX = event.clientX;
            const mouseY = event.clientY;
            
            // Calculate angle from center to mouse position
            const deltaX = mouseX - centerX;
            const deltaY = centerY - mouseY; // Invert Y axis
            const angle = Math.atan2(deltaY, deltaX) * 180 / Math.PI;
            const normalizedAngle = (angle + 360) % 360;
            
            // Calculate passed section angle
            const passedAngle = (passedTests / (passedTests + failedTests)) * 360;
            
            // Determine which section is being hovered
            if (normalizedAngle <= passedAngle) {
                // Hovering over passed section (green)
                showPassedTooltip(event, `Passed: ${passedTests}`);
                document.getElementById('failed-tooltip').style.opacity = '0';
            } else {
                // Hovering over failed section (red)
                showFailedTooltip(event, `Failed: ${failedTests}`);
                document.getElementById('passed-tooltip').style.opacity = '0';
            }
        }
        
        function filterScenarios(filter, event) {
            // Update active filter button
            document.querySelectorAll('.filter-option').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');
            
            // Filter scenarios
            const scenarios = document.querySelectorAll('.scenario-item');
            scenarios.forEach(scenario => {
                const status = scenario.getAttribute('data-status');
                if (filter === 'all' || status === filter) {
                    scenario.style.display = 'block';
                } else {
                    scenario.style.display = 'none';
                }
            });
        }
        
        // Expand first scenario by default
        window.onload = function() {
            const firstScenario = document.querySelector('.scenario-item');
            if (firstScenario) {
                const scenarioId = firstScenario.querySelector('.scenario-header').getAttribute('onclick').match(/'([^']+)'/)[1];
                toggleScenario(scenarioId);
            }
        };
    </script>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Rumah Pendidikan Automation Report</h1>
            <h2>Comprehensive Test Automation Report</h2>
        </div>
        
        <div class="nav-bar">
            <a href="parallel_comprehensive_20250803_022343.html" class="back-button">← Features Overview</a>
            <div class="tags">
                <a href="#TC13" class="tag">TC13</a>
            </div>
        </div>
        
        <div class="feature-summary">
            <div class="feature-details">
                <div class="feature-info">
                    <h3>Feature: Parallel Test Execution</h3>
                    <p><strong>Description:</strong> Regression Test</p>
                    <p><strong>File name:</strong> parallel_execution.feature</p>
                    
                    <div class="donut-chart passed" 
                         onmousemove="handleDonutHover(event, 1, 0)" 
                         onmouseout="hideTooltip()">
                        <div class="passed-tooltip" id="passed-tooltip"></div>
                        <div class="failed-tooltip" id="failed-tooltip"></div>
                    </div>
                    
                    <div class="stats-grid">
                        <div class="stat-item">
                            <span class="stat-label">STATUS</span>
                            <span class="stat-value">✅ Passed</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">PROGRESS</span>
                            <span class="stat-value">100.0%</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">STATUS</span>
                            <span class="stat-value">❌ Failed</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">PROGRESS</span>
                            <span class="stat-value">0.0%</span>
                        </div>
                    </div>
                </div>
                
                <div class="metadata">
                    <h3>Metadata</h3>
                    <div class="stat-item">
                        <span class="stat-label">Device</span>
                        <span class="stat-value">Local Machine</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">OS</span>
                        <span class="stat-value">macOS</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">Browser</span>
                        <span class="stat-value">Chrome 103</span>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="filter-bar">
            <div class="filter-options">
                <a href="#" class="filter-option active" onclick="filterScenarios('all', event)">All (1)</a>
                <a href="#" class="filter-option" onclick="filterScenarios('passed', event)">Passed (1)</a>
                <a href="#" class="filter-option" onclick="filterScenarios('failed', event)">Failed (0)</a>
            </div>
        </div>
        
        <div class="scenarios">
            
            <div class="scenario-item" data-status="passed">
                <div class="scenario-header" onclick="toggleScenario('TC13')">
                    <div class="scenario-title">
                        <span class="scenario-icon">✅</span>
                        Parallel Test: [TC13]
                    </div>
                    <div class="scenario-meta">
                        <span class="scenario-duration">0.6s</span>
                        <span class="scenario-arrow">▼</span>
                    </div>
                </div>
                <div class="scenario-content" id="content-TC13">
                    
                <div class="step-item passed">
                    <div class="step-icon">✅</div>
                    <div class="step-content">
                        <div class="step-text">Given I am on the main page</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                <div class="step-item skipped">
                    <div class="step-icon">⏭️</div>
                    <div class="step-content">
                        <div class="step-text">When I scroll to the "Pertanyaan yang paling sering ditanyakan" section</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                <div class="step-item skipped">
                    <div class="step-icon">⏭️</div>
                    <div class="step-content">
                        <div class="step-text">And I click button beside "Apa manfaat menggunakan Rumah Pendidikan?"</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                <div class="step-item skipped">
                    <div class="step-icon">⏭️</div>
                    <div class="step-content">
                        <div class="step-text">Then I should see "manfaaat"</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                <div class="step-item skipped">
                    <div class="step-icon">⏭️</div>
                    <div class="step-content">
                        <div class="step-text">And I click button beside "Bagaimana cara mengakses Rumah Pendidikan?"</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                <div class="step-item skipped">
                    <div class="step-icon">⏭️</div>
                    <div class="step-content">
                        <div class="step-text">Then I should see "cara mengakses"</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                <div class="step-item skipped">
                    <div class="step-icon">⏭️</div>
                    <div class="step-content">
                        <div class="step-text">And I click button beside "Apakah pengguna harus menggunakan Akun belajar.id untuk mengakses Rumah Pendidikan?"</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                <div class="step-item skipped">
                    <div class="step-icon">⏭️</div>
                    <div class="step-content">
                        <div class="step-text">Then I should see "masyarakat umum"</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                <div class="step-item skipped">
                    <div class="step-icon">⏭️</div>
                    <div class="step-content">
                        <div class="step-text">And I click button beside "Siapa saja pengguna yang disarankan untuk menggunakan platform Rumah Pendidikan"</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                <div class="step-item skipped">
                    <div class="step-icon">⏭️</div>
                    <div class="step-content">
                        <div class="step-text">Then I should see "pengguna yang disarankan"</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                <div class="step-item skipped">
                    <div class="step-icon">⏭️</div>
                    <div class="step-content">
                        <div class="step-text">And I click button beside "Apa perbedaan rumah pendidikan dengan platform digital pendidikan milik Kemendikdasmen lainnya?"</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                <div class="step-item skipped">
                    <div class="step-icon">⏭️</div>
                    <div class="step-content">
                        <div class="step-text">Then I should see "perbedaan"</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                <div class="step-item skipped">
                    <div class="step-icon">⏭️</div>
                    <div class="step-content">
                        <div class="step-text">And I click button "Ke Pusat Informasi"</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                <div class="step-item skipped">
                    <div class="step-icon">⏭️</div>
                    <div class="step-content">
                        <div class="step-text">Then user direct to page "https://pusatinformasi.rumahpendidikan.kemendikdasmen.go.id/hc/id"</div>
                        <div class="step-details">Step executed successfully</div>
                    </div>
                </div>
                
                </div>
            </div>
            
        </div>
        
        <div class="footer">
            <p>Maintained by Test Automation Team. Find us on:</p>
            <div class="social-icons">
                <a href="https://github.com" target="_blank">🐙</a>
                <a href="https://twitter.com" target="_blank">🐦</a>
                <a href="https://linkedin.com" target="_blank">💼</a>
            </div>
        </div>
    </div>
</body>
</html>
        