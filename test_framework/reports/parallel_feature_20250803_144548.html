
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>R<PERSON>h Pendidikan Automation Report</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
            position: relative;
        }
        .back-button {
            position: absolute;
            left: 30px;
            top: 50%;
            transform: translateY(-50%);
            background: rgba(255,255,255,0.2);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            text-decoration: none;
            font-size: 0.9em;
            transition: background 0.3s;
        }
        .back-button:hover {
            background: rgba(255,255,255,0.3);
        }
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }
        .header h2 {
            margin: 10px 0 0 0;
            font-size: 1.2em;
            font-weight: 300;
            opacity: 0.9;
        }
        .feature-section {
            background: white;
            padding: 30px;
            border-bottom: 1px solid #eee;
        }
        .feature-tags {
            margin-bottom: 20px;
        }
        .tag {
            display: inline-block;
            background: #007bff;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            margin: 2px;
            font-size: 0.8em;
            text-decoration: none;
        }
        .feature-info {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            align-items: start;
        }
        .feature-details {
            background: white;
            border-radius: 10px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            height: 100%;
            display: flex;
            flex-direction: column;
        }
        .feature-info h3 {
            margin: 0 0 20px 0;
            color: #333;
            font-size: 1.5em;
        }
        .donut-chart {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            margin: 0 auto 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.4em;
            font-weight: bold;
            color: white;
            position: relative;
            cursor: pointer;
            background: conic-gradient(#28a745 0deg 360.0deg, #dc3545 360.0deg 360deg);
        }
        .donut-chart::before {
            content: '';
            position: absolute;
            width: 70px;
            height: 70px;
            border-radius: 50%;
            background: white;
            z-index: 1;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }
        .donut-chart::after {
            content: '1';
            position: absolute;
            z-index: 2;
            color: #333;
            font-size: 1.2em;
            font-weight: bold;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }
        .donut-chart.passed {
            background: conic-gradient(#28a745 0deg 360deg);
        }
        .donut-chart.failed {
            background: conic-gradient(#dc3545 0deg 360deg);
        }
        .donut-chart.mixed {
            background: conic-gradient(#28a745 0deg 360.0deg, #dc3545 360.0deg 360deg);
        }
        .passed-tooltip, .failed-tooltip {
            position: fixed;
            background: rgba(0,0,0,0.9);
            color: white;
            padding: 6px 10px;
            border-radius: 4px;
            font-size: 0.7em;
            font-weight: 500;
            white-space: nowrap;
            pointer-events: none;
            opacity: 0;
            transition: opacity 0.2s ease;
            z-index: 1000;
            box-shadow: 0 3px 8px rgba(0,0,0,0.3);
            backdrop-filter: blur(4px);
        }
        .passed-tooltip {
            border-left: 3px solid #28a745;
        }
        .failed-tooltip {
            border-left: 3px solid #dc3545;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            text-align: left;
        }
        .stat-item {
            display: flex;
            justify-content: space-between;
            padding: 5px 0;
        }
        .stat-label {
            font-weight: 500;
            color: #666;
        }
        .stat-value {
            font-weight: bold;
            color: #333;
        }
        .metadata {
            background: white;
            border-radius: 10px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            height: 100%;
            display: flex;
            flex-direction: column;
        }
        .metadata h3 {
            margin: 0 0 20px 0;
            color: #333;
            font-size: 1.3em;
        }
        .filter-bar {
            background: white;
            padding: 20px 30px;
            border-bottom: 1px solid #eee;
        }
        .filter-options {
            display: flex;
            gap: 20px;
        }
        .filter-option {
            padding: 8px 16px;
            border-radius: 5px;
            text-decoration: none;
            color: #666;
            background: #f8f9fa;
        }
        .filter-option.active {
            background: #007bff;
            color: white;
        }
        .scenarios {
            padding: 30px;
        }
        .scenario-item {
            background: white;
            border-radius: 10px;
            margin-bottom: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            overflow: hidden;
        }
        .scenario-header {
            padding: 20px;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: #f8f9fa;
        }
        .scenario-header:hover {
            background: #e9ecef;
        }
        .scenario-title {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .status-icon {
            font-size: 1.2em;
        }
        .scenario-name {
            font-weight: 600;
            color: #333;
        }
        .scenario-meta {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        .execution-time {
            color: #666;
            font-size: 0.9em;
        }
        .arrow {
            font-size: 0.8em;
            color: #666;
            transition: transform 0.3s;
        }
        .scenario-content {
            padding: 20px;
            background: white;
        }
        .steps-container {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }
        .step-item {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            border-left: 4px solid #28a745;
        }
        .step-item.failed {
            border-left-color: #dc3545;
        }
        .step-item.skipped {
            border-left-color: #ffc107;
        }
        .step-header {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 8px;
        }
        .step-icon {
            font-size: 1.1em;
        }
        .step-text {
            flex: 1;
            font-weight: 500;
            color: #333;
        }
        .step-time {
            color: #666;
            font-size: 0.9em;
        }
        .step-execution {
            background: #e3f2fd;
            color: #1976d2;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8em;
            font-weight: 500;
            display: inline-block;
            border: 1px solid #bbdefb;
        }
        .error-details {
            margin-top: 10px;
            background: #fff5f5;
            border: 1px solid #fed7d7;
            border-radius: 6px;
            overflow: hidden;
        }
        .error-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 10px 15px;
            background: #fed7d7;
            cursor: pointer;
        }
        .error-icon {
            margin-right: 8px;
        }
        .error-text {
            flex: 1;
            font-weight: 500;
            color: #c53030;
        }
        .quick-show-btn {
            background: #c53030;
            color: white;
            border: none;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.7em;
            cursor: pointer;
        }
        .quick-show-btn:hover {
            background: #a02323;
        }
        .error-content {
            padding: 15px;
        }
        .error-comparison {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }
        .expected-actual {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        .expected, .actual {
            padding: 10px;
            border-radius: 4px;
        }
        .expected {
            background: #f0fff4;
            border: 1px solid #9ae6b4;
        }
        .actual {
            background: #fff5f5;
            border: 1px solid #feb2b2;
        }
        .expected strong, .actual strong {
            display: block;
            margin-bottom: 5px;
            font-size: 0.9em;
        }
        .expected .value, .actual .value {
            font-family: monospace;
            font-size: 0.8em;
            word-break: break-all;
        }
        .footer {
            background: #f8f9fa;
            padding: 20px;
            text-align: center;
            color: #666;
        }
    </style>
    <script>
        function toggleScenario(scenarioId) {
            const content = document.getElementById(scenarioId);
            const header = content.previousElementSibling;
            const arrow = header.querySelector('.arrow');
            
            if (content.style.display === 'none') {
                content.style.display = 'block';
                arrow.style.transform = 'rotate(180deg)';
            } else {
                content.style.display = 'none';
                arrow.style.transform = 'rotate(0deg)';
            }
        }
        
        function showPassedTooltip(event, passedText) {
            const tooltip = document.getElementById('passed-tooltip');
            tooltip.innerHTML = passedText;
            tooltip.style.left = (event.clientX + 10) + 'px';
            tooltip.style.top = (event.clientY - 30) + 'px';
            tooltip.style.opacity = '1';
        }
        
        function showFailedTooltip(event, failedText) {
            const tooltip = document.getElementById('failed-tooltip');
            tooltip.innerHTML = failedText;
            tooltip.style.left = (event.clientX + 10) + 'px';
            tooltip.style.top = (event.clientY - 30) + 'px';
            tooltip.style.opacity = '1';
        }
        
        function hideTooltip() {
            document.getElementById('passed-tooltip').style.opacity = '0';
            document.getElementById('failed-tooltip').style.opacity = '0';
        }
        
        function handleDonutHover(event, passedTests, failedTests) {
            const rect = event.target.getBoundingClientRect();
            const centerX = rect.left + rect.width / 2;
            const centerY = rect.top + rect.height / 2;
            const mouseX = event.clientX;
            const mouseY = event.clientY;
            
            // Calculate angle from center to mouse position
            const deltaX = mouseX - centerX;
            const deltaY = centerY - mouseY; // Invert Y axis
            const angle = Math.atan2(deltaY, deltaX) * 180 / Math.PI;
            const normalizedAngle = (angle + 360) % 360;
            
            // Calculate passed section angle
            const passedAngle = (passedTests / (passedTests + failedTests)) * 360;
            
            // Determine which section is being hovered
            if (normalizedAngle <= passedAngle) {
                // Hovering over passed section (green)
                showPassedTooltip(event, `Passed: ${passedTests}`);
                document.getElementById('failed-tooltip').style.opacity = '0';
            } else {
                // Hovering over failed section (red)
                showFailedTooltip(event, `Failed: ${failedTests}`);
                document.getElementById('passed-tooltip').style.opacity = '0';
            }
        }
        
        function filterScenarios(filter, event) {
            // Update active filter button
            document.querySelectorAll('.filter-option').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');
            
            // Filter scenarios
            const scenarios = document.querySelectorAll('.scenario-item');
            scenarios.forEach(scenario => {
                const status = scenario.getAttribute('data-status');
                if (filter === 'all' || status === filter) {
                    scenario.style.display = 'block';
                } else {
                    scenario.style.display = 'none';
                }
            });
        }
        
        function toggleErrorDetails(errorId) {
            const errorContent = document.getElementById(errorId);
            if (errorContent.style.display === 'none') {
                errorContent.style.display = 'block';
            } else {
                errorContent.style.display = 'none';
            }
        }
    </script>
</head>
<body>
    <div class="container">
        <div class="header">
            <a href="parallel_comprehensive_20250803_144548.html" class="back-button">← Features Overview</a>
            <h1>Rumah Pendidikan Automation Report</h1>
            <h2>Comprehensive Test Automation Report</h2>
        </div>
        
        <div class="feature-section">
            <div class="feature-tags">
                <a href="#TC13" class="tag">TC13</a>
            </div>
            <div class="feature-info">
                <div class="feature-details">
                    <h3>Feature Details</h3>
                    <p><strong>Feature:</strong> Test Execution</p>
                    <p><strong>Description:</strong> Regression Test</p>
                    <p><strong>File name:</strong> rumdik_regression_test.feature</p>
                    <p><strong>Relative path:</strong> test_framework/features/rumdik_regression_test.feature</p>
                </div>
                <div class="metadata">
                    <h3>Metadata</h3>
                    <p><strong>Device:</strong> Local Machine</p>
                    <p><strong>OS:</strong> macOS</p>
                    <p><strong>Browser:</strong> Chrome 103</p>
                </div>
            </div>
        </div>
        
        <div class="filter-bar">
            <div class="filter-options">
                <a href="#" class="filter-option active" onclick="filterScenarios('all', event)">All (1)</a>
                <a href="#" class="filter-option" onclick="filterScenarios('passed', event)">Passed (1)</a>
                <a href="#" class="filter-option" onclick="filterScenarios('failed', event)">Failed (0)</a>
            </div>
        </div>
        
        <div class="scenarios">
            
            <div class="scenario-item" data-status="passed">
                <div class="scenario-header" onclick="toggleScenario('TC13')">
                    <div class="scenario-title">
                        <span class="status-icon">✅</span>
                        <span class="scenario-name">Test Scenario: [TC13]</span>
                    </div>
                    <div class="scenario-meta">
                        <span class="execution-time">40.3s</span>
                        <span class="arrow">▼</span>
                    </div>
                </div>
                <div class="scenario-content" id="TC13" style="display: none;">
                    <div class="steps-container">
                        
                <div class="step-item passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-text">Given I am on the main page</span>
                        <span class="step-time">(I am on the main page (0.02s))</span>
                    </div>
                    <div class="step-execution">Step 1 execution</div>
                    
                </div>
                
                <div class="step-item passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-text">When I scroll to the "Pertanyaan yang paling sering ditanyakan" section</span>
                        <span class="step-time">(I scroll to the "Pertanyaan yang paling sering ditanyakan" section (0.12s))</span>
                    </div>
                    <div class="step-execution">Step 2 execution</div>
                    
                </div>
                
                <div class="step-item passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-text">And I click button beside "Apa manfaat menggunakan Rumah Pendidikan?"</span>
                        <span class="step-time">(I click button beside "Apa manfaat menggunakan Rumah Pendidikan?" (0.29s))</span>
                    </div>
                    <div class="step-execution">Step 3 execution</div>
                    
                </div>
                
                <div class="step-item passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-text">Then I should see "Pengguna dapat menemukan berbagai layanan digital pendidikan di ekosistem Kemendikdasmen dalam satu platform, tanpa perlu berpindah ke berbagai situs atau aplikasi."</span>
                        <span class="step-time">(I should see "Pengguna dapat menemukan berbagai layanan digital pendidikan di ekosistem Kemendikdasmen dalam satu platform, tanpa perlu berpindah ke berbagai situs atau aplikasi." (6.23s))</span>
                    </div>
                    <div class="step-execution">Step 4 execution</div>
                    
                </div>
                
                <div class="step-item passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-text">And I click button beside "Bagaimana cara mengakses Rumah Pendidikan?"</span>
                        <span class="step-time">(I click button beside "Bagaimana cara mengakses Rumah Pendidikan?" (0.22s))</span>
                    </div>
                    <div class="step-execution">Step 5 execution</div>
                    
                </div>
                
                <div class="step-item passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-text">Then I should see "Rumah Pendidikan dapat diakses dengan beberapa cara:"</span>
                        <span class="step-time">(I should see "Rumah Pendidikan dapat diakses dengan beberapa cara:" (3.13s))</span>
                    </div>
                    <div class="step-execution">Step 6 execution</div>
                    
                </div>
                
                <div class="step-item passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-text">And I click button beside "Apakah pengguna harus menggunakan Akun belajar.id untuk mengakses Rumah Pendidikan?"</span>
                        <span class="step-time">(I click button beside "Apakah pengguna harus menggunakan Akun belajar.id untuk mengakses Rumah Pendidikan?" (0.24s))</span>
                    </div>
                    <div class="step-execution">Step 7 execution</div>
                    
                </div>
                
                <div class="step-item passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-text">Then I should see "Tidak, Rumah Pendidikan dapat diakses oleh masyarakat umum baik dengan atau tanpa login. Namun, untuk beberapa layanan yang ada di dalam Rumah Pendidikan membutuhkan akses login menggunakan Akun belajar.id. Sehingga, apabila Anda memiliki Akun belajar.id, direkomendasikan untuk login menggunakan akun tersebut untuk memudahkan akses ke berbagai layanan di dalam Rumah Pendidikan."</span>
                        <span class="step-time">(I should see "Tidak, Rumah Pendidikan dapat diakses oleh masyarakat umum baik dengan atau tanpa login. Namun, untuk beberapa layanan yang ada di dalam Rumah Pendidikan membutuhkan akses login menggunakan Akun belajar.id. Sehingga, apabila Anda memiliki Akun belajar.id, direkomendasikan untuk login menggunakan akun tersebut untuk memudahkan akses ke berbagai layanan di dalam Rumah Pendidikan." (6.24s))</span>
                    </div>
                    <div class="step-execution">Step 8 execution</div>
                    
                </div>
                
                <div class="step-item passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-text">And I click button beside "Siapa saja pengguna yang disarankan untuk menggunakan platform Rumah Pendidikan"</span>
                        <span class="step-time">(I click button beside "Siapa saja pengguna yang disarankan untuk menggunakan platform Rumah Pendidikan" (0.23s))</span>
                    </div>
                    <div class="step-execution">Step 9 execution</div>
                    
                </div>
                
                <div class="step-item passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-text">Then I should see "Rumah Pendidikan dapat digunakan oleh seluruh lapisan masyarakat, termasuk guru, kepala sekolah, murid, tenaga kependidikan lainnya, orang tua, pemerintah daerah, mitra pendidikan, dan masyarakat umum, sesuai dengan kebutuhan masing-masing setiap pengguna."</span>
                        <span class="step-time">(I should see "Rumah Pendidikan dapat digunakan oleh seluruh lapisan masyarakat, termasuk guru, kepala sekolah, murid, tenaga kependidikan lainnya, orang tua, pemerintah daerah, mitra pendidikan, dan masyarakat umum, sesuai dengan kebutuhan masing-masing setiap pengguna." (6.24s))</span>
                    </div>
                    <div class="step-execution">Step 10 execution</div>
                    
                </div>
                
                <div class="step-item passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-text">And I click button beside "Apa perbedaan rumah pendidikan dengan platform digital pendidikan milik Kemendikdasmen lainnya?"</span>
                        <span class="step-time">(I click button beside "Apa perbedaan rumah pendidikan dengan platform digital pendidikan milik Kemendikdasmen lainnya?" (0.23s))</span>
                    </div>
                    <div class="step-execution">Step 11 execution</div>
                    
                </div>
                
                <div class="step-item passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-text">Then I should see "Rumah Pendidikan berbeda dari platform digital pendidikan lain karena berfungsi sebagai portal utama yang mengintegrasikan berbagai layanan pendidikan dalam ekosistem Kemendikdasmen. Pengguna tidak hanya menemukan satu layanan, tetapi berbagai fitur yang mendukung ekosistem pendidikan secara komprehensif."</span>
                        <span class="step-time">(I should see "Rumah Pendidikan berbeda dari platform digital pendidikan lain karena berfungsi sebagai portal utama yang mengintegrasikan berbagai layanan pendidikan dalam ekosistem Kemendikdasmen. Pengguna tidak hanya menemukan satu layanan, tetapi berbagai fitur yang mendukung ekosistem pendidikan secara komprehensif." (6.24s))</span>
                    </div>
                    <div class="step-execution">Step 12 execution</div>
                    
                </div>
                
                <div class="step-item passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-text">And I click button "Ke Pusat Informasi"</span>
                        <span class="step-time">(I click button "Ke Pusat Informasi" (9.36s))</span>
                    </div>
                    <div class="step-execution">Step 13 execution</div>
                    
                </div>
                
                <div class="step-item passed">
                    <div class="step-header">
                        <span class="step-icon">✅</span>
                        <span class="step-text">Then user direct to page "https://pusatinformasi.rumahpendidikan.kemendikdasmen.go.id/hc/id"</span>
                        <span class="step-time">(user direct to page "https://pusatinformasi.rumahpendidikan.kemendikdasmen.go.id/hc/id" (0.00s))</span>
                    </div>
                    <div class="step-execution">Step 14 execution</div>
                    
                </div>
                
                    </div>
                </div>
            </div>
            
        </div>
        
        <div class="footer">
            <p>Maintained by Test Automation Team.</p>
        </div>
    </div>
</body>
</html>
        