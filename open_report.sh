#!/bin/bash

# Find the latest HTML report
LATEST_REPORT=$(find test_framework/reports -name "test_report_*.html" -type f | sort | tail -n 1)

if [ -z "$LATEST_REPORT" ]; then
    echo "❌ No HTML reports found in test_framework/reports/"
    echo "Run a test first with: ./run_gherkin.sh @TC1"
    exit 1
fi

echo "📊 Opening latest HTML report: $LATEST_REPORT"

# Open the report in the default browser
if command -v open &> /dev/null; then
    # macOS
    open "$LATEST_REPORT"
elif command -v xdg-open &> /dev/null; then
    # Linux
    xdg-open "$LATEST_REPORT"
elif command -v start &> /dev/null; then
    # Windows
    start "$LATEST_REPORT"
else
    echo "❌ Could not open browser automatically"
    echo "Please open manually: $LATEST_REPORT"
fi

echo "✅ Report opened in browser!" 